import React, { useRef, useEffect, useState } from 'react';
import { motion, useScroll, useTransform, useSpring, useInView } from 'framer-motion';
import { cn } from '@/lib/utils';

interface ScrollFadeProps {
  children: React.ReactNode;
  className?: string;
  threshold?: number;
  delay?: number;
  duration?: number;
  direction?: 'up' | 'down' | 'left' | 'right' | 'none';
  distance?: number;
  once?: boolean;
}

/**
 * Componente para animar elementos quando entram na viewport durante o scroll
 */
export function ScrollFade({
  children,
  className,
  threshold = 0.1,
  delay = 0,
  duration = 0.5,
  direction = 'up',
  distance = 50,
  once = true,
}: ScrollFadeProps) {
  const ref = useRef<HTMLDivElement>(null);
  const isInView = useInView(ref, { amount: threshold, once });

  // Configurar variantes de animação baseadas na direção
  const variants = {
    hidden: {
      opacity: 0,
      x: direction === 'left' ? distance : direction === 'right' ? -distance : 0,
      y: direction === 'up' ? distance : direction === 'down' ? -distance : 0,
    },
    visible: {
      opacity: 1,
      x: 0,
      y: 0,
      transition: {
        duration,
        delay,
        ease: [0.25, 0.1, 0.25, 1.0], // Curva de bezier para easing suave
      },
    },
  };

  return (
    <motion.div
      ref={ref}
      className={className}
      initial="hidden"
      animate={isInView ? 'visible' : 'hidden'}
      variants={variants}
    >
      {children}
    </motion.div>
  );
}

interface ParallaxProps {
  children: React.ReactNode;
  className?: string;
  speed?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
  containerClassName?: string;
}

/**
 * Componente para criar efeito de parallax durante o scroll
 */
export function Parallax({
  children,
  className,
  speed = 0.5,
  direction = 'up',
  containerClassName,
}: ParallaxProps) {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ['start end', 'end start'],
  });

  // Configurar transformação baseada na direção
  const springConfig = { stiffness: 100, damping: 30, restDelta: 0.001 };
  
  const getTransform = () => {
    switch (direction) {
      case 'up':
        return useTransform(scrollYProgress, [0, 1], ['0%', `${-speed * 100}%`]);
      case 'down':
        return useTransform(scrollYProgress, [0, 1], ['0%', `${speed * 100}%`]);
      case 'left':
        return useTransform(scrollYProgress, [0, 1], ['0%', `${-speed * 100}%`]);
      case 'right':
        return useTransform(scrollYProgress, [0, 1], ['0%', `${speed * 100}%`]);
      default:
        return useTransform(scrollYProgress, [0, 1], ['0%', '0%']);
    }
  };

  const transform = getTransform();
  const smoothTransform = useSpring(transform, springConfig);

  // Aplicar transformação baseada na direção
  const style = {
    ...(direction === 'up' || direction === 'down'
      ? { y: smoothTransform }
      : { x: smoothTransform }),
  };

  return (
    <div ref={ref} className={cn('overflow-hidden', containerClassName)}>
      <motion.div className={className} style={style}>
        {children}
      </motion.div>
    </div>
  );
}

interface ScrollProgressProps {
  children?: React.ReactNode;
  className?: string;
  height?: number;
  color?: string;
  position?: 'top' | 'bottom';
  zIndex?: number;
}

/**
 * Componente para mostrar o progresso de scroll na página
 */
export function ScrollProgress({
  children,
  className,
  height = 4,
  color = 'var(--primary)',
  position = 'top',
  zIndex = 50,
}: ScrollProgressProps) {
  const { scrollYProgress } = useScroll();
  const scaleX = useSpring(scrollYProgress, {
    stiffness: 100,
    damping: 30,
    restDelta: 0.001,
  });

  return (
    <motion.div
      className={cn('fixed left-0 right-0', className)}
      style={{
        top: position === 'top' ? 0 : 'auto',
        bottom: position === 'bottom' ? 0 : 'auto',
        height,
        backgroundColor: color,
        transformOrigin: 'left',
        scaleX,
        zIndex,
      }}
    >
      {children}
    </motion.div>
  );
}

interface ScrollRevealProps {
  children: React.ReactNode;
  className?: string;
  staggerChildren?: number;
  threshold?: number;
  once?: boolean;
}

/**
 * Componente para revelar elementos filhos em sequência durante o scroll
 */
export function ScrollReveal({
  children,
  className,
  staggerChildren = 0.1,
  threshold = 0.1,
  once = true,
}: ScrollRevealProps) {
  const ref = useRef<HTMLDivElement>(null);
  const isInView = useInView(ref, { amount: threshold, once });

  // Configurar variantes de animação
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: [0.25, 0.1, 0.25, 1.0],
      },
    },
  };

  // Clonar elementos filhos e adicionar variantes
  const childrenWithProps = React.Children.map(children, (child) => {
    if (React.isValidElement(child)) {
      return React.cloneElement(child as React.ReactElement, {
        variants: itemVariants,
      });
    }
    return child;
  });

  return (
    <motion.div
      ref={ref}
      className={className}
      initial="hidden"
      animate={isInView ? 'visible' : 'hidden'}
      variants={containerVariants}
    >
      {childrenWithProps}
    </motion.div>
  );
}
