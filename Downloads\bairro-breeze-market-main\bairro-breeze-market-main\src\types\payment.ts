/**
 * Tipos de métodos de pagamento suportados
 */
export type PaymentMethodType = 'credit_card' | 'debit_card' | 'pix' | 'cash';

/**
 * Status possíveis para um pagamento
 */
export type PaymentStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'refunded';

/**
 * Representa um método de pagamento
 */
export interface PaymentMethod {
  id: string;
  userId: string;
  type: PaymentMethodType;
  last4?: string;
  brand?: string;
  expiryMonth?: number;
  expiryYear?: number;
  holderName?: string;
  isDefault: boolean;
  createdAt: string;
  updatedAt?: string;
}

/**
 * Representa um pagamento
 */
export interface Payment {
  id: string;
  orderId: string;
  userId?: string;
  amount: number;
  status: PaymentStatus;
  method: PaymentMethodType;
  methodId?: string;
  transactionId?: string;
  receiptUrl?: string;
  createdAt: string;
  updatedAt?: string;
  metadata?: Record<string, any>;
}

/**
 * Dados necessários para processar um pagamento
 */
export interface ProcessPaymentRequest {
  orderId: string;
  amount: number;
  paymentMethodId: string;
  userId?: string;
  description?: string;
  metadata?: Record<string, any>;
}

/**
 * Dados necessários para adicionar um método de pagamento
 */
export interface AddPaymentMethodRequest {
  userId: string;
  type: PaymentMethodType;
  cardNumber?: string;
  cardExpiry?: string;
  cardCvc?: string;
  holderName?: string;
  setAsDefault?: boolean;
}

/**
 * Resposta de uma transação de pagamento
 */
export interface PaymentResponse {
  success: boolean;
  payment?: Payment;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
}
