import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				// Enhanced color system for Já Comprei
				eco: {
					DEFAULT: '#4CAF50', // Green - Sustainability
					light: '#F2FCE2',
					dark: '#388E3C',
					foreground: '#FFFFFF',
					50: '#F1F8E9',
					100: '#DCEDC8',
					200: '#C5E1A5',
					300: '#AED581',
					400: '#9CCC65',
					500: '#8BC34A',
					600: '#7CB342',
					700: '#689F38',
					800: '#558B2F',
					900: '#33691E',
				},
				cta: {
					DEFAULT: '#F97316', // Orange - Call to Action
					light: '#FEC6A1',
					dark: '#EA580C',
					foreground: '#FFFFFF',
					50: '#FFF3E0',
					100: '#FFE0B2',
					200: '#FFCC80',
					300: '#FFB74D',
					400: '#FFA726',
					500: '#FF9800',
					600: '#FB8C00',
					700: '#F57C00',
					800: '#EF6C00',
					900: '#E65100',
				},
				trust: {
					DEFAULT: '#0EA5E9', // Blue - Trust/Confidence
					light: '#BAE6FD',
					dark: '#0369A1',
					foreground: '#FFFFFF',
					50: '#E1F5FE',
					100: '#B3E5FC',
					200: '#81D4FA',
					300: '#4FC3F7',
					400: '#29B6F6',
					500: '#03A9F4',
					600: '#039BE5',
					700: '#0288D1',
					800: '#0277BD',
					900: '#01579B',
				},
				// New accent colors
				purple: {
					DEFAULT: '#9C27B0',
					light: '#F3E5F5',
					dark: '#7B1FA2',
					foreground: '#FFFFFF',
				},
				rose: {
					DEFAULT: '#E91E63',
					light: '#FCE4EC',
					dark: '#C2185B',
					foreground: '#FFFFFF',
				},
				sidebar: {
					DEFAULT: 'hsl(var(--sidebar-background))',
					foreground: 'hsl(var(--sidebar-foreground))',
					primary: 'hsl(var(--sidebar-primary))',
					'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
					accent: 'hsl(var(--sidebar-accent))',
					'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
					border: 'hsl(var(--sidebar-border))',
					ring: 'hsl(var(--sidebar-ring))'
				}
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			keyframes: {
				'accordion-down': {
					from: {
						height: '0'
					},
					to: {
						height: 'var(--radix-accordion-content-height)'
					}
				},
				'accordion-up': {
					from: {
						height: 'var(--radix-accordion-content-height)'
					},
					to: {
						height: '0'
					}
				}
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out'
			}
		}
	},
	plugins: [require("tailwindcss-animate")],
} satisfies Config;
