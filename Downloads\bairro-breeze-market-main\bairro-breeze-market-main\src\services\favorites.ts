import { supabaseClient } from '@/lib/supabase';
import { Product } from '@/types/product';
import { Shop } from './shops';
import { fetchProductById } from './products';
import { fetchShopById } from './shops';

// Favorite interface
export interface Favorite {
  id: string;
  userId: string;
  type: 'product' | 'shop';
  itemId: string;
  createdAt: string;
}

// Mock favorites for development mode
let mockFavorites: Favorite[] = [
  {
    id: '1',
    userId: 'user1',
    type: 'product',
    itemId: '1',
    createdAt: '2024-04-18T10:00:00Z'
  },
  {
    id: '2',
    userId: 'user1',
    type: 'shop',
    itemId: '2',
    createdAt: '2024-04-18T11:00:00Z'
  }
];

/**
 * Get favorite products for a user
 * @param userId User ID
 * @returns Promise with favorite products
 */
export const getFavoriteProducts = async (userId: string): Promise<Product[]> => {
  try {
    // In a real app, this would fetch from a database
    
    // For now, use mock data
    const favoriteProductIds = mockFavorites
      .filter(fav => fav.userId === userId && fav.type === 'product')
      .map(fav => fav.itemId);
    
    // Fetch products by IDs
    const products = await Promise.all(
      favoriteProductIds.map(id => fetchProductById(id))
    );
    
    return products.filter(Boolean) as Product[];
  } catch (error) {
    console.error(`Error fetching favorite products for user ${userId}:`, error);
    return [];
  }
};

/**
 * Get favorite shops for a user
 * @param userId User ID
 * @returns Promise with favorite shops
 */
export const getFavoriteShops = async (userId: string): Promise<Shop[]> => {
  try {
    // In a real app, this would fetch from a database
    
    // For now, use mock data
    const favoriteShopIds = mockFavorites
      .filter(fav => fav.userId === userId && fav.type === 'shop')
      .map(fav => fav.itemId);
    
    // Fetch shops by IDs
    const shops = await Promise.all(
      favoriteShopIds.map(id => fetchShopById(id))
    );
    
    return shops.filter(Boolean) as Shop[];
  } catch (error) {
    console.error(`Error fetching favorite shops for user ${userId}:`, error);
    return [];
  }
};

/**
 * Check if an item is favorited
 * @param userId User ID
 * @param itemId Item ID
 * @param type Item type
 * @returns Promise with favorite status
 */
export const isFavorite = async (
  userId: string,
  itemId: string,
  type: 'product' | 'shop'
): Promise<boolean> => {
  try {
    // In a real app, this would fetch from a database
    
    // For now, use mock data
    return mockFavorites.some(
      fav => fav.userId === userId && fav.itemId === itemId && fav.type === type
    );
  } catch (error) {
    console.error(`Error checking favorite status for ${type} ${itemId}:`, error);
    return false;
  }
};

/**
 * Add an item to favorites
 * @param userId User ID
 * @param itemId Item ID
 * @param type Item type
 * @returns Promise with success status
 */
export const addFavorite = async (
  userId: string,
  itemId: string,
  type: 'product' | 'shop'
): Promise<boolean> => {
  try {
    // In a real app, this would insert into the database
    
    // For now, update mock data
    // Check if already favorited
    if (await isFavorite(userId, itemId, type)) {
      return true;
    }
    
    // Add to favorites
    mockFavorites.push({
      id: `${mockFavorites.length + 1}`,
      userId,
      type,
      itemId,
      createdAt: new Date().toISOString()
    });
    
    return true;
  } catch (error) {
    console.error(`Error adding ${type} ${itemId} to favorites:`, error);
    return false;
  }
};

/**
 * Remove an item from favorites
 * @param userId User ID
 * @param itemId Item ID
 * @param type Item type
 * @returns Promise with success status
 */
export const removeFavorite = async (
  userId: string,
  itemId: string,
  type: 'product' | 'shop'
): Promise<boolean> => {
  try {
    // In a real app, this would delete from the database
    
    // For now, update mock data
    const index = mockFavorites.findIndex(
      fav => fav.userId === userId && fav.itemId === itemId && fav.type === type
    );
    
    if (index >= 0) {
      mockFavorites.splice(index, 1);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error removing ${type} ${itemId} from favorites:`, error);
    return false;
  }
};

/**
 * Toggle favorite status
 * @param userId User ID
 * @param itemId Item ID
 * @param type Item type
 * @returns Promise with new favorite status
 */
export const toggleFavorite = async (
  userId: string,
  itemId: string,
  type: 'product' | 'shop'
): Promise<boolean> => {
  try {
    const isFav = await isFavorite(userId, itemId, type);
    
    if (isFav) {
      await removeFavorite(userId, itemId, type);
      return false;
    } else {
      await addFavorite(userId, itemId, type);
      return true;
    }
  } catch (error) {
    console.error(`Error toggling favorite status for ${type} ${itemId}:`, error);
    return false;
  }
};
