import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { 
  Calendar, 
  CalendarIcon,
  Plus, 
  Search, 
  Edit, 
  Trash2, 
  MoreVertical, 
  Tag, 
  ArrowUpDown,
  Percent
} from "lucide-react";
import { Product } from "@/types/product";
import { toast } from "@/hooks/use-toast";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { fetchProducts } from "@/services/products";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";

interface Promotion {
  id: string;
  name: string;
  discount: number;
  startDate: Date;
  endDate: Date;
  products: string[];
  active: boolean;
}

export function PromotionManagement() {
  const [isAddPromoOpen, setIsAddPromoOpen] = useState(false);
  const [isEditPromoOpen, setIsEditPromoOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState<"name" | "discount" | "endDate">("name");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [selectedPromotion, setSelectedPromotion] = useState<Promotion | null>(null);
  const [newPromotion, setNewPromotion] = useState<Partial<Promotion>>({
    name: "",
    discount: 10,
    startDate: new Date(),
    endDate: new Date(new Date().setDate(new Date().getDate() + 7)),
    products: [],
    active: true
  });

  // Mock promotions data
  const [promotions, setPromotions] = useState<Promotion[]>([
    {
      id: "1",
      name: "Semana de Frutas",
      discount: 20,
      startDate: new Date(2024, 3, 15),
      endDate: new Date(2024, 3, 22),
      products: ["1", "8"],
      active: true
    },
    {
      id: "2",
      name: "Especial de Padaria",
      discount: 15,
      startDate: new Date(2024, 3, 10),
      endDate: new Date(2024, 3, 20),
      products: ["3", "4", "7"],
      active: true
    },
    {
      id: "3",
      name: "Carnes Premium",
      discount: 10,
      startDate: new Date(2024, 3, 5),
      endDate: new Date(2024, 3, 12),
      products: ["2", "6"],
      active: false
    }
  ]);

  const queryClient = useQueryClient();
  
  // Fetch products
  const { data: products = [], isLoading: isLoadingProducts } = useQuery({
    queryKey: ["products"],
    queryFn: fetchProducts,
  });

  // Filter promotions by search term
  const filteredPromotions = promotions.filter(promo => 
    promo.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Sort promotions
  const sortedPromotions = [...filteredPromotions].sort((a, b) => {
    if (sortBy === "name") {
      return sortOrder === "asc" 
        ? a.name.localeCompare(b.name) 
        : b.name.localeCompare(a.name);
    } else if (sortBy === "discount") {
      return sortOrder === "asc" 
        ? a.discount - b.discount 
        : b.discount - a.discount;
    } else if (sortBy === "endDate") {
      return sortOrder === "asc" 
        ? a.endDate.getTime() - b.endDate.getTime() 
        : b.endDate.getTime() - a.endDate.getTime();
    }
    return 0;
  });

  const handleAddPromotion = () => {
    const newPromotionWithId = {
      ...newPromotion,
      id: `${promotions.length + 1}`,
    } as Promotion;
    
    setPromotions([...promotions, newPromotionWithId]);
    
    toast({
      title: "Promoção adicionada",
      description: `${newPromotion.name} foi adicionada com sucesso.`,
    });
    
    setNewPromotion({
      name: "",
      discount: 10,
      startDate: new Date(),
      endDate: new Date(new Date().setDate(new Date().getDate() + 7)),
      products: [],
      active: true
    });
    
    setIsAddPromoOpen(false);
  };

  const handleEditPromotion = () => {
    if (!selectedPromotion) return;
    
    const updatedPromotions = promotions.map(promo => 
      promo.id === selectedPromotion.id ? selectedPromotion : promo
    );
    
    setPromotions(updatedPromotions);
    
    toast({
      title: "Promoção atualizada",
      description: `${selectedPromotion.name} foi atualizada com sucesso.`,
    });
    
    setIsEditPromoOpen(false);
  };

  const handleDeletePromotion = () => {
    if (!selectedPromotion) return;
    
    const updatedPromotions = promotions.filter(promo => 
      promo.id !== selectedPromotion.id
    );
    
    setPromotions(updatedPromotions);
    
    toast({
      title: "Promoção removida",
      description: `${selectedPromotion.name} foi removida com sucesso.`,
    });
    
    setIsDeleteDialogOpen(false);
  };

  const toggleSort = (column: "name" | "discount" | "endDate") => {
    if (sortBy === column) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(column);
      setSortOrder("asc");
    }
  };

  const formatDate = (date: Date) => {
    return format(date, "dd/MM/yyyy", { locale: ptBR });
  };

  const getProductsForPromotion = (productIds: string[]) => {
    return products
      .filter(product => productIds.includes(product.id))
      .map(product => product.name)
      .join(", ");
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="relative w-full sm:w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Buscar promoções..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <Button 
          className="bg-cta hover:bg-cta-dark w-full sm:w-auto"
          onClick={() => setIsAddPromoOpen(true)}
        >
          <Plus className="mr-2 h-4 w-4" />
          Nova Promoção
        </Button>
      </div>

      {isLoadingProducts ? (
        <div className="flex justify-center items-center py-12">
          <div className="h-6 w-6 animate-spin rounded-full border-2 border-cta border-t-transparent" />
        </div>
      ) : (
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>
                  <button 
                    className="flex items-center"
                    onClick={() => toggleSort("name")}
                  >
                    Nome
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                  </button>
                </TableHead>
                <TableHead>
                  <button 
                    className="flex items-center"
                    onClick={() => toggleSort("discount")}
                  >
                    Desconto
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                  </button>
                </TableHead>
                <TableHead>Período</TableHead>
                <TableHead>Produtos</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Ações</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedPromotions.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                    Nenhuma promoção encontrada
                  </TableCell>
                </TableRow>
              ) : (
                sortedPromotions.map((promo) => (
                  <TableRow key={promo.id}>
                    <TableCell className="font-medium">{promo.name}</TableCell>
                    <TableCell>
                      <div className="flex items-center">
                        <Percent className="h-4 w-4 mr-1 text-cta" />
                        <span>{promo.discount}%</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div>{formatDate(promo.startDate)}</div>
                        <div>até</div>
                        <div>{formatDate(promo.endDate)}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="max-w-[200px] truncate">
                        {getProductsForPromotion(promo.products)}
                      </div>
                    </TableCell>
                    <TableCell>
                      {promo.active ? (
                        <div className="flex items-center">
                          <div className="h-2 w-2 rounded-full bg-green-500 mr-2" />
                          <span className="text-xs font-medium">Ativa</span>
                        </div>
                      ) : (
                        <div className="flex items-center">
                          <div className="h-2 w-2 rounded-full bg-gray-400 mr-2" />
                          <span className="text-xs font-medium">Inativa</span>
                        </div>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem
                            onClick={() => {
                              setSelectedPromotion(promo);
                              setIsEditPromoOpen(true);
                            }}
                          >
                            <Edit className="mr-2 h-4 w-4" />
                            Editar
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => {
                              setSelectedPromotion(promo);
                              setIsDeleteDialogOpen(true);
                            }}
                            className="text-destructive"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Excluir
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      )}

      {/* Add Promotion Dialog */}
      <Dialog open={isAddPromoOpen} onOpenChange={setIsAddPromoOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Adicionar Nova Promoção</DialogTitle>
            <DialogDescription>
              Preencha os detalhes da promoção abaixo.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Nome da Promoção</Label>
              <Input
                id="name"
                value={newPromotion.name}
                onChange={(e) => setNewPromotion({ ...newPromotion, name: e.target.value })}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="discount">Desconto (%)</Label>
              <Input
                id="discount"
                type="number"
                min="1"
                max="99"
                value={newPromotion.discount}
                onChange={(e) => setNewPromotion({ ...newPromotion, discount: parseInt(e.target.value) })}
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label>Data de Início</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !newPromotion.startDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {newPromotion.startDate ? (
                        format(newPromotion.startDate, "dd/MM/yyyy")
                      ) : (
                        <span>Selecione a data</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <CalendarComponent
                      mode="single"
                      selected={newPromotion.startDate}
                      onSelect={(date) => date && setNewPromotion({ ...newPromotion, startDate: date })}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="grid gap-2">
                <Label>Data de Término</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !newPromotion.endDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {newPromotion.endDate ? (
                        format(newPromotion.endDate, "dd/MM/yyyy")
                      ) : (
                        <span>Selecione a data</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <CalendarComponent
                      mode="single"
                      selected={newPromotion.endDate}
                      onSelect={(date) => date && setNewPromotion({ ...newPromotion, endDate: date })}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="products">Produtos</Label>
              <Select
                value={newPromotion.products?.length ? "selected" : ""}
                onValueChange={(value) => {
                  if (value === "all") {
                    setNewPromotion({ 
                      ...newPromotion, 
                      products: products.map(p => p.id) 
                    });
                  }
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione os produtos" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Nenhum produto</SelectItem>
                  <SelectItem value="all">Todos os produtos</SelectItem>
                  <SelectItem value="selected">
                    {newPromotion.products?.length 
                      ? `${newPromotion.products.length} produtos selecionados` 
                      : "Produtos selecionados"}
                  </SelectItem>
                </SelectContent>
              </Select>
              <div className="mt-2 space-y-2 max-h-[200px] overflow-y-auto">
                {products.map(product => (
                  <div key={product.id} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={`product-${product.id}`}
                      checked={newPromotion.products?.includes(product.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setNewPromotion({
                            ...newPromotion,
                            products: [...(newPromotion.products || []), product.id]
                          });
                        } else {
                          setNewPromotion({
                            ...newPromotion,
                            products: newPromotion.products?.filter(id => id !== product.id)
                          });
                        }
                      }}
                      className="rounded border-gray-300 text-cta focus:ring-cta"
                    />
                    <label htmlFor={`product-${product.id}`} className="text-sm">
                      {product.name}
                    </label>
                  </div>
                ))}
              </div>
            </div>
            <div className="grid gap-2">
              <Label htmlFor="active">Status</Label>
              <Select
                value={newPromotion.active ? "active" : "inactive"}
                onValueChange={(value) => setNewPromotion({ 
                  ...newPromotion, 
                  active: value === "active" 
                })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Ativa</SelectItem>
                  <SelectItem value="inactive">Inativa</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddPromoOpen(false)}>
              Cancelar
            </Button>
            <Button 
              className="bg-cta hover:bg-cta-dark"
              onClick={handleAddPromotion}
            >
              Adicionar Promoção
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Promotion Dialog */}
      <Dialog open={isEditPromoOpen} onOpenChange={setIsEditPromoOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Editar Promoção</DialogTitle>
            <DialogDescription>
              Atualize os detalhes da promoção abaixo.
            </DialogDescription>
          </DialogHeader>
          {selectedPromotion && (
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="edit-name">Nome da Promoção</Label>
                <Input
                  id="edit-name"
                  value={selectedPromotion.name}
                  onChange={(e) => setSelectedPromotion({ 
                    ...selectedPromotion, 
                    name: e.target.value 
                  })}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-discount">Desconto (%)</Label>
                <Input
                  id="edit-discount"
                  type="number"
                  min="1"
                  max="99"
                  value={selectedPromotion.discount}
                  onChange={(e) => setSelectedPromotion({ 
                    ...selectedPromotion, 
                    discount: parseInt(e.target.value) 
                  })}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="grid gap-2">
                  <Label>Data de Início</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full justify-start text-left font-normal"
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {format(selectedPromotion.startDate, "dd/MM/yyyy")}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <CalendarComponent
                        mode="single"
                        selected={selectedPromotion.startDate}
                        onSelect={(date) => date && setSelectedPromotion({ 
                          ...selectedPromotion, 
                          startDate: date 
                        })}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
                <div className="grid gap-2">
                  <Label>Data de Término</Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full justify-start text-left font-normal"
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {format(selectedPromotion.endDate, "dd/MM/yyyy")}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <CalendarComponent
                        mode="single"
                        selected={selectedPromotion.endDate}
                        onSelect={(date) => date && setSelectedPromotion({ 
                          ...selectedPromotion, 
                          endDate: date 
                        })}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-products">Produtos</Label>
                <Select
                  value={selectedPromotion.products?.length ? "selected" : ""}
                  onValueChange={(value) => {
                    if (value === "all") {
                      setSelectedPromotion({ 
                        ...selectedPromotion, 
                        products: products.map(p => p.id) 
                      });
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione os produtos" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">Nenhum produto</SelectItem>
                    <SelectItem value="all">Todos os produtos</SelectItem>
                    <SelectItem value="selected">
                      {selectedPromotion.products?.length 
                        ? `${selectedPromotion.products.length} produtos selecionados` 
                        : "Produtos selecionados"}
                    </SelectItem>
                  </SelectContent>
                </Select>
                <div className="mt-2 space-y-2 max-h-[200px] overflow-y-auto">
                  {products.map(product => (
                    <div key={product.id} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id={`edit-product-${product.id}`}
                        checked={selectedPromotion.products?.includes(product.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedPromotion({
                              ...selectedPromotion,
                              products: [...(selectedPromotion.products || []), product.id]
                            });
                          } else {
                            setSelectedPromotion({
                              ...selectedPromotion,
                              products: selectedPromotion.products?.filter(id => id !== product.id)
                            });
                          }
                        }}
                        className="rounded border-gray-300 text-cta focus:ring-cta"
                      />
                      <label htmlFor={`edit-product-${product.id}`} className="text-sm">
                        {product.name}
                      </label>
                    </div>
                  ))}
                </div>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="edit-active">Status</Label>
                <Select
                  value={selectedPromotion.active ? "active" : "inactive"}
                  onValueChange={(value) => setSelectedPromotion({ 
                    ...selectedPromotion, 
                    active: value === "active" 
                  })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Ativa</SelectItem>
                    <SelectItem value="inactive">Inativa</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditPromoOpen(false)}>
              Cancelar
            </Button>
            <Button 
              className="bg-cta hover:bg-cta-dark"
              onClick={handleEditPromotion}
            >
              Salvar Alterações
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Confirmar Exclusão</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja excluir esta promoção? Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancelar
            </Button>
            <Button 
              variant="destructive"
              onClick={handleDeletePromotion}
            >
              Excluir
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
