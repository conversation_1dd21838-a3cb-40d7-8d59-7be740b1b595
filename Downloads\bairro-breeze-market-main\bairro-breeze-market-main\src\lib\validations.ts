import { z } from 'zod';

/**
 * Validação para endereço
 */
export const addressSchema = z.object({
  name: z.string().min(1, 'Nome é obrigatório'),
  street: z.string().min(3, 'Rua deve ter pelo menos 3 caracteres'),
  number: z.string().min(1, 'Número é obrigatório'),
  complement: z.string().optional(),
  neighborhood: z.string().min(2, 'Bairro é obrigatório'),
  city: z.string().min(2, 'Cidade é obrigatória'),
  state: z.string().length(2, 'Estado deve ter 2 caracteres'),
  zipCode: z.string().regex(/^\d{5}-\d{3}$/, 'CEP deve estar no formato 00000-000'),
});

export type AddressFormValues = z.infer<typeof addressSchema>;

/**
 * Validação para cartão de crédito
 */
export const creditCardSchema = z.object({
  cardNumber: z
    .string()
    .min(13, 'Número do cartão deve ter pelo menos 13 dígitos')
    .max(19, 'Número do cartão deve ter no máximo 19 dígitos')
    .regex(/^[0-9\s]+$/, 'Número do cartão deve conter apenas números'),
  cardName: z.string().min(3, 'Nome no cartão é obrigatório'),
  expiryMonth: z
    .string()
    .regex(/^(0[1-9]|1[0-2])$/, 'Mês deve estar entre 01 e 12'),
  expiryYear: z
    .string()
    .regex(/^(2[3-9]|[3-9][0-9])$/, 'Ano deve ser maior que o atual'),
  cvv: z
    .string()
    .regex(/^\d{3,4}$/, 'CVV deve ter 3 ou 4 dígitos'),
  saveCard: z.boolean().optional(),
});

export type CreditCardFormValues = z.infer<typeof creditCardSchema>;

/**
 * Validação para login
 */
export const loginSchema = z.object({
  email: z.string().email('Email inválido'),
  password: z.string().min(6, 'Senha deve ter pelo menos 6 caracteres'),
});

export type LoginFormValues = z.infer<typeof loginSchema>;

/**
 * Validação para registro
 */
export const registerSchema = z.object({
  name: z.string().min(3, 'Nome deve ter pelo menos 3 caracteres'),
  email: z.string().email('Email inválido'),
  password: z
    .string()
    .min(8, 'Senha deve ter pelo menos 8 caracteres')
    .regex(/[A-Z]/, 'Senha deve conter pelo menos uma letra maiúscula')
    .regex(/[0-9]/, 'Senha deve conter pelo menos um número'),
  confirmPassword: z.string(),
  role: z.enum(['customer', 'merchant', 'deliverer']),
  terms: z.boolean().refine(val => val === true, {
    message: 'Você deve aceitar os termos e condições',
  }),
}).refine(data => data.password === data.confirmPassword, {
  message: 'Senhas não conferem',
  path: ['confirmPassword'],
});

export type RegisterFormValues = z.infer<typeof registerSchema>;

/**
 * Validação para produto
 */
export const productSchema = z.object({
  name: z.string().min(3, 'Nome deve ter pelo menos 3 caracteres'),
  description: z.string().optional(),
  price: z.number().positive('Preço deve ser maior que zero'),
  originalPrice: z.number().positive('Preço original deve ser maior que zero').optional(),
  category: z.string().min(1, 'Categoria é obrigatória'),
  stock: z.number().int('Estoque deve ser um número inteiro').min(0, 'Estoque não pode ser negativo').optional(),
  isPromo: z.boolean().optional(),
});

export type ProductFormValues = z.infer<typeof productSchema>;

/**
 * Validação para avaliação
 */
export const reviewSchema = z.object({
  rating: z.number().min(1, 'Avaliação é obrigatória').max(5, 'Avaliação deve ser no máximo 5'),
  comment: z.string().optional(),
});

export type ReviewFormValues = z.infer<typeof reviewSchema>;

/**
 * Validação para busca
 */
export const searchSchema = z.object({
  query: z.string().min(2, 'Busca deve ter pelo menos 2 caracteres'),
  category: z.string().optional(),
  minPrice: z.number().optional(),
  maxPrice: z.number().optional(),
});

export type SearchFormValues = z.infer<typeof searchSchema>;

/**
 * Validação para CEP
 */
export const zipCodeSchema = z.string().regex(/^\d{5}-\d{3}$/, 'CEP deve estar no formato 00000-000');

/**
 * Validação para número de cartão de crédito
 */
export const creditCardNumberSchema = z
  .string()
  .min(13, 'Número do cartão deve ter pelo menos 13 dígitos')
  .max(19, 'Número do cartão deve ter no máximo 19 dígitos')
  .regex(/^[0-9\s]+$/, 'Número do cartão deve conter apenas números');

/**
 * Validação para email
 */
export const emailSchema = z.string().email('Email inválido');

/**
 * Validação para senha
 */
export const passwordSchema = z
  .string()
  .min(8, 'Senha deve ter pelo menos 8 caracteres')
  .regex(/[A-Z]/, 'Senha deve conter pelo menos uma letra maiúscula')
  .regex(/[0-9]/, 'Senha deve conter pelo menos um número');

/**
 * Validação para telefone
 */
export const phoneSchema = z.string().regex(/^\(\d{2}\) \d{5}-\d{4}$/, 'Telefone deve estar no formato (00) 00000-0000');

/**
 * Validação para CPF
 */
export const cpfSchema = z.string().regex(/^\d{3}\.\d{3}\.\d{3}-\d{2}$/, 'CPF deve estar no formato 000.000.000-00');

/**
 * Validação para CNPJ
 */
export const cnpjSchema = z.string().regex(/^\d{2}\.\d{3}\.\d{3}\/\d{4}-\d{2}$/, 'CNPJ deve estar no formato 00.000.000/0000-00');

/**
 * Validação para data
 */
export const dateSchema = z.string().regex(/^\d{2}\/\d{2}\/\d{4}$/, 'Data deve estar no formato DD/MM/AAAA');

/**
 * Validação para hora
 */
export const timeSchema = z.string().regex(/^\d{2}:\d{2}$/, 'Hora deve estar no formato HH:MM');

/**
 * Validação para URL
 */
export const urlSchema = z.string().url('URL inválida');

/**
 * Validação para cor hexadecimal
 */
export const hexColorSchema = z.string().regex(/^#[0-9A-F]{6}$/i, 'Cor deve estar no formato hexadecimal #RRGGBB');

/**
 * Validação para número inteiro
 */
export const integerSchema = z.number().int('Deve ser um número inteiro');

/**
 * Validação para número positivo
 */
export const positiveNumberSchema = z.number().positive('Deve ser um número positivo');

/**
 * Validação para número não negativo
 */
export const nonNegativeNumberSchema = z.number().min(0, 'Não pode ser negativo');

/**
 * Validação para porcentagem
 */
export const percentageSchema = z.number().min(0, 'Porcentagem não pode ser negativa').max(100, 'Porcentagem não pode ser maior que 100');

/**
 * Validação para nome de usuário
 */
export const usernameSchema = z
  .string()
  .min(3, 'Nome de usuário deve ter pelo menos 3 caracteres')
  .max(20, 'Nome de usuário deve ter no máximo 20 caracteres')
  .regex(/^[a-zA-Z0-9_]+$/, 'Nome de usuário deve conter apenas letras, números e underscore');

/**
 * Validação para nome completo
 */
export const fullNameSchema = z
  .string()
  .min(3, 'Nome deve ter pelo menos 3 caracteres')
  .regex(/^[a-zA-ZÀ-ÖØ-öø-ÿ\s]+$/, 'Nome deve conter apenas letras');

/**
 * Validação para texto curto
 */
export const shortTextSchema = z.string().min(1, 'Campo obrigatório').max(100, 'Texto muito longo');

/**
 * Validação para texto longo
 */
export const longTextSchema = z.string().min(1, 'Campo obrigatório').max(1000, 'Texto muito longo');

/**
 * Validação para código de rastreamento
 */
export const trackingCodeSchema = z.string().regex(/^[A-Z0-9]{10,13}$/, 'Código de rastreamento inválido');

/**
 * Validação para código de cupom
 */
export const couponCodeSchema = z.string().regex(/^[A-Z0-9]{4,10}$/, 'Código de cupom inválido');

/**
 * Validação para código PIX
 */
export const pixCodeSchema = z.string().min(10, 'Código PIX inválido');

/**
 * Formata um número de cartão de crédito
 * @param value Número do cartão
 * @returns Número formatado
 */
export const formatCreditCardNumber = (value: string): string => {
  if (!value) return '';
  
  // Remove todos os espaços
  const v = value.replace(/\s+/g, '');
  
  // Adiciona espaço a cada 4 caracteres
  const matches = v.match(/\d{4,16}/g);
  const match = matches && matches[0] || '';
  const parts = [];
  
  for (let i = 0, len = match.length; i < len; i += 4) {
    parts.push(match.substring(i, i + 4));
  }
  
  if (parts.length) {
    return parts.join(' ');
  } else {
    return value;
  }
};

/**
 * Formata um CEP
 * @param value CEP
 * @returns CEP formatado
 */
export const formatZipCode = (value: string): string => {
  if (!value) return '';
  
  // Remove todos os caracteres não numéricos
  const v = value.replace(/\D/g, '');
  
  // Formata como 00000-000
  if (v.length <= 5) {
    return v;
  } else {
    return `${v.substring(0, 5)}-${v.substring(5, 8)}`;
  }
};

/**
 * Formata um telefone
 * @param value Telefone
 * @returns Telefone formatado
 */
export const formatPhone = (value: string): string => {
  if (!value) return '';
  
  // Remove todos os caracteres não numéricos
  const v = value.replace(/\D/g, '');
  
  // Formata como (00) 00000-0000
  if (v.length <= 2) {
    return `(${v}`;
  } else if (v.length <= 7) {
    return `(${v.substring(0, 2)}) ${v.substring(2)}`;
  } else {
    return `(${v.substring(0, 2)}) ${v.substring(2, 7)}-${v.substring(7, 11)}`;
  }
};

/**
 * Formata um CPF
 * @param value CPF
 * @returns CPF formatado
 */
export const formatCPF = (value: string): string => {
  if (!value) return '';
  
  // Remove todos os caracteres não numéricos
  const v = value.replace(/\D/g, '');
  
  // Formata como 000.000.000-00
  if (v.length <= 3) {
    return v;
  } else if (v.length <= 6) {
    return `${v.substring(0, 3)}.${v.substring(3)}`;
  } else if (v.length <= 9) {
    return `${v.substring(0, 3)}.${v.substring(3, 6)}.${v.substring(6)}`;
  } else {
    return `${v.substring(0, 3)}.${v.substring(3, 6)}.${v.substring(6, 9)}-${v.substring(9, 11)}`;
  }
};

/**
 * Formata um CNPJ
 * @param value CNPJ
 * @returns CNPJ formatado
 */
export const formatCNPJ = (value: string): string => {
  if (!value) return '';
  
  // Remove todos os caracteres não numéricos
  const v = value.replace(/\D/g, '');
  
  // Formata como 00.000.000/0000-00
  if (v.length <= 2) {
    return v;
  } else if (v.length <= 5) {
    return `${v.substring(0, 2)}.${v.substring(2)}`;
  } else if (v.length <= 8) {
    return `${v.substring(0, 2)}.${v.substring(2, 5)}.${v.substring(5)}`;
  } else if (v.length <= 12) {
    return `${v.substring(0, 2)}.${v.substring(2, 5)}.${v.substring(5, 8)}/${v.substring(8)}`;
  } else {
    return `${v.substring(0, 2)}.${v.substring(2, 5)}.${v.substring(5, 8)}/${v.substring(8, 12)}-${v.substring(12, 14)}`;
  }
};

/**
 * Formata uma data
 * @param value Data
 * @returns Data formatada
 */
export const formatDate = (value: string): string => {
  if (!value) return '';
  
  // Remove todos os caracteres não numéricos
  const v = value.replace(/\D/g, '');
  
  // Formata como DD/MM/AAAA
  if (v.length <= 2) {
    return v;
  } else if (v.length <= 4) {
    return `${v.substring(0, 2)}/${v.substring(2)}`;
  } else {
    return `${v.substring(0, 2)}/${v.substring(2, 4)}/${v.substring(4, 8)}`;
  }
};

/**
 * Formata uma hora
 * @param value Hora
 * @returns Hora formatada
 */
export const formatTime = (value: string): string => {
  if (!value) return '';
  
  // Remove todos os caracteres não numéricos
  const v = value.replace(/\D/g, '');
  
  // Formata como HH:MM
  if (v.length <= 2) {
    return v;
  } else {
    return `${v.substring(0, 2)}:${v.substring(2, 4)}`;
  }
};

/**
 * Formata um valor monetário
 * @param value Valor
 * @returns Valor formatado
 */
export const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value);
};

/**
 * Formata um número com separador de milhares
 * @param value Número
 * @returns Número formatado
 */
export const formatNumber = (value: number): string => {
  return new Intl.NumberFormat('pt-BR').format(value);
};

/**
 * Formata uma porcentagem
 * @param value Porcentagem
 * @returns Porcentagem formatada
 */
export const formatPercentage = (value: number): string => {
  return `${value}%`;
};

/**
 * Formata um tamanho de arquivo
 * @param bytes Tamanho em bytes
 * @returns Tamanho formatado
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
};

/**
 * Formata uma distância
 * @param meters Distância em metros
 * @returns Distância formatada
 */
export const formatDistance = (meters: number): string => {
  if (meters < 1000) {
    return `${meters} m`;
  } else {
    return `${(meters / 1000).toFixed(1)} km`;
  }
};

/**
 * Formata um tempo em segundos
 * @param seconds Tempo em segundos
 * @returns Tempo formatado
 */
export const formatDuration = (seconds: number): string => {
  if (seconds < 60) {
    return `${seconds} seg`;
  } else if (seconds < 3600) {
    const minutes = Math.floor(seconds / 60);
    return `${minutes} min`;
  } else {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}h ${minutes}min`;
  }
};
