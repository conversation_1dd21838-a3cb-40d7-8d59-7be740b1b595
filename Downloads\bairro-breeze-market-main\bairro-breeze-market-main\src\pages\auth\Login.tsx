
import { useState, useEffect } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useLocation, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { ShoppingBag, Facebook, Mail, Store, Truck, User } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";

const Login = () => {
  const { signIn, signUp } = useAuth();
  const { toast } = useToast();
  const location = useLocation();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [showRoleDialog, setShowRoleDialog] = useState(false);
  const [selectedRole, setSelectedRole] = useState<'customer' | 'merchant' | 'deliverer'>('customer');

  // Estado do formulário
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    name: "",
    phone: "",
    role: "customer",
  });

  // Verificar se há dados de estado da navegação
  useEffect(() => {
    if (location.state) {
      const { email, role } = location.state as { email?: string; role?: 'customer' | 'merchant' | 'deliverer' };
      if (email) {
        setFormData(prev => ({ ...prev, email }));
      }
      if (role) {
        setFormData(prev => ({ ...prev, role }));
        setSelectedRole(role);
      }
    }
  }, [location.state]);

  // Verificar se estamos em modo de desenvolvimento
  const isDevelopmentMode = process.env.NODE_ENV === 'development' && !import.meta.env.VITE_SUPABASE_URL;

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    // No modo de desenvolvimento, mostrar diálogo de seleção de perfil
    if (isDevelopmentMode) {
      setShowRoleDialog(true);
      return;
    }

    // Em produção, fazer login normal
    await processLogin();
  };

  const processLogin = async () => {
    setIsLoading(true);
    try {
      // Passar o perfil selecionado apenas no modo de desenvolvimento
      await signIn(
        formData.email,
        formData.password,
        isDevelopmentMode ? selectedRole : undefined
      );

      toast({
        title: "Login realizado com sucesso!",
        description: "Bem-vindo de volta.",
      });
    } catch (error) {
      toast({
        title: "Erro ao fazer login",
        description: "Verifique suas credenciais e tente novamente.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
      setShowRoleDialog(false);
    }
  };

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      // Usar o perfil selecionado para registro
      await signUp(formData.email, formData.password, formData.name, formData.role);
      toast({
        title: "Conta criada com sucesso!",
        description: isDevelopmentMode
          ? "Você já pode fazer login."
          : "Verifique seu email para confirmar o cadastro.",
      });

      // No modo de desenvolvimento, redirecionar para login com o email preenchido
      if (isDevelopmentMode) {
        setFormData(prev => ({ ...prev, password: "" }));
      }
    } catch (error) {
      toast({
        title: "Erro ao criar conta",
        description: "Verifique os dados e tente novamente.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center p-4">
      <div className="w-full max-w-md">
        <div className="flex justify-center mb-6">
          <div className="flex items-center">
            <div className="bg-eco-light p-3 rounded-full">
              <ShoppingBag className="h-6 w-6 text-eco" />
            </div>
            <div className="ml-2">
              <span className="text-2xl font-bold text-trust-dark">Já</span>
              <span className="text-2xl font-bold text-cta">Comprei</span>
            </div>
          </div>
        </div>

        <div className="bg-white shadow-md rounded-lg p-6 border">
          <Tabs defaultValue="login">
            <TabsList className="grid w-full grid-cols-2 mb-6">
              <TabsTrigger value="login">Entrar</TabsTrigger>
              <TabsTrigger value="register">Criar Conta</TabsTrigger>
            </TabsList>

            <TabsContent value="login">
              <form onSubmit={handleLogin}>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="email">E-mail</Label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                  <div>
                    <div className="flex items-center justify-between">
                      <Label htmlFor="password">Senha</Label>
                      <a href="#" className="text-xs text-trust hover:underline">
                        Esqueceu a senha?
                      </a>
                    </div>
                    <Input
                      id="password"
                      name="password"
                      type="password"
                      value={formData.password}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <Button className="w-full bg-cta hover:bg-cta-dark" disabled={isLoading}>
                    {isLoading ? "Entrando..." : "Entrar"}
                  </Button>
                </div>
              </form>

              <div className="mt-6">
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <Separator />
                  </div>
                  <div className="relative flex justify-center">
                    <span className="bg-white px-2 text-xs text-muted-foreground">
                      Ou continue com
                    </span>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-2 mt-4">
                  <Button variant="outline" className="w-full">
                    <Facebook className="mr-2 h-4 w-4" />
                    Facebook
                  </Button>
                  <Button variant="outline" className="w-full">
                    <Mail className="mr-2 h-4 w-4" />
                    Google
                  </Button>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="register">
              <form onSubmit={handleRegister}>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="name">Nome completo</Label>
                    <Input
                      id="name"
                      name="name"
                      type="text"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="reg-email">E-mail</Label>
                    <Input
                      id="reg-email"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="<EMAIL>"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone">Telefone</Label>
                    <Input
                      id="phone"
                      name="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={handleInputChange}
                      placeholder="(00) 00000-0000"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="reg-password">Senha</Label>
                    <Input
                      id="reg-password"
                      name="password"
                      type="password"
                      value={formData.password}
                      onChange={handleInputChange}
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="role">Tipo de conta</Label>
                    <RadioGroup
                      value={formData.role}
                      onValueChange={(value) => setFormData(prev => ({ ...prev, role: value }))}
                      className="flex space-x-1 mt-2"
                    >
                      <div className="flex items-center space-x-2 border rounded-md p-2 flex-1">
                        <RadioGroupItem value="customer" id="customer" />
                        <Label htmlFor="customer" className="flex items-center cursor-pointer">
                          <User className="h-4 w-4 mr-1" />
                          Cliente
                        </Label>
                      </div>

                      <div className="flex items-center space-x-2 border rounded-md p-2 flex-1">
                        <RadioGroupItem value="merchant" id="merchant" />
                        <Label htmlFor="merchant" className="flex items-center cursor-pointer">
                          <Store className="h-4 w-4 mr-1" />
                          Lojista
                        </Label>
                      </div>

                      <div className="flex items-center space-x-2 border rounded-md p-2 flex-1">
                        <RadioGroupItem value="deliverer" id="deliverer" />
                        <Label htmlFor="deliverer" className="flex items-center cursor-pointer">
                          <Truck className="h-4 w-4 mr-1" />
                          Entregador
                        </Label>
                      </div>
                    </RadioGroup>
                  </div>

                  <Button className="w-full bg-eco hover:bg-eco-dark" disabled={isLoading}>
                    {isLoading ? "Criando conta..." : "Criar conta"}
                  </Button>
                </div>
              </form>

              <div className="mt-4 text-center text-xs text-muted-foreground">
                Ao criar uma conta, você concorda com nossos{' '}
                <a href="#" className="text-trust hover:underline">
                  Termos de Serviço
                </a>{' '}
                e{' '}
                <a href="#" className="text-trust hover:underline">
                  Política de Privacidade
                </a>
                .
              </div>
            </TabsContent>
          </Tabs>
        </div>

        <div className="mt-6 text-center">
          <div className="text-sm text-muted-foreground">
            <span>Quero me juntar como </span>
            <button
              onClick={() => {
                setFormData(prev => ({ ...prev, role: 'merchant' }));
                navigate('/login', { state: { role: 'merchant' } });
              }}
              className="text-trust hover:underline bg-transparent border-none p-0 cursor-pointer"
            >
              Comerciante
            </button>
            <span> ou </span>
            <button
              onClick={() => {
                setFormData(prev => ({ ...prev, role: 'deliverer' }));
                navigate('/login', { state: { role: 'deliverer' } });
              }}
              className="text-trust hover:underline bg-transparent border-none p-0 cursor-pointer"
            >
              Entregador
            </button>
          </div>
        </div>
      </div>

      {/* Diálogo de seleção de perfil para modo de desenvolvimento */}
      <Dialog open={showRoleDialog} onOpenChange={setShowRoleDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Selecione o tipo de perfil</DialogTitle>
          </DialogHeader>

          <div className="py-4">
            <RadioGroup
              value={selectedRole}
              onValueChange={(value: 'customer' | 'merchant' | 'deliverer') => setSelectedRole(value)}
              className="space-y-3"
            >
              <div className="flex items-center space-x-2 border rounded-md p-3">
                <RadioGroupItem value="customer" id="role-customer" />
                <Label htmlFor="role-customer" className="flex items-center cursor-pointer">
                  <User className="h-5 w-5 mr-2" />
                  <div>
                    <div className="font-medium">Cliente</div>
                    <div className="text-sm text-muted-foreground">Comprar produtos e serviços</div>
                  </div>
                </Label>
              </div>

              <div className="flex items-center space-x-2 border rounded-md p-3">
                <RadioGroupItem value="merchant" id="role-merchant" />
                <Label htmlFor="role-merchant" className="flex items-center cursor-pointer">
                  <Store className="h-5 w-5 mr-2" />
                  <div>
                    <div className="font-medium">Lojista</div>
                    <div className="text-sm text-muted-foreground">Gerenciar loja e produtos</div>
                  </div>
                </Label>
              </div>

              <div className="flex items-center space-x-2 border rounded-md p-3">
                <RadioGroupItem value="deliverer" id="role-deliverer" />
                <Label htmlFor="role-deliverer" className="flex items-center cursor-pointer">
                  <Truck className="h-5 w-5 mr-2" />
                  <div>
                    <div className="font-medium">Entregador</div>
                    <div className="text-sm text-muted-foreground">Realizar entregas</div>
                  </div>
                </Label>
              </div>
            </RadioGroup>
          </div>

          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setShowRoleDialog(false)}>
              Cancelar
            </Button>
            <Button
              className="bg-eco hover:bg-eco-dark"
              onClick={() => processLogin()}
            >
              Continuar
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Login;
