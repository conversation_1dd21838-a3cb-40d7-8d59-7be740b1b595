
import { useState, useEffect } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { Navbar } from "@/components/ui/navbar";
import { BottomNav } from "@/components/ui/bottom-nav";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ChevronLeft, Heart, Minus, Plus, Share2, ShoppingCart, Star, Store, Truck, Clock, Award, ShieldCheck } from "lucide-react";
import { useCart } from "@/hooks/useCart";
import { useQuery } from "@tanstack/react-query";
import { fetchProducts } from "@/services/products";
import { Product } from "@/types/product";
import { toast } from "@/hooks/use-toast";
import { motion, AnimatePresence } from "framer-motion";
import { AnimatedPage, AnimatedButton, FadeIn } from "@/components/animations";
import { Badge } from "@/components/ui/badge";

const ProductDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [quantity, setQuantity] = useState(1);
  const [isFavorite, setIsFavorite] = useState(false);
  const { addItem } = useCart();

  // Fetch all products
  const { data: products, isLoading } = useQuery({
    queryKey: ["products"],
    queryFn: fetchProducts,
  });

  // Find the current product
  const product = products?.find(p => p.id === id || encodeURIComponent(p.name) === id);

  useEffect(() => {
    if (!isLoading && !product) {
      // Product not found, redirect to home
      toast({
        title: "Produto não encontrado",
        description: "O produto que você está procurando não existe.",
        variant: "destructive"
      });
      navigate('/');
    }
  }, [product, isLoading, navigate]);

  const handleAddToCart = () => {
    if (product) {
      addItem(product, quantity);
    }
  };

  const incrementQuantity = () => {
    setQuantity(prev => prev + 1);
  };

  const decrementQuantity = () => {
    if (quantity > 1) {
      setQuantity(prev => prev - 1);
    }
  };

  if (isLoading || !product) {
    return (
      <AnimatedPage className="flex flex-col min-h-screen bg-gray-50">
        <Navbar />
        <main className="flex-1 flex items-center justify-center">
          <motion.div
            className="rounded-full h-12 w-12 border-t-2 border-b-2 border-cta"
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          />
        </main>
        <BottomNav />
      </AnimatedPage>
    );
  }

  return (
    <AnimatedPage className="flex flex-col min-h-screen bg-gray-50">
      <Navbar />

      <main className="flex-1 pb-20 md:pb-6">
        {/* Product Image */}
        <motion.div
          className="relative h-80 bg-white"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <motion.button
            className="absolute top-4 left-4 z-10 bg-white rounded-full p-2 shadow-sm"
            onClick={() => navigate(-1)}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <ChevronLeft className="h-5 w-5" />
          </motion.button>
          <div className="absolute top-4 right-4 z-10 flex space-x-2">
            <motion.button
              className="bg-white rounded-full p-2 shadow-sm"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <Share2 className="h-5 w-5" />
            </motion.button>
            <motion.button
              className="bg-white rounded-full p-2 shadow-sm"
              onClick={() => setIsFavorite(!isFavorite)}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <Heart className={`h-5 w-5 ${isFavorite ? 'fill-cta text-cta' : ''}`} />
            </motion.button>
          </div>
          {product.isPromo && (
            <div className="absolute top-4 left-1/2 transform -translate-x-1/2 z-10">
              <Badge className="bg-cta text-white">
                {Math.round((1 - product.price / (product.originalPrice || product.price)) * 100)}% OFF
              </Badge>
            </div>
          )}
          <motion.img
            src={product.image}
            alt={product.name}
            className="w-full h-full object-cover"
            initial={{ scale: 1.05 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5 }}
          />
        </motion.div>

        {/* Product Info */}
        <FadeIn className="container px-4 py-6" delay={0.1}>
          <div className="flex items-center justify-between mb-1">
            <motion.div
              className="flex items-center text-sm text-muted-foreground"
              whileHover={{ scale: 1.05 }}
            >
              <Store className="h-4 w-4 mr-1" />
              <span>{product.shopName}</span>
            </motion.div>
            <div className="flex items-center">
              <Star className="h-4 w-4 text-yellow-400 fill-yellow-400 mr-1" />
              <span className="text-sm font-medium">4.8</span>
              <span className="text-sm text-muted-foreground ml-1">(56)</span>
            </div>
          </div>

          <motion.h1
            className="text-2xl font-bold mb-2"
            initial={{ opacity: 0, y: 5 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            {product.name}
          </motion.h1>

          <motion.div
            className="flex items-baseline mb-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            <span className="text-xl font-bold mr-2">
              {new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL',
              }).format(product.price)}
            </span>
            {product.originalPrice && (
              <>
                <span className="text-sm text-muted-foreground line-through">
                  {new Intl.NumberFormat('pt-BR', {
                    style: 'currency',
                    currency: 'BRL',
                  }).format(product.originalPrice)}
                </span>
                <motion.span
                  className="text-xs text-white bg-cta px-2 py-0.5 rounded ml-2"
                  initial={{ scale: 0.8 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.4, type: "spring" }}
                >
                  {Math.round((1 - product.price / product.originalPrice) * 100)}%
                </motion.span>
              </>
            )}
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-6">
            <motion.div
              className="bg-eco-light p-3 rounded-lg flex items-center"
              whileHover={{ y: -2, boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)" }}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              <Truck className="h-5 w-5 text-eco mr-2" />
              <div>
                <p className="text-sm font-medium">Entrega rápida</p>
                <p className="text-xs text-muted-foreground">25-40 min</p>
              </div>
            </motion.div>

            <motion.div
              className="bg-trust-light p-3 rounded-lg flex items-center"
              whileHover={{ y: -2, boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)" }}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
            >
              <ShieldCheck className="h-5 w-5 text-trust mr-2" />
              <div>
                <p className="text-sm font-medium">Garantia de qualidade</p>
                <p className="text-xs text-muted-foreground">Produto verificado</p>
              </div>
            </motion.div>

            <motion.div
              className="bg-cta-light p-3 rounded-lg flex items-center"
              whileHover={{ y: -2, boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)" }}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.7 }}
            >
              <Award className="h-5 w-5 text-cta mr-2" />
              <div>
                <p className="text-sm font-medium">Produto local</p>
                <p className="text-xs text-muted-foreground">Apoie o comércio local</p>
              </div>
            </motion.div>
          </div>

          <Separator className="my-6" />

          {/* Product Tabs */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
          >
            <Tabs defaultValue="details">
              <TabsList className="grid w-full grid-cols-3 mb-6">
                <TabsTrigger value="details">Detalhes</TabsTrigger>
                <TabsTrigger value="nutrition">Nutrição</TabsTrigger>
                <TabsTrigger value="reviews">Avaliações</TabsTrigger>
              </TabsList>

            <TabsContent value="details" className="space-y-4">
              <div>
                <h3 className="font-medium mb-2">Descrição</h3>
                <p className="text-sm text-muted-foreground">
                  Morangos frescos e orgânicos, colhidos diretamente de nossos produtores locais.
                  Cultivados sem o uso de pesticidas ou fertilizantes químicos, esses morangos
                  são doces, suculentos e cheios de sabor natural.
                </p>
              </div>

              <div>
                <h3 className="font-medium mb-2">Origem</h3>
                <p className="text-sm text-muted-foreground">
                  Cultivados em fazendas familiares da região, apoiando a agricultura sustentável local.
                </p>
              </div>

              <div>
                <h3 className="font-medium mb-2">Informações do Produto</h3>
                <div className="grid grid-cols-2 gap-y-2 text-sm">
                  <div>Peso:</div>
                  <div>500g (aproximadamente)</div>
                  <div>Tipo:</div>
                  <div>Orgânico</div>
                  <div>Validade:</div>
                  <div>5 dias (refrigerado)</div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="nutrition">
              <div className="bg-white p-4 rounded-lg border">
                <h3 className="font-medium mb-3">Informação Nutricional</h3>
                <p className="text-xs mb-2">Porção de 100g</p>

                <div className="space-y-2">
                  <div className="flex justify-between py-1 border-b text-sm">
                    <span>Calorias</span>
                    <span>32 kcal</span>
                  </div>
                  <div className="flex justify-between py-1 border-b text-sm">
                    <span>Carboidratos</span>
                    <span>7.7 g</span>
                  </div>
                  <div className="flex justify-between py-1 border-b text-sm">
                    <span>Proteínas</span>
                    <span>0.7 g</span>
                  </div>
                  <div className="flex justify-between py-1 border-b text-sm">
                    <span>Gorduras</span>
                    <span>0.3 g</span>
                  </div>
                  <div className="flex justify-between py-1 border-b text-sm">
                    <span>Fibras</span>
                    <span>2.0 g</span>
                  </div>
                  <div className="flex justify-between py-1 text-sm">
                    <span>Vitamina C</span>
                    <span>58.8 mg</span>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="reviews">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">Avaliações de Clientes</h3>
                    <div className="flex items-center mt-1">
                      <div className="flex">
                        {[1, 2, 3, 4, 5].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${i < 5 ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`}
                          />
                        ))}
                      </div>
                      <span className="text-sm ml-2">4.8 de 5 (56 avaliações)</span>
                    </div>
                  </div>
                  <Button variant="outline" size="sm">Ver Todas</Button>
                </div>

                <div className="bg-white p-4 rounded-lg border">
                  <div className="flex justify-between mb-2">
                    <div>
                      <div className="font-medium">Ana Paula</div>
                      <div className="flex">
                        {[1, 2, 3, 4, 5].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-3 w-3 ${i < 5 ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`}
                          />
                        ))}
                      </div>
                    </div>
                    <div className="text-xs text-muted-foreground">2 dias atrás</div>
                  </div>
                  <p className="text-sm">
                    Morangos deliciosos e muito frescos! Adoro que são orgânicos
                    e de produtores locais. Vou comprar novamente.
                  </p>
                </div>

                <div className="bg-white p-4 rounded-lg border">
                  <div className="flex justify-between mb-2">
                    <div>
                      <div className="font-medium">Carlos Mendes</div>
                      <div className="flex">
                        {[1, 2, 3, 4, 5].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-3 w-3 ${i < 4 ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`}
                          />
                        ))}
                      </div>
                    </div>
                    <div className="text-xs text-muted-foreground">5 dias atrás</div>
                  </div>
                  <p className="text-sm">
                    Os morangos chegaram em ótimo estado. Sabor muito bom.
                    Tirando uma estrela porque a embalagem veio um pouco amassada.
                  </p>
                </div>
              </div>
            </TabsContent>
            </Tabs>
          </motion.div>
        </FadeIn>
      </main>

      {/* Add to Cart Bar */}
      <motion.div
        className="fixed bottom-0 left-0 right-0 bg-white border-t p-4 md:px-6 z-40 pb-safe"
        initial={{ y: 100 }}
        animate={{ y: 0 }}
        transition={{ delay: 0.5, type: "spring", stiffness: 300, damping: 30 }}
      >
        <div className="flex items-center space-x-4">
          <div className="flex items-center border rounded-full bg-gray-50">
            <motion.button
              className="px-3 py-2 text-muted-foreground hover:bg-gray-100 rounded-l-full"
              onClick={decrementQuantity}
              whileTap={{ scale: 0.9 }}
              disabled={quantity <= 1}
            >
              <Minus className="h-4 w-4" />
            </motion.button>
            <span className="px-3 py-2 text-center min-w-[40px] font-medium">{quantity}</span>
            <motion.button
              className="px-3 py-2 text-muted-foreground hover:bg-gray-100 rounded-r-full"
              onClick={incrementQuantity}
              whileTap={{ scale: 0.9 }}
            >
              <Plus className="h-4 w-4" />
            </motion.button>
          </div>
          <AnimatedButton
            className="flex-1 bg-cta hover:bg-cta-dark text-white"
            onClick={handleAddToCart}
            whileHoverScale={1.02}
            whileTapScale={0.98}
          >
            <ShoppingCart className="mr-2 h-4 w-4" />
            Adicionar ao Carrinho • {new Intl.NumberFormat('pt-BR', {
              style: 'currency',
              currency: 'BRL',
            }).format(product.price * quantity)}
          </AnimatedButton>
        </div>
      </motion.div>

      <BottomNav />
    </AnimatedPage>
  );
};

export default ProductDetails;
