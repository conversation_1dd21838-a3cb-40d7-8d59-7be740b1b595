import { deliveryService } from './deliveryService';
import { orderService } from './orderService';
import { Tables } from '@/types/database';
import { Order } from '@/types/order';

// Delivery interface for frontend
export interface Delivery {
  id: string;
  orderId: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  pickupTime: string | null;
  deliveryTime: string | null;
  earnings: number;
  createdAt: string;
  order?: Order;
}

// Convert database delivery to frontend delivery model
const mapDatabaseDeliveryToDelivery = async (
  dbDelivery: Tables<'deliveries'>
): Promise<Delivery> => {
  // Fetch order details if needed
  let order: Order | undefined = undefined;

  try {
    const dbOrder = await orderService.getById(dbDelivery.order_id);
    if (dbOrder) {
      const orderItems = await orderService.getOrderItems(dbOrder.id);

      order = {
        id: dbOrder.id,
        status: dbOrder.status,
        items: orderItems.map(item => ({
          productId: item.product_id,
          quantity: item.quantity,
          price: item.price,
          name: item.name
        })),
        total: dbOrder.total,
        createdAt: dbOrder.created_at,
        shopName: dbOrder.shop_id, // Simplified for now
        deliveryAddress: dbOrder.delivery_address
      };
    }
  } catch (error) {
    console.error(`Error fetching order details for delivery ${dbDelivery.id}:`, error);
  }

  return {
    id: dbDelivery.id,
    orderId: dbDelivery.order_id,
    status: dbDelivery.status,
    pickupTime: dbDelivery.pickup_time,
    deliveryTime: dbDelivery.delivery_time,
    earnings: dbDelivery.earnings,
    createdAt: dbDelivery.created_at,
    order
  };
};

// Mock deliveries for development mode
const mockDeliveries: Delivery[] = [
  {
    id: "1",
    orderId: "2",
    status: "in_progress",
    pickupTime: "2024-04-18T12:00:00Z",
    deliveryTime: null,
    earnings: 7.0,
    createdAt: "2024-04-18T11:45:00Z"
  },
  {
    id: "2",
    orderId: "1",
    status: "completed",
    pickupTime: "2024-04-18T10:15:00Z",
    deliveryTime: "2024-04-18T10:45:00Z",
    earnings: 3.2,
    createdAt: "2024-04-18T10:10:00Z"
  },
  {
    id: "3",
    orderId: "4",
    status: "completed",
    pickupTime: "2024-04-17T09:30:00Z",
    deliveryTime: "2024-04-17T10:00:00Z",
    earnings: 3.8,
    createdAt: "2024-04-17T09:20:00Z"
  }
];

// Fetch deliveries by deliverer ID
export const fetchDeliveriesByDelivererId = async (
  delivererId: string
): Promise<Delivery[]> => {
  try {
    const dbDeliveries = await deliveryService.getByDelivererId(delivererId);
    const deliveries = await Promise.all(dbDeliveries.map(mapDatabaseDeliveryToDelivery));
    return deliveries;
  } catch (error) {
    console.error(`Error fetching deliveries for deliverer ${delivererId}:`, error);
    return mockDeliveries;
  }
};

// Fetch deliveries by status
export const fetchDeliveriesByStatus = async (
  status: Delivery['status'],
  delivererId?: string
): Promise<Delivery[]> => {
  try {
    const dbDeliveries = await deliveryService.getByStatus(status, delivererId);
    const deliveries = await Promise.all(dbDeliveries.map(mapDatabaseDeliveryToDelivery));
    return deliveries;
  } catch (error) {
    console.error(`Error fetching deliveries with status ${status}:`, error);
    return mockDeliveries.filter(d => d.status === status);
  }
};

// Update delivery status
export const updateDeliveryStatus = async (
  deliveryId: string,
  status: Delivery['status']
): Promise<Delivery | null> => {
  try {
    const dbDelivery = await deliveryService.updateStatus(deliveryId, status);
    if (!dbDelivery) {
      throw new Error(`Failed to update delivery ${deliveryId}`);
    }

    return await mapDatabaseDeliveryToDelivery(dbDelivery);
  } catch (error) {
    console.error(`Error updating delivery ${deliveryId} status to ${status}:`, error);

    // Update mock delivery in development mode
    const deliveryIndex = mockDeliveries.findIndex(d => d.id === deliveryId);
    if (deliveryIndex >= 0) {
      mockDeliveries[deliveryIndex] = {
        ...mockDeliveries[deliveryIndex],
        status
      };
      return mockDeliveries[deliveryIndex];
    }

    return null;
  }
};

// Complete a delivery
export const completeDelivery = async (deliveryId: string): Promise<Delivery | null> => {
  try {
    const dbDelivery = await deliveryService.completeDelivery(deliveryId);
    if (!dbDelivery) {
      throw new Error(`Failed to complete delivery ${deliveryId}`);
    }

    return await mapDatabaseDeliveryToDelivery(dbDelivery);
  } catch (error) {
    console.error(`Error completing delivery ${deliveryId}:`, error);

    // Update mock delivery in development mode
    const deliveryIndex = mockDeliveries.findIndex(d => d.id === deliveryId);
    if (deliveryIndex >= 0) {
      mockDeliveries[deliveryIndex] = {
        ...mockDeliveries[deliveryIndex],
        status: 'completed',
        deliveryTime: new Date().toISOString()
      };
      return mockDeliveries[deliveryIndex];
    }

    return null;
  }
};

// Get earnings for a deliverer
export const getDelivererEarnings = async (
  delivererId: string,
  period: 'day' | 'week' | 'month' | 'all' = 'all'
): Promise<number> => {
  try {
    return await deliveryService.getEarnings(delivererId, period);
  } catch (error) {
    console.error(`Error fetching earnings for deliverer ${delivererId}:`, error);

    // Return mock earnings in development mode
    return mockDeliveries
      .filter(d => d.status === 'completed')
      .reduce((total, delivery) => total + delivery.earnings, 0);
  }
};

// Fetch orders by deliverer ID
export const fetchOrdersByDelivererId = async (delivererId: string): Promise<Order[]> => {
  try {
    const dbOrders = await orderService.getByDelivererId(delivererId);
    // Process orders
    return dbOrders.map(order => ({
      id: order.id,
      status: order.status,
      items: [],  // This would be populated in a real implementation
      total: order.total,
      createdAt: order.created_at,
      shopName: "Loja Exemplo",  // This would be fetched in a real implementation
      deliveryAddress: order.delivery_address,
      paymentMethod: order.payment_method,
      notes: order.notes
    }));
  } catch (error) {
    console.error(`Error fetching orders for deliverer ${delivererId}:`, error);

    // Return mock orders in development mode
    return [
      {
        id: "order1",
        status: "in_progress",
        items: [
          { productId: "1", name: "Pizza Margherita", price: 45.90, quantity: 1, shopName: "Pizzaria Napolitana" },
          { productId: "2", name: "Refrigerante 2L", price: 12.90, quantity: 1, shopName: "Pizzaria Napolitana" }
        ],
        total: 58.80,
        createdAt: new Date().toISOString(),
        shopName: "Pizzaria Napolitana",
        deliveryAddress: "Rua das Flores, 123, Apto 101, Centro, São Paulo, SP",
        paymentMethod: "credit_card",
        delivererId: delivererId,
        delivererName: "João Entregador",
        notes: "Deixar na portaria"
      },
      {
        id: "order2",
        status: "delivered",
        items: [
          { productId: "3", name: "Hambúrguer Artesanal", price: 32.90, quantity: 1, shopName: "Burger House" },
          { productId: "4", name: "Batata Frita", price: 15.90, quantity: 1, shopName: "Burger House" }
        ],
        total: 48.80,
        createdAt: new Date(Date.now() - 86400000).toISOString(), // Yesterday
        shopName: "Burger House",
        deliveryAddress: "Av. Paulista, 1000, Apto 500, Bela Vista, São Paulo, SP",
        paymentMethod: "pix",
        delivererId: delivererId,
        delivererName: "João Entregador",
        notes: ""
      }
    ];
  }
};

// Fetch pending orders (available for deliverers)
export const fetchPendingOrders = async (): Promise<Order[]> => {
  try {
    const dbOrders = await orderService.getPendingOrders();
    // Process orders
    return dbOrders.map(order => ({
      id: order.id,
      status: order.status,
      items: [],  // This would be populated in a real implementation
      total: order.total,
      createdAt: order.created_at,
      shopName: "Loja Exemplo",  // This would be fetched in a real implementation
      deliveryAddress: order.delivery_address,
      paymentMethod: order.payment_method,
      notes: order.notes
    }));
  } catch (error) {
    console.error('Error fetching pending orders:', error);

    // Return mock orders in development mode
    return [
      {
        id: "order3",
        status: "pending",
        items: [
          { productId: "5", name: "Açaí 500ml", price: 25.90, quantity: 1, shopName: "Açaí Tropical" },
          { productId: "6", name: "Granola", price: 5.90, quantity: 1, shopName: "Açaí Tropical" }
        ],
        total: 31.80,
        createdAt: new Date().toISOString(),
        shopName: "Açaí Tropical",
        deliveryAddress: "Rua Augusta, 500, Consolação, São Paulo, SP",
        paymentMethod: "credit_card",
        notes: "Sem banana"
      },
      {
        id: "order4",
        status: "pending",
        items: [
          { productId: "7", name: "Yakisoba", price: 38.90, quantity: 1, shopName: "China in Box" },
          { productId: "8", name: "Rolinho Primavera", price: 18.90, quantity: 1, shopName: "China in Box" }
        ],
        total: 57.80,
        createdAt: new Date().toISOString(),
        shopName: "China in Box",
        deliveryAddress: "Rua Oscar Freire, 200, Jardins, São Paulo, SP",
        paymentMethod: "pix",
        notes: "Sem shoyu"
      }
    ];
  }
};
