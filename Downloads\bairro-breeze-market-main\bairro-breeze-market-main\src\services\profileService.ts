import { ApiService } from './api';
import { Tables, Insertable, Updatable } from '@/types/database';
import { supabaseClient } from '@/lib/supabase';

// Create a typed service for user profiles
class ProfileService extends ApiService<'profiles'> {
  constructor() {
    super('profiles');
  }

  /**
   * Get profile by user ID
   * @param userId User ID
   * @returns Promise with profile
   */
  async getByUserId(userId: string): Promise<Tables<'profiles'> | null> {
    try {
      const { data, error } = await supabaseClient
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Error fetching profile by user ID:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in getByUserId:', error);
      return null;
    }
  }

  /**
   * Update profile
   * @param userId User ID
   * @param profileData Profile data
   * @returns Promise with updated profile
   */
  async updateProfile(
    userId: string,
    profileData: Partial<Tables<'profiles'>>
  ): Promise<Tables<'profiles'> | null> {
    try {
      const { data, error } = await supabaseClient
        .from('profiles')
        .update({
          ...profileData,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select()
        .single();

      if (error) {
        console.error('Error updating profile:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in updateProfile:', error);
      return null;
    }
  }

  /**
   * Upload avatar image
   * @param userId User ID
   * @param file Image file
   * @returns Promise with avatar URL
   */
  async uploadAvatar(userId: string, file: File): Promise<string | null> {
    try {
      const fileExt = file.name.split('.').pop();
      const fileName = `${userId}-${Math.random().toString(36).substring(2)}.${fileExt}`;
      const filePath = `avatars/${fileName}`;

      const { error: uploadError } = await supabaseClient.storage
        .from('avatars')
        .upload(filePath, file);

      if (uploadError) {
        console.error('Error uploading avatar:', uploadError);
        throw uploadError;
      }

      const { data: urlData } = supabaseClient.storage
        .from('avatars')
        .getPublicUrl(filePath);

      const avatarUrl = urlData.publicUrl;

      // Update profile with new avatar URL
      const { error: updateError } = await supabaseClient
        .from('profiles')
        .update({
          avatar_url: avatarUrl,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);

      if (updateError) {
        console.error('Error updating profile with avatar URL:', updateError);
        throw updateError;
      }

      return avatarUrl;
    } catch (error) {
      console.error('Error in uploadAvatar:', error);
      return null;
    }
  }

  /**
   * Get users by role
   * @param role User role
   * @returns Promise with users
   */
  async getByRole(role: 'customer' | 'merchant' | 'deliverer'): Promise<Tables<'profiles'>[]> {
    try {
      const { data, error } = await supabaseClient
        .from('profiles')
        .select('*')
        .eq('role', role)
        .order('name', { ascending: true });

      if (error) {
        console.error('Error fetching users by role:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in getByRole:', error);
      return [];
    }
  }
}

export const profileService = new ProfileService();
