import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/hooks/useAuth";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Store, Package, TrendingUp, Users, Clock, DollarSign, ShoppingBag, LogOut } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { AnimatedPage } from "@/components/animations";
import { LoadingState } from "@/components/ui/async-state";

const MerchantDashboard = () => {
  const { user, signOut } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);

  // Verificar se o usuário está autenticado e é um lojista
  useEffect(() => {
    const checkAuth = async () => {
      if (!user) {
        navigate("/login");
        return;
      }

      // Verificar se o usuário é um lojista
      if (user.user_metadata?.role !== "merchant") {
        toast({
          title: "Acesso negado",
          description: "Você não tem permissão para acessar esta página.",
          variant: "destructive",
        });
        navigate("/");
        return;
      }

      // Simular carregamento de dados
      setTimeout(() => {
        setLoading(false);
      }, 1000);
    };

    checkAuth();
  }, [user, navigate]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingState message="Carregando painel do lojista..." />
      </div>
    );
  }

  return (
    <AnimatedPage className="min-h-screen bg-gray-50">
      <header className="bg-eco text-white sticky top-0 z-10">
        <div className="container px-4 h-16 flex items-center justify-between">
          <div className="flex items-center">
            <Store className="h-6 w-6 mr-2" />
            <h1 className="text-xl font-medium">Painel do Lojista</h1>
          </div>
          <Button variant="ghost" onClick={signOut} className="text-white">
            <LogOut className="h-5 w-5 mr-2" />
            Sair
          </Button>
        </div>
      </header>

      <main className="container px-4 py-6 max-w-6xl mx-auto">
        {/* Resumo */}
        <section className="mb-8">
          <h2 className="text-2xl font-bold mb-4">Bem-vindo, {user?.user_metadata?.name || "Lojista"}</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Pedidos Hoje</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <ShoppingBag className="h-5 w-5 text-eco mr-2" />
                  <span className="text-2xl font-bold">12</span>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Faturamento</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <DollarSign className="h-5 w-5 text-eco mr-2" />
                  <span className="text-2xl font-bold">R$ 1.250,00</span>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Tempo Médio</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <Clock className="h-5 w-5 text-eco mr-2" />
                  <span className="text-2xl font-bold">28 min</span>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Avaliação</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <Users className="h-5 w-5 text-eco mr-2" />
                  <span className="text-2xl font-bold">4.8</span>
                  <span className="text-sm text-muted-foreground ml-2">(32 avaliações)</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Abas principais */}
        <Tabs defaultValue="orders">
          <TabsList className="mb-6">
            <TabsTrigger value="orders" className="flex items-center">
              <ShoppingBag className="h-4 w-4 mr-2" />
              Pedidos
            </TabsTrigger>
            <TabsTrigger value="products" className="flex items-center">
              <Package className="h-4 w-4 mr-2" />
              Produtos
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center">
              <TrendingUp className="h-4 w-4 mr-2" />
              Análises
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="orders">
            <Card>
              <CardHeader>
                <CardTitle>Pedidos Recentes</CardTitle>
                <CardDescription>Gerencie seus pedidos recentes e atualize seus status.</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="border rounded-md p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium">Pedido #1234</h3>
                        <p className="text-sm text-muted-foreground">2 itens • R$ 45,90</p>
                        <div className="flex items-center mt-2">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            Pendente
                          </span>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">João Silva</p>
                        <p className="text-xs text-muted-foreground">Há 5 minutos</p>
                      </div>
                    </div>
                    <div className="flex justify-end mt-4 space-x-2">
                      <Button variant="outline" size="sm">Ver Detalhes</Button>
                      <Button size="sm" className="bg-eco hover:bg-eco-dark">Aceitar</Button>
                    </div>
                  </div>
                  
                  <div className="border rounded-md p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium">Pedido #1233</h3>
                        <p className="text-sm text-muted-foreground">4 itens • R$ 78,50</p>
                        <div className="flex items-center mt-2">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Em preparo
                          </span>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">Maria Oliveira</p>
                        <p className="text-xs text-muted-foreground">Há 15 minutos</p>
                      </div>
                    </div>
                    <div className="flex justify-end mt-4 space-x-2">
                      <Button variant="outline" size="sm">Ver Detalhes</Button>
                      <Button size="sm" className="bg-eco hover:bg-eco-dark">Pronto para Entrega</Button>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline">Ver Todos os Pedidos</Button>
                <Button className="bg-eco hover:bg-eco-dark">Atualizar</Button>
              </CardFooter>
            </Card>
          </TabsContent>
          
          <TabsContent value="products">
            <Card>
              <CardHeader>
                <CardTitle>Seus Produtos</CardTitle>
                <CardDescription>Gerencie seu catálogo de produtos.</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="border rounded-md p-4 flex items-center">
                    <div className="h-16 w-16 bg-gray-100 rounded-md mr-4"></div>
                    <div className="flex-1">
                      <h3 className="font-medium">Hambúrguer Artesanal</h3>
                      <p className="text-sm text-muted-foreground">Categoria: Lanches</p>
                      <p className="text-sm font-medium">R$ 25,90</p>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">Editar</Button>
                      <Button variant="destructive" size="sm">Remover</Button>
                    </div>
                  </div>
                  
                  <div className="border rounded-md p-4 flex items-center">
                    <div className="h-16 w-16 bg-gray-100 rounded-md mr-4"></div>
                    <div className="flex-1">
                      <h3 className="font-medium">Batata Frita Grande</h3>
                      <p className="text-sm text-muted-foreground">Categoria: Acompanhamentos</p>
                      <p className="text-sm font-medium">R$ 15,90</p>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">Editar</Button>
                      <Button variant="destructive" size="sm">Remover</Button>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button variant="outline">Ver Todos os Produtos</Button>
                <Button className="bg-eco hover:bg-eco-dark">Adicionar Produto</Button>
              </CardFooter>
            </Card>
          </TabsContent>
          
          <TabsContent value="analytics">
            <Card>
              <CardHeader>
                <CardTitle>Análise de Vendas</CardTitle>
                <CardDescription>Acompanhe o desempenho da sua loja.</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-center justify-center bg-gray-100 rounded-md">
                  <p className="text-muted-foreground">Gráfico de vendas será exibido aqui</p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Produto Mais Vendido</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="font-medium">Hambúrguer Artesanal</p>
                      <p className="text-sm text-muted-foreground">142 unidades</p>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Horário de Pico</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="font-medium">19:00 - 20:00</p>
                      <p className="text-sm text-muted-foreground">32% dos pedidos</p>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm font-medium">Ticket Médio</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="font-medium">R$ 42,50</p>
                      <p className="text-sm text-muted-foreground">+12% esta semana</p>
                    </CardContent>
                  </Card>
                </div>
              </CardContent>
              <CardFooter>
                <Button className="w-full bg-eco hover:bg-eco-dark">Gerar Relatório Completo</Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </main>
    </AnimatedPage>
  );
};

export default MerchantDashboard;
