import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Truck, Package, TrendingUp, Users, Clock, DollarSign, ShoppingBag, LogOut, Settings, CreditCard, User, Wallet } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { AnimatedPage } from "@/components/animations";
import { LoadingState } from "@/components/ui/async-state";
import { useAuth } from "@/hooks/useAuth";
import { DelivererProfileForm } from "@/components/deliverer/DelivererProfileForm";

const DelivererProfile = () => {
  const { user, signOut } = useAuth();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("profile");
  const [loading, setLoading] = useState(false);

  const handleSignOut = async () => {
    try {
      setLoading(true);
      await signOut();
      navigate("/login");
    } catch (error) {
      console.error("Error signing out:", error);
      toast({
        title: "Erro ao sair",
        description: "Não foi possível sair da sua conta. Tente novamente mais tarde.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingState message="Carregando..." />
      </div>
    );
  }

  return (
    <AnimatedPage className="min-h-screen bg-gray-50">
      <header className="bg-trust text-white sticky top-0 z-10">
        <div className="container px-4 h-16 flex items-center justify-between">
          <div className="flex items-center">
            <Truck className="h-6 w-6 mr-2" />
            <h1 className="text-xl font-medium">Perfil do Entregador</h1>
          </div>
          <Button variant="ghost" onClick={handleSignOut} className="text-white">
            <LogOut className="h-5 w-5 mr-2" />
            Sair
          </Button>
        </div>
      </header>

      <main className="container px-4 py-6">
        <div className="flex flex-col md:flex-row gap-6">
          {/* Sidebar */}
          <div className="w-full md:w-1/4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="mr-2 h-5 w-5" />
                  Minha Conta
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <Tabs
                  value={activeTab}
                  onValueChange={setActiveTab}
                  orientation="vertical"
                  className="w-full"
                >
                  <TabsList className="flex flex-col items-start h-auto p-0 bg-transparent">
                    <TabsTrigger
                      value="profile"
                      className="w-full justify-start px-4 py-2 data-[state=active]:bg-muted"
                    >
                      <User className="mr-2 h-4 w-4" />
                      Informações Pessoais
                    </TabsTrigger>
                    <TabsTrigger
                      value="earnings"
                      className="w-full justify-start px-4 py-2 data-[state=active]:bg-muted"
                    >
                      <Wallet className="mr-2 h-4 w-4" />
                      Ganhos e Pagamentos
                    </TabsTrigger>
                    <TabsTrigger
                      value="settings"
                      className="w-full justify-start px-4 py-2 data-[state=active]:bg-muted"
                    >
                      <Settings className="mr-2 h-4 w-4" />
                      Configurações
                    </TabsTrigger>
                  </TabsList>
                </Tabs>
              </CardContent>
              <CardFooter className="border-t p-4">
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => navigate("/deliverer/dashboard")}
                >
                  <TrendingUp className="mr-2 h-4 w-4" />
                  Voltar ao Dashboard
                </Button>
              </CardFooter>
            </Card>
          </div>

          {/* Main Content */}
          <div className="w-full md:w-3/4">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsContent value="profile" className="mt-0">
                <DelivererProfileForm />
              </TabsContent>

              <TabsContent value="earnings" className="mt-0">
                <Card>
                  <CardHeader>
                    <CardTitle>Ganhos e Pagamentos</CardTitle>
                    <CardDescription>
                      Acompanhe seus ganhos e histórico de pagamentos
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">Disponível para Saque</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="text-2xl font-bold">R$ 245,80</div>
                            <div className="text-xs text-muted-foreground">Atualizado em 15/05/2023</div>
                          </CardContent>
                        </Card>
                        
                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">Ganhos do Mês</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="text-2xl font-bold">R$ 1.245,30</div>
                            <div className="text-xs text-muted-foreground">Maio/2023</div>
                          </CardContent>
                        </Card>
                        
                        <Card>
                          <CardHeader className="pb-2">
                            <CardTitle className="text-sm font-medium">Total de Entregas</CardTitle>
                          </CardHeader>
                          <CardContent>
                            <div className="text-2xl font-bold">32</div>
                            <div className="text-xs text-muted-foreground">Neste mês</div>
                          </CardContent>
                        </Card>
                      </div>
                      
                      <div>
                        <h3 className="text-lg font-medium mb-4">Histórico de Pagamentos</h3>
                        <div className="border rounded-md overflow-hidden">
                          <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                              <tr>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Data
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Valor
                                </th>
                                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Status
                                </th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              <tr>
                                <td className="px-6 py-4 whitespace-nowrap text-sm">
                                  10/05/2023
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                  R$ 320,50
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm">
                                  <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    Pago
                                  </span>
                                </td>
                              </tr>
                              <tr>
                                <td className="px-6 py-4 whitespace-nowrap text-sm">
                                  03/05/2023
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                  R$ 285,00
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm">
                                  <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    Pago
                                  </span>
                                </td>
                              </tr>
                              <tr>
                                <td className="px-6 py-4 whitespace-nowrap text-sm">
                                  25/04/2023
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                  R$ 310,20
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm">
                                  <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    Pago
                                  </span>
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button className="w-full bg-trust hover:bg-trust-dark">
                      Solicitar Saque
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>

              <TabsContent value="settings" className="mt-0">
                <Card>
                  <CardHeader>
                    <CardTitle>Configurações da Conta</CardTitle>
                    <CardDescription>
                      Gerencie as configurações da sua conta
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between border-b pb-4">
                        <div>
                          <p className="font-medium">Notificações por Email</p>
                          <p className="text-sm text-muted-foreground">Receber notificações por email</p>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="email-notifications"
                            className="h-4 w-4 rounded border-gray-300 text-trust focus:ring-trust"
                            defaultChecked
                          />
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between border-b pb-4">
                        <div>
                          <p className="font-medium">Notificações Push</p>
                          <p className="text-sm text-muted-foreground">Receber notificações push</p>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="push-notifications"
                            className="h-4 w-4 rounded border-gray-300 text-trust focus:ring-trust"
                            defaultChecked
                          />
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between border-b pb-4">
                        <div>
                          <p className="font-medium">Sons de Notificação</p>
                          <p className="text-sm text-muted-foreground">Ativar sons para novas entregas</p>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="sound-notifications"
                            className="h-4 w-4 rounded border-gray-300 text-trust focus:ring-trust"
                            defaultChecked
                          />
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">Modo de Férias</p>
                          <p className="text-sm text-muted-foreground">Desativar temporariamente sua conta</p>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="vacation-mode"
                            className="h-4 w-4 rounded border-gray-300 text-trust focus:ring-trust"
                          />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="flex flex-col space-y-2">
                    <Button className="w-full bg-trust hover:bg-trust-dark">
                      Salvar Configurações
                    </Button>
                    <Button variant="destructive" className="w-full">
                      Desativar Conta
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </main>
    </AnimatedPage>
  );
};

export default DelivererProfile;
