import { CartItem } from "@/types/cart";
import { Order } from "@/types/order";

// Interface for payment method
export interface PaymentMethod {
  id: string;
  type: "credit_card" | "debit_card" | "pix" | "cash";
  name: string;
  last4?: string;
  expiryDate?: string;
}

// Interface for checkout data
export interface CheckoutData {
  items: CartItem[];
  total: number;
  deliveryAddress: string;
  paymentMethod: PaymentMethod;
  notes?: string;
}

// Interface for checkout result
export interface CheckoutResult {
  success: boolean;
  order?: Order;
  error?: string;
}

// Mock payment methods
const mockPaymentMethods: PaymentMethod[] = [
  {
    id: "pm_1",
    type: "credit_card",
    name: "Visa",
    last4: "4242",
    expiryDate: "12/25"
  },
  {
    id: "pm_2",
    type: "credit_card",
    name: "Mastercard",
    last4: "5555",
    expiryDate: "10/24"
  },
  {
    id: "pm_3",
    type: "pix",
    name: "P<PERSON>"
  },
  {
    id: "pm_4",
    type: "cash",
    name: "<PERSON><PERSON><PERSON>"
  }
];

/**
 * Get saved payment methods for the user
 * @returns Promise with array of payment methods
 */
export async function getSavedPaymentMethods(): Promise<PaymentMethod[]> {
  try {
    // In a real app, this would call an API to get the user's saved payment methods

    // For now, return mock data
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(mockPaymentMethods);
      }, 500);
    });
  } catch (error) {
    console.error("Error getting saved payment methods:", error);
    throw error;
  }
}

/**
 * Process checkout
 * @param checkoutData Checkout data
 * @returns Promise with checkout result
 */
export async function processCheckout(checkoutData: CheckoutData): Promise<CheckoutResult> {
  try {
    // In a real app, this would call an API to process the payment and create the order

    // For now, simulate API call with a delay
    return new Promise((resolve) => {
      setTimeout(() => {
        // Simulate payment processing
        const paymentSuccessful = Math.random() > 0.1; // 90% success rate

        if (!paymentSuccessful) {
          resolve({
            success: false,
            error: "Falha no processamento do pagamento. Por favor, tente novamente."
          });
          return;
        }

        // Create order
        const order: Order = {
          id: `order_${Date.now()}`,
          items: checkoutData.items,
          total: checkoutData.total,
          status: "pending",
          createdAt: new Date().toISOString(),
          deliveryAddress: checkoutData.deliveryAddress,
          shopName: checkoutData.items[0].shopName, // Assume all items are from the same shop
          notes: checkoutData.notes
        };

        resolve({
          success: true,
          order
        });
      }, 2000);
    });
  } catch (error: any) {
    console.error("Error processing checkout:", error);
    return {
      success: false,
      error: error.message || "Ocorreu um erro ao processar o pagamento."
    };
  }
}

/**
 * Calculate delivery fee
 * @param items Cart items
 * @param deliveryAddress Delivery address
 * @returns Promise with delivery fee
 */
export async function calculateDeliveryFee(items: CartItem[], deliveryAddress: string): Promise<number> {
  try {
    // In a real app, this would call an API to calculate the delivery fee based on distance, etc.

    // For now, return a fixed fee
    return new Promise((resolve) => {
      setTimeout(() => {
        // Base fee
        let fee = 5.0;

        // Add a small random amount to simulate distance-based calculation
        fee += Math.random() * 5;

        // Round to 2 decimal places
        fee = Math.round(fee * 100) / 100;

        resolve(fee);
      }, 500);
    });
  } catch (error) {
    console.error("Error calculating delivery fee:", error);
    return 5.0; // Default fee
  }
}

/**
 * Add a new payment method
 * @param paymentMethod Payment method data
 * @returns Promise with the new payment method
 */
export async function addPaymentMethod(paymentMethod: Omit<PaymentMethod, "id">): Promise<PaymentMethod> {
  try {
    // In a real app, this would call an API to add the payment method

    // For now, simulate API call with a delay
    return new Promise((resolve) => {
      setTimeout(() => {
        const newPaymentMethod: PaymentMethod = {
          ...paymentMethod,
          id: `pm_${Date.now()}`
        };

        // In a real app, we would save this to the backend
        // For now, we'll just return the new payment method
        resolve(newPaymentMethod);
      }, 1000);
    });
  } catch (error) {
    console.error("Error adding payment method:", error);
    throw error;
  }
}

/**
 * Remove a payment method
 * @param paymentMethodId Payment method ID
 * @returns Promise with success status
 */
export async function removePaymentMethod(paymentMethodId: string): Promise<boolean> {
  try {
    // In a real app, this would call an API to remove the payment method

    // For now, simulate API call with a delay
    return new Promise((resolve) => {
      setTimeout(() => {
        // In a real app, we would delete this from the backend
        // For now, we'll just return success
        resolve(true);
      }, 1000);
    });
  } catch (error) {
    console.error(`Error removing payment method ${paymentMethodId}:`, error);
    throw error;
  }
}
