# Guia de Deploy do "Já Comprei" App

Este documento fornece instruções para fazer o deploy do aplicativo "Já Comprei" em diferentes plataformas.

## Pré-requisitos

- Node.js 18+ instalado
- NPM 9+ instalado
- Acesso às credenciais do Supabase (ou outro backend utilizado)

## Preparação para Deploy

1. Configure as variáveis de ambiente no arquivo `.env.production`:
   ```
   VITE_APP_ENV=production
   VITE_API_URL=https://api.jacomprei.com
   VITE_SUPABASE_URL=https://your-supabase-project.supabase.co
   VITE_SUPABASE_ANON_KEY=your-supabase-anon-key
   ```

2. Construa o aplicativo para produção:
   ```
   npm run build
   ```

3. Teste o build localmente:
   ```
   npm run preview
   ```

## Opções de Deploy

### 1. Vercel

A Vercel é uma plataforma de deploy ideal para aplicativos React.

1. Instale a CLI da Vercel:
   ```
   npm install -g vercel
   ```

2. Faça login na sua conta Vercel:
   ```
   vercel login
   ```

3. Deploy do aplicativo:
   ```
   vercel
   ```

4. Para deploy em produção:
   ```
   vercel --prod
   ```

### 2. Netlify

1. Instale a CLI do Netlify:
   ```
   npm install -g netlify-cli
   ```

2. Faça login na sua conta Netlify:
   ```
   netlify login
   ```

3. Inicialize o site:
   ```
   netlify init
   ```

4. Deploy do aplicativo:
   ```
   netlify deploy
   ```

5. Para deploy em produção:
   ```
   netlify deploy --prod
   ```

### 3. Firebase Hosting

1. Instale a CLI do Firebase:
   ```
   npm install -g firebase-tools
   ```

2. Faça login na sua conta Google:
   ```
   firebase login
   ```

3. Inicialize o projeto Firebase:
   ```
   firebase init hosting
   ```
   - Selecione a pasta `dist` como diretório público
   - Configure como SPA (Single Page Application)

4. Deploy do aplicativo:
   ```
   firebase deploy --only hosting
   ```

### 4. AWS Amplify

1. Instale a CLI do Amplify:
   ```
   npm install -g @aws-amplify/cli
   ```

2. Configure o Amplify:
   ```
   amplify configure
   ```

3. Inicialize o projeto Amplify:
   ```
   amplify init
   ```

4. Adicione hospedagem:
   ```
   amplify add hosting
   ```

5. Deploy do aplicativo:
   ```
   amplify publish
   ```

### 5. GitHub Pages

1. Instale o pacote gh-pages:
   ```
   npm install --save-dev gh-pages
   ```

2. Adicione os seguintes scripts ao package.json:
   ```json
   "scripts": {
     "predeploy": "npm run build",
     "deploy": "gh-pages -d dist"
   }
   ```

3. Deploy do aplicativo:
   ```
   npm run deploy
   ```

## Configuração de Domínio Personalizado

Após o deploy, você pode configurar um domínio personalizado (como jacomprei.com) em qualquer uma das plataformas acima. Cada plataforma tem seu próprio processo para configuração de domínio:

1. **Vercel**: Vá para as configurações do projeto > Domains > Add Domain
2. **Netlify**: Vá para as configurações do site > Domain management > Add custom domain
3. **Firebase**: Vá para o console do Firebase > Hosting > Connect domain
4. **AWS Amplify**: Vá para o console do Amplify > App settings > Domain management
5. **GitHub Pages**: Configure o CNAME no repositório

## Considerações para Produção

1. **SSL/HTTPS**: Certifique-se de que seu site está usando HTTPS para segurança.
2. **CDN**: Todas as plataformas mencionadas incluem CDN para melhor performance.
3. **Cache**: Configure cabeçalhos de cache apropriados para recursos estáticos.
4. **Monitoramento**: Implemente ferramentas de monitoramento como Google Analytics ou Sentry.

## Suporte e Manutenção

Após o deploy, monitore regularmente:
- Performance do aplicativo
- Erros reportados pelos usuários
- Atualizações de segurança necessárias

Para atualizações futuras, siga o mesmo processo de build e deploy.
