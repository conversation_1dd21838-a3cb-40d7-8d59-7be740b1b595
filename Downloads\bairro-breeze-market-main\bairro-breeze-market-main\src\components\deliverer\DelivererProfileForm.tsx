import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  Card<PERSON>ooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  Avatar, 
  AvatarFallback, 
  AvatarImage 
} from "@/components/ui/avatar";
import { 
  User, 
  Phone, 
  MapPin, 
  Upload, 
  Loader2,
  Truck,
  Calendar,
  Clock,
  Bike,
  Car,
  Motorcycle
} from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

interface DelivererProfile {
  id: string;
  name: string;
  phone: string;
  address: string;
  vehicleType: "bike" | "motorcycle" | "car";
  licenseNumber?: string;
  licensePlate?: string;
  workingHours: {
    monday: { start: string; end: string; isWorking: boolean };
    tuesday: { start: string; end: string; isWorking: boolean };
    wednesday: { start: string; end: string; isWorking: boolean };
    thursday: { start: string; end: string; isWorking: boolean };
    friday: { start: string; end: string; isWorking: boolean };
    saturday: { start: string; end: string; isWorking: boolean };
    sunday: { start: string; end: string; isWorking: boolean };
  };
  avatarUrl?: string;
  bankInfo?: {
    bankName: string;
    accountType: string;
    accountNumber: string;
    agency: string;
  };
}

// Mock function to fetch deliverer profile
const fetchDelivererProfile = async (userId: string): Promise<DelivererProfile> => {
  // Simulate API call
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        id: userId,
        name: "Carlos Entregador",
        phone: "(11) 99999-9999",
        address: "Rua das Entregas, 123, Centro, São Paulo, SP",
        vehicleType: "motorcycle",
        licenseNumber: "***********",
        licensePlate: "ABC1234",
        workingHours: {
          monday: { start: "08:00", end: "18:00", isWorking: true },
          tuesday: { start: "08:00", end: "18:00", isWorking: true },
          wednesday: { start: "08:00", end: "18:00", isWorking: true },
          thursday: { start: "08:00", end: "18:00", isWorking: true },
          friday: { start: "08:00", end: "18:00", isWorking: true },
          saturday: { start: "09:00", end: "15:00", isWorking: true },
          sunday: { start: "09:00", end: "15:00", isWorking: false },
        },
        avatarUrl: "",
        bankInfo: {
          bankName: "Banco do Brasil",
          accountType: "Corrente",
          accountNumber: "12345-6",
          agency: "1234",
        },
      });
    }, 1000);
  });
};

// Mock function to update deliverer profile
const updateDelivererProfile = async (userId: string, data: Partial<DelivererProfile>): Promise<DelivererProfile> => {
  // Simulate API call
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        id: userId,
        ...data,
      } as DelivererProfile);
    }, 1000);
  });
};

export function DelivererProfileForm() {
  const { user } = useAuth();
  
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [profile, setProfile] = useState<DelivererProfile | null>(null);
  
  // Form state
  const [name, setName] = useState("");
  const [phone, setPhone] = useState("");
  const [address, setAddress] = useState("");
  const [vehicleType, setVehicleType] = useState<"bike" | "motorcycle" | "car">("motorcycle");
  const [licenseNumber, setLicenseNumber] = useState("");
  const [licensePlate, setLicensePlate] = useState("");
  const [workingHours, setWorkingHours] = useState<DelivererProfile["workingHours"] | null>(null);
  const [bankName, setBankName] = useState("");
  const [accountType, setAccountType] = useState("");
  const [accountNumber, setAccountNumber] = useState("");
  const [agency, setAgency] = useState("");
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  
  // Load deliverer profile
  useEffect(() => {
    const loadProfile = async () => {
      if (!user) return;
      
      setIsLoading(true);
      
      try {
        const delivererProfile = await fetchDelivererProfile(user.id);
        
        if (delivererProfile) {
          setProfile(delivererProfile);
          setName(delivererProfile.name || "");
          setPhone(delivererProfile.phone || "");
          setAddress(delivererProfile.address || "");
          setVehicleType(delivererProfile.vehicleType || "motorcycle");
          setLicenseNumber(delivererProfile.licenseNumber || "");
          setLicensePlate(delivererProfile.licensePlate || "");
          setWorkingHours(delivererProfile.workingHours || null);
          setAvatarPreview(delivererProfile.avatarUrl || null);
          
          if (delivererProfile.bankInfo) {
            setBankName(delivererProfile.bankInfo.bankName || "");
            setAccountType(delivererProfile.bankInfo.accountType || "");
            setAccountNumber(delivererProfile.bankInfo.accountNumber || "");
            setAgency(delivererProfile.bankInfo.agency || "");
          }
        }
      } catch (error) {
        console.error("Error loading deliverer profile:", error);
        toast({
          title: "Erro ao carregar perfil",
          description: "Não foi possível carregar suas informações. Tente novamente mais tarde.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    loadProfile();
  }, [user]);
  
  // Handle avatar change
  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    
    if (file) {
      // Check file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: "Arquivo muito grande",
          description: "O tamanho máximo permitido é 5MB.",
          variant: "destructive",
        });
        return;
      }
      
      // Check file type
      if (!file.type.startsWith("image/")) {
        toast({
          title: "Tipo de arquivo inválido",
          description: "Por favor, selecione uma imagem.",
          variant: "destructive",
        });
        return;
      }
      
      setAvatarFile(file);
      
      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setAvatarPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };
  
  // Handle working hours change
  const handleWorkingHoursChange = (
    day: keyof DelivererProfile["workingHours"],
    field: "start" | "end" | "isWorking",
    value: string | boolean
  ) => {
    if (!workingHours) return;
    
    setWorkingHours({
      ...workingHours,
      [day]: {
        ...workingHours[day],
        [field]: value
      }
    });
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user) {
      toast({
        title: "Erro",
        description: "Você precisa estar logado para atualizar seu perfil.",
        variant: "destructive",
      });
      return;
    }
    
    setIsSaving(true);
    
    try {
      // Update profile
      const updatedProfile = await updateDelivererProfile(user.id, {
        name,
        phone,
        address,
        vehicleType,
        licenseNumber,
        licensePlate,
        workingHours: workingHours || undefined,
        bankInfo: {
          bankName,
          accountType,
          accountNumber,
          agency,
        },
      });
      
      if (updatedProfile) {
        setProfile(updatedProfile);
        
        toast({
          title: "Perfil atualizado",
          description: "Suas informações foram atualizadas com sucesso.",
        });
      }
    } catch (error) {
      console.error("Error updating deliverer profile:", error);
      toast({
        title: "Erro ao atualizar perfil",
        description: "Não foi possível atualizar suas informações. Tente novamente mais tarde.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };
  
  // Get initials for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };
  
  // Get vehicle icon
  const getVehicleIcon = (type: "bike" | "motorcycle" | "car") => {
    switch (type) {
      case "bike":
        return <Bike className="h-5 w-5" />;
      case "motorcycle":
        return <Motorcycle className="h-5 w-5" />;
      case "car":
        return <Car className="h-5 w-5" />;
      default:
        return <Truck className="h-5 w-5" />;
    }
  };
  
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      <form onSubmit={handleSubmit}>
        <Card>
          <CardHeader>
            <CardTitle>Informações Pessoais</CardTitle>
            <CardDescription>
              Atualize suas informações pessoais
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Avatar */}
            <div className="flex flex-col items-center">
              <Avatar className="h-24 w-24 mb-4">
                <AvatarImage src={avatarPreview || undefined} />
                <AvatarFallback>{getInitials(name || "User")}</AvatarFallback>
              </Avatar>
              <div className="flex items-center">
                <input
                  type="file"
                  id="avatar"
                  className="hidden"
                  accept="image/*"
                  onChange={handleAvatarChange}
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => document.getElementById("avatar")?.click()}
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Alterar foto
                </Button>
              </div>
            </div>
            
            {/* Basic Info */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name" className="flex items-center">
                  <User className="h-4 w-4 mr-2 text-muted-foreground" />
                  Nome completo
                </Label>
                <Input
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="Seu nome completo"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="phone" className="flex items-center">
                  <Phone className="h-4 w-4 mr-2 text-muted-foreground" />
                  Telefone
                </Label>
                <Input
                  id="phone"
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                  placeholder="(00) 00000-0000"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="address" className="flex items-center">
                  <MapPin className="h-4 w-4 mr-2 text-muted-foreground" />
                  Endereço
                </Label>
                <Textarea
                  id="address"
                  value={address}
                  onChange={(e) => setAddress(e.target.value)}
                  placeholder="Rua, número, complemento, bairro, cidade, estado, CEP"
                  rows={3}
                />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Informações do Veículo</CardTitle>
            <CardDescription>
              Informe os detalhes do seu veículo
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Tipo de veículo</Label>
                <RadioGroup
                  value={vehicleType}
                  onValueChange={(value) => setVehicleType(value as "bike" | "motorcycle" | "car")}
                  className="grid grid-cols-3 gap-4"
                >
                  <div className={`border rounded-lg p-4 cursor-pointer ${vehicleType === "bike" ? "border-trust bg-trust/5" : ""}`}>
                    <RadioGroupItem value="bike" id="bike" className="sr-only" />
                    <Label htmlFor="bike" className="flex flex-col items-center cursor-pointer">
                      <Bike className={`h-8 w-8 mb-2 ${vehicleType === "bike" ? "text-trust" : "text-muted-foreground"}`} />
                      <span className="font-medium">Bicicleta</span>
                    </Label>
                  </div>
                  
                  <div className={`border rounded-lg p-4 cursor-pointer ${vehicleType === "motorcycle" ? "border-trust bg-trust/5" : ""}`}>
                    <RadioGroupItem value="motorcycle" id="motorcycle" className="sr-only" />
                    <Label htmlFor="motorcycle" className="flex flex-col items-center cursor-pointer">
                      <Motorcycle className={`h-8 w-8 mb-2 ${vehicleType === "motorcycle" ? "text-trust" : "text-muted-foreground"}`} />
                      <span className="font-medium">Moto</span>
                    </Label>
                  </div>
                  
                  <div className={`border rounded-lg p-4 cursor-pointer ${vehicleType === "car" ? "border-trust bg-trust/5" : ""}`}>
                    <RadioGroupItem value="car" id="car" className="sr-only" />
                    <Label htmlFor="car" className="flex flex-col items-center cursor-pointer">
                      <Car className={`h-8 w-8 mb-2 ${vehicleType === "car" ? "text-trust" : "text-muted-foreground"}`} />
                      <span className="font-medium">Carro</span>
                    </Label>
                  </div>
                </RadioGroup>
              </div>
              
              {(vehicleType === "motorcycle" || vehicleType === "car") && (
                <>
                  <div className="space-y-2">
                    <Label htmlFor="licenseNumber" className="flex items-center">
                      <User className="h-4 w-4 mr-2 text-muted-foreground" />
                      Número da CNH
                    </Label>
                    <Input
                      id="licenseNumber"
                      value={licenseNumber}
                      onChange={(e) => setLicenseNumber(e.target.value)}
                      placeholder="Número da sua CNH"
                      required={vehicleType === "motorcycle" || vehicleType === "car"}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="licensePlate" className="flex items-center">
                      <Truck className="h-4 w-4 mr-2 text-muted-foreground" />
                      Placa do veículo
                    </Label>
                    <Input
                      id="licensePlate"
                      value={licensePlate}
                      onChange={(e) => setLicensePlate(e.target.value)}
                      placeholder="ABC1234"
                      required={vehicleType === "motorcycle" || vehicleType === "car"}
                    />
                  </div>
                </>
              )}
            </div>
          </CardContent>
        </Card>
        
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Horário de Trabalho</CardTitle>
            <CardDescription>
              Configure seus horários de disponibilidade
            </CardDescription>
          </CardHeader>
          <CardContent>
            {workingHours && (
              <div className="space-y-4">
                {Object.entries(workingHours).map(([day, hours]) => (
                  <div key={day} className="flex items-center justify-between border-b pb-4 last:border-0 last:pb-0">
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                      <span className="font-medium">
                        {day === "monday" && "Segunda-feira"}
                        {day === "tuesday" && "Terça-feira"}
                        {day === "wednesday" && "Quarta-feira"}
                        {day === "thursday" && "Quinta-feira"}
                        {day === "friday" && "Sexta-feira"}
                        {day === "saturday" && "Sábado"}
                        {day === "sunday" && "Domingo"}
                      </span>
                    </div>
                    
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id={`working-${day}`}
                          checked={hours.isWorking}
                          onChange={(e) => 
                            handleWorkingHoursChange(
                              day as keyof DelivererProfile["workingHours"],
                              "isWorking",
                              e.target.checked
                            )
                          }
                          className="h-4 w-4 rounded border-gray-300 text-trust focus:ring-trust"
                        />
                        <Label htmlFor={`working-${day}`} className="text-sm">
                          {hours.isWorking ? "Disponível" : "Indisponível"}
                        </Label>
                      </div>
                      
                      {hours.isWorking && (
                        <div className="flex items-center space-x-2">
                          <Input
                            type="time"
                            value={hours.start}
                            onChange={(e) => 
                              handleWorkingHoursChange(
                                day as keyof DelivererProfile["workingHours"],
                                "start",
                                e.target.value
                              )
                            }
                            className="w-24"
                          />
                          <span>às</span>
                          <Input
                            type="time"
                            value={hours.end}
                            onChange={(e) => 
                              handleWorkingHoursChange(
                                day as keyof DelivererProfile["workingHours"],
                                "end",
                                e.target.value
                              )
                            }
                            className="w-24"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
        
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Informações Bancárias</CardTitle>
            <CardDescription>
              Informe seus dados bancários para recebimento
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="bankName">Nome do banco</Label>
              <Input
                id="bankName"
                value={bankName}
                onChange={(e) => setBankName(e.target.value)}
                placeholder="Ex: Banco do Brasil, Itaú, Nubank"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="accountType">Tipo de conta</Label>
              <Select
                value={accountType}
                onValueChange={setAccountType}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione o tipo de conta" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="corrente">Conta Corrente</SelectItem>
                  <SelectItem value="poupanca">Conta Poupança</SelectItem>
                  <SelectItem value="pagamento">Conta de Pagamento</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="agency">Agência</Label>
                <Input
                  id="agency"
                  value={agency}
                  onChange={(e) => setAgency(e.target.value)}
                  placeholder="Número da agência"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="accountNumber">Número da conta</Label>
                <Input
                  id="accountNumber"
                  value={accountNumber}
                  onChange={(e) => setAccountNumber(e.target.value)}
                  placeholder="Número da conta com dígito"
                />
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                // Reset form to original values
                if (profile) {
                  setName(profile.name || "");
                  setPhone(profile.phone || "");
                  setAddress(profile.address || "");
                  setVehicleType(profile.vehicleType || "motorcycle");
                  setLicenseNumber(profile.licenseNumber || "");
                  setLicensePlate(profile.licensePlate || "");
                  setWorkingHours(profile.workingHours || null);
                  setAvatarPreview(profile.avatarUrl || null);
                  setAvatarFile(null);
                  
                  if (profile.bankInfo) {
                    setBankName(profile.bankInfo.bankName || "");
                    setAccountType(profile.bankInfo.accountType || "");
                    setAccountNumber(profile.bankInfo.accountNumber || "");
                    setAgency(profile.bankInfo.agency || "");
                  }
                }
              }}
            >
              Cancelar
            </Button>
            <Button
              type="submit"
              className="bg-trust hover:bg-trust-dark"
              disabled={isSaving}
            >
              {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Salvando...
                </>
              ) : (
                "Salvar Alterações"
              )}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </div>
  );
}
