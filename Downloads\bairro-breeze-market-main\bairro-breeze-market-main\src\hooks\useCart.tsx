import { createContext, useContext, useState, useEffect, ReactNode, useCallback } from 'react';
import { Product } from '@/types/product';
import { toast } from '@/hooks/use-toast';
import { formatCurrency } from '@/lib/utils';

export interface CartItem {
  id: string;
  name: string;
  price: number;
  originalPrice?: number;
  quantity: number;
  image: string;
  shopId: string;
  shopName: string;
  category?: string;
  maxQuantity?: number;
}

interface CartContextType {
  items: CartItem[];
  addItem: (product: Product, quantity: number) => void;
  removeItem: (id: string) => void;
  updateQuantity: (id: string, quantity: number) => void;
  clearCart: () => void;
  totalItems: number;
  subtotal: number;
  shopName: string | null;
  canAddProduct: (product: Product) => { canAdd: boolean; reason?: string };
  hasItems: boolean;
  discount: number;
  deliveryFee: number;
  total: number;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export function CartProvider({ children }: { children: ReactNode }) {
  const [items, setItems] = useState<CartItem[]>([]);

  // Load cart from localStorage on initial render
  useEffect(() => {
    const savedCart = localStorage.getItem('jaComprei:cart');
    if (savedCart) {
      try {
        setItems(JSON.parse(savedCart));
      } catch (error) {
        console.error('Erro ao carregar carrinho:', error);
        localStorage.removeItem('jaComprei:cart');
      }
    }
  }, []);

  // Save cart to localStorage whenever it changes, com debounce para melhorar performance
  useEffect(() => {
    const saveCartToStorage = () => {
      try {
        localStorage.setItem('jaComprei:cart', JSON.stringify(items));
      } catch (error) {
        console.error('Erro ao salvar carrinho:', error);
      }
    };

    // Debounce para evitar múltiplas gravações em sequência rápida
    const timeoutId = setTimeout(saveCartToStorage, 300);

    return () => clearTimeout(timeoutId);
  }, [items]);

  // Verificar se um produto pode ser adicionado ao carrinho
  const canAddProduct = useCallback((product: Product): { canAdd: boolean; reason?: string } => {
    // Se o carrinho estiver vazio, qualquer produto pode ser adicionado
    if (items.length === 0) {
      return { canAdd: true };
    }

    // Verificar se o produto é da mesma loja que os itens no carrinho
    const cartShopId = items[0].shopId;
    if (product.shopId !== cartShopId) {
      return {
        canAdd: false,
        reason: `Seu carrinho já contém itens de ${items[0].shopName}. Finalize a compra atual ou esvazie o carrinho para adicionar itens de outra loja.`
      };
    }

    return { canAdd: true };
  }, [items]);

  // Adicionar item ao carrinho
  const addItem = useCallback((product: Product, quantity: number) => {
    // Validação básica
    if (!product || !product.id || quantity <= 0) {
      toast({
        title: "Erro ao adicionar produto",
        description: "Dados do produto inválidos ou quantidade incorreta",
        variant: "destructive"
      });
      return;
    }

    // Verificar se o produto pode ser adicionado
    const { canAdd, reason } = canAddProduct(product);

    if (!canAdd) {
      toast({
        title: "Não foi possível adicionar o produto",
        description: reason,
        variant: "destructive"
      });
      return;
    }

    // Verificar quantidade máxima disponível (se aplicável)
    if (product.stock !== undefined && quantity > product.stock) {
      toast({
        title: "Quantidade indisponível",
        description: `Apenas ${product.stock} unidades disponíveis.`,
        variant: "destructive"
      });
      return;
    }

    setItems(currentItems => {
      // Verificar se o item já existe no carrinho
      const existingItemIndex = currentItems.findIndex(item => item.id === product.id);

      if (existingItemIndex > -1) {
        // Atualizar quantidade se o item existir
        const updatedItems = [...currentItems];
        const newQuantity = updatedItems[existingItemIndex].quantity + quantity;

        // Verificar se a nova quantidade excede o estoque
        if (product.stock !== undefined && newQuantity > product.stock) {
          toast({
            title: "Quantidade indisponível",
            description: `Apenas ${product.stock} unidades disponíveis.`,
            variant: "destructive"
          });
          return currentItems;
        }

        updatedItems[existingItemIndex].quantity = newQuantity;

        toast({
          title: "Produto atualizado",
          description: `Quantidade de ${product.name} atualizada para ${newQuantity}.`,
        });

        return updatedItems;
      } else {
        // Adicionar novo item se não existir
        toast({
          title: "Produto adicionado",
          description: `${product.name} adicionado ao carrinho.`,
        });

        return [...currentItems, {
          id: product.id,
          name: product.name,
          price: product.price,
          originalPrice: product.originalPrice,
          quantity: quantity,
          image: product.image,
          shopId: product.shopId,
          shopName: product.shopName,
          category: product.category,
          maxQuantity: product.stock
        }];
      }
    });
  }, [canAddProduct, toast]);

  const removeItem = useCallback((id: string) => {
    if (!id) return;

    setItems(currentItems => {
      const itemToRemove = currentItems.find(item => item.id === id);
      const updatedItems = currentItems.filter(item => item.id !== id);

      if (itemToRemove) {
        toast({
          title: "Produto removido",
          description: `${itemToRemove.name} removido do carrinho.`,
        });
      }

      return updatedItems;
    });
  }, [toast]);

  const updateQuantity = useCallback((id: string, quantity: number) => {
    // Validação básica
    if (!id || quantity < 1) return;

    setItems(currentItems => {
      const item = currentItems.find(item => item.id === id);

      // Se o item não for encontrado, retornar o estado atual
      if (!item) return currentItems;

      // Verificar se a quantidade excede o máximo disponível
      if (item.maxQuantity !== undefined && quantity > item.maxQuantity) {
        toast({
          title: "Quantidade indisponível",
          description: `Apenas ${item.maxQuantity} unidades disponíveis.`,
          variant: "destructive"
        });
        return currentItems.map(i =>
          i.id === id ? { ...i, quantity: item.maxQuantity } : i
        );
      }

      // Atualizar a quantidade apenas se for diferente
      if (item.quantity === quantity) return currentItems;

      return currentItems.map(i =>
        i.id === id ? { ...i, quantity } : i
      );
    });
  }, [toast]);

  const clearCart = useCallback(() => {
    setItems([]);
    toast({
      title: "Carrinho limpo",
      description: "Todos os itens foram removidos do carrinho.",
    });
  }, [toast]);

  // Calcular valores
  const hasItems = items.length > 0;
  const totalItems = items.reduce((total, item) => total + item.quantity, 0);
  const subtotal = items.reduce((total, item) => total + item.price * item.quantity, 0);

  // Taxa de entrega fixa (em uma aplicação real, seria calculada com base na distância)
  const deliveryFee = hasItems ? 5.0 : 0;

  // Calcular desconto (se houver produtos em promoção)
  const hasPromoItems = items.some(item => item.originalPrice && item.originalPrice > item.price);
  const discount = hasPromoItems ? 4.0 : 0;

  // Total final
  const total = subtotal + deliveryFee - discount;

  // Nome da loja (todos os itens são da mesma loja)
  const shopName = hasItems ? items[0].shopName : null;

  return (
    <CartContext.Provider value={{
      items,
      addItem,
      removeItem,
      updateQuantity,
      clearCart,
      totalItems,
      subtotal,
      shopName,
      canAddProduct,
      hasItems,
      discount,
      deliveryFee,
      total
    }}>
      {children}
    </CartContext.Provider>
  );
}

export function useCart() {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
}
