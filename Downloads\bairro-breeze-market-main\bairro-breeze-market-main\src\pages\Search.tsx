import { useState, useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import { Navbar } from "@/components/ui/navbar";
import { BottomNav } from "@/components/ui/bottom-nav";
import { SearchBar, SearchFilters } from "@/components/ui/search-bar";
import { ProductCard } from "@/components/products/ProductCard";
import { ShopCard } from "@/components/shops/ShopCard";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import {
  Search as SearchIcon,
  ShoppingBag,
  Store,
  Filter as FilterIcon,
  X,
  Clock,
  Star,
  DollarSign,
  MapPin,
  Utensils
} from "lucide-react";
import { motion } from "framer-motion";
import { AnimatedPage, AnimatedList, FadeIn } from "@/components/animations";
import { search, getPopularSearchTerms, getRecentSearchTerms, saveSearchTerm } from "@/services/search";
import { fetchCategories } from "@/services/categories";
import { useAuth } from "@/hooks/useAuth";
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";

const SearchPage = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const initialQuery = searchParams.get("q") || "";

  const [searchQuery, setSearchQuery] = useState(initialQuery);
  const [activeTab, setActiveTab] = useState<"products" | "shops">("products");
  const [isLoading, setIsLoading] = useState(false);
  const [products, setProducts] = useState([]);
  const [shops, setShops] = useState([]);
  const [popularSearches, setPopularSearches] = useState<string[]>([]);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);
  const [categories, setCategories] = useState([]);
  const [filters, setFilters] = useState<SearchFilters>({});
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 200]);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [activeFilters, setActiveFilters] = useState<string[]>([]);

  const { user } = useAuth();

  // Load initial data
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        const [categoriesData, popularTerms] = await Promise.all([
          fetchCategories(),
          getPopularSearchTerms()
        ]);

        setCategories(categoriesData);
        setPopularSearches(popularTerms);

        if (user) {
          const recentTerms = await getRecentSearchTerms(user.id);
          setRecentSearches(recentTerms);
        }
      } catch (error) {
        console.error("Error loading initial data:", error);
      }
    };

    loadInitialData();
  }, [user]);

  // Perform search when query changes
  useEffect(() => {
    if (initialQuery) {
      handleSearch(initialQuery);
    }
  }, [initialQuery]);

  const handleSearch = async (query: string, searchFilters?: SearchFilters) => {
    if (!query.trim()) return;

    setIsLoading(true);
    setSearchQuery(query);

    try {
      // Update URL
      setSearchParams({ q: query });

      // Save search term
      if (user) {
        await saveSearchTerm(user.id, query);
      }

      // Perform search
      const results = await search(query);

      setProducts(results.products);
      setShops(results.shops);

      // Apply filters if provided
      if (searchFilters) {
        setFilters(searchFilters);
        applyFilters(results.products, results.shops, searchFilters);
      }
    } catch (error) {
      console.error("Error searching:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const applyFilters = (productsList, shopsList, currentFilters: SearchFilters) => {
    let filteredProducts = [...productsList];
    let filteredShops = [...shopsList];

    // Track active filters
    const newActiveFilters: string[] = [];

    // Apply category filter
    if (currentFilters.categories && currentFilters.categories.length > 0) {
      filteredProducts = filteredProducts.filter(product =>
        currentFilters.categories.includes(product.category)
      );

      filteredShops = filteredShops.filter(shop =>
        currentFilters.categories.includes(shop.category)
      );

      newActiveFilters.push("Categorias");
    }

    // Apply price range filter
    if (currentFilters.priceRange) {
      const [min, max] = currentFilters.priceRange;
      filteredProducts = filteredProducts.filter(product =>
        product.price >= min && product.price <= max
      );

      newActiveFilters.push("Preço");
    }

    // Apply sort
    if (currentFilters.sortBy) {
      switch (currentFilters.sortBy) {
        case 'price-asc':
          filteredProducts.sort((a, b) => a.price - b.price);
          filteredShops.sort((a, b) => a.deliveryFee - b.deliveryFee);
          newActiveFilters.push("Preço: Menor para maior");
          break;
        case 'price-desc':
          filteredProducts.sort((a, b) => b.price - a.price);
          filteredShops.sort((a, b) => b.deliveryFee - a.deliveryFee);
          newActiveFilters.push("Preço: Maior para menor");
          break;
        case 'rating':
          filteredShops.sort((a, b) => b.rating - a.rating);
          newActiveFilters.push("Melhor avaliados");
          break;
        case 'distance':
          // In a real app, this would sort by actual distance
          newActiveFilters.push("Mais próximos");
          break;
        case 'delivery-time':
          // In a real app, this would sort by actual delivery time
          filteredShops.sort((a, b) => {
            const aTime = parseInt(a.deliveryTime.split('-')[0]);
            const bTime = parseInt(b.deliveryTime.split('-')[0]);
            return aTime - bTime;
          });
          newActiveFilters.push("Menor tempo de entrega");
          break;
      }
    }

    // Apply promotion filter
    if (currentFilters.onlyPromotions) {
      filteredProducts = filteredProducts.filter(product => product.isPromo);
      newActiveFilters.push("Promoções");
    }

    setProducts(filteredProducts);
    setShops(filteredShops);
    setActiveFilters(newActiveFilters);
  };

  const handleFilterChange = (newFilters: SearchFilters) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);

    if (searchQuery) {
      handleSearch(searchQuery, updatedFilters);
    }
  };

  const handlePriceRangeChange = (value: number[]) => {
    setPriceRange([value[0], value[1]]);
  };

  const applyPriceRange = () => {
    handleFilterChange({ ...filters, priceRange });
  };

  const clearFilters = () => {
    setFilters({});
    setPriceRange([0, 200]);
    setActiveFilters([]);

    if (searchQuery) {
      handleSearch(searchQuery, {});
    }
  };

  const handleCategoryToggle = (category: string) => {
    const currentCategories = filters.categories || [];
    let newCategories: string[];

    if (currentCategories.includes(category)) {
      newCategories = currentCategories.filter(c => c !== category);
    } else {
      newCategories = [...currentCategories, category];
    }

    handleFilterChange({ ...filters, categories: newCategories });
  };

  const handlePromotionsToggle = () => {
    handleFilterChange({
      ...filters,
      onlyPromotions: !filters.onlyPromotions
    });
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  return (
    <AnimatedPage>
      <Navbar />

      <main className="flex-1 container px-4 py-6 max-w-5xl mx-auto">
        <div className="mb-6">
          <SearchBar
            onSearch={handleSearch}
            initialValue={searchQuery}
          />
        </div>

        {searchQuery ? (
          <>
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <h1 className="text-xl font-bold">Resultados para "{searchQuery}"</h1>
                <span className="ml-2 text-sm text-muted-foreground">
                  {products.length + shops.length} resultados
                </span>
              </div>

              <div className="flex items-center gap-2">
                {activeFilters.length > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={clearFilters}
                    className="text-xs"
                  >
                    Limpar filtros
                    <X className="ml-1 h-3 w-3" />
                  </Button>
                )}

                <Sheet open={isFilterOpen} onOpenChange={setIsFilterOpen}>
                  <SheetTrigger asChild>
                    <Button variant="outline" size="sm" className="flex items-center gap-1">
                      <FilterIcon className="h-4 w-4" />
                      Filtrar
                    </Button>
                  </SheetTrigger>
                  <SheetContent side="right" className="w-[300px] sm:w-[400px]">
                    <SheetHeader>
                      <SheetTitle>Filtros</SheetTitle>
                    </SheetHeader>
                    <ScrollArea className="h-[calc(100vh-120px)] pr-4">
                      <div className="py-6 space-y-6">
                        {/* Sort Options */}
                        <div className="space-y-4">
                          <h3 className="font-medium flex items-center">
                            <DollarSign className="h-4 w-4 mr-2 text-muted-foreground" />
                            Ordenar por
                          </h3>
                          <div className="grid grid-cols-1 gap-2">
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                id="sort-price-asc"
                                checked={filters.sortBy === 'price-asc'}
                                onCheckedChange={() => handleFilterChange({ sortBy: 'price-asc' })}
                              />
                              <Label htmlFor="sort-price-asc">Preço: Menor para maior</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                id="sort-price-desc"
                                checked={filters.sortBy === 'price-desc'}
                                onCheckedChange={() => handleFilterChange({ sortBy: 'price-desc' })}
                              />
                              <Label htmlFor="sort-price-desc">Preço: Maior para menor</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                id="sort-rating"
                                checked={filters.sortBy === 'rating'}
                                onCheckedChange={() => handleFilterChange({ sortBy: 'rating' })}
                              />
                              <Label htmlFor="sort-rating">Melhor avaliados</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                id="sort-distance"
                                checked={filters.sortBy === 'distance'}
                                onCheckedChange={() => handleFilterChange({ sortBy: 'distance' })}
                              />
                              <Label htmlFor="sort-distance">Mais próximos</Label>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Checkbox
                                id="sort-delivery-time"
                                checked={filters.sortBy === 'delivery-time'}
                                onCheckedChange={() => handleFilterChange({ sortBy: 'delivery-time' })}
                              />
                              <Label htmlFor="sort-delivery-time">Menor tempo de entrega</Label>
                            </div>
                          </div>
                        </div>

                        <Separator />

                        {/* Price Range */}
                        <div className="space-y-4">
                          <h3 className="font-medium flex items-center">
                            <DollarSign className="h-4 w-4 mr-2 text-muted-foreground" />
                            Faixa de Preço
                          </h3>
                          <div className="space-y-6">
                            <Slider
                              defaultValue={[0, 200]}
                              max={200}
                              step={1}
                              value={priceRange}
                              onValueChange={handlePriceRangeChange}
                              onValueCommit={applyPriceRange}
                              className="py-4"
                            />
                            <div className="flex items-center justify-between">
                              <span>{formatCurrency(priceRange[0])}</span>
                              <span>{formatCurrency(priceRange[1])}</span>
                            </div>
                          </div>
                        </div>

                        <Separator />

                        {/* Categories */}
                        <div className="space-y-4">
                          <h3 className="font-medium flex items-center">
                            <ShoppingBag className="h-4 w-4 mr-2 text-muted-foreground" />
                            Categorias
                          </h3>
                          <div className="grid grid-cols-1 gap-2">
                            {categories.map((category) => (
                              <div key={category.id} className="flex items-center space-x-2">
                                <Checkbox
                                  id={`category-${category.id}`}
                                  checked={(filters.categories || []).includes(category.name)}
                                  onCheckedChange={() => handleCategoryToggle(category.name)}
                                />
                                <Label htmlFor={`category-${category.id}`}>{category.name}</Label>
                              </div>
                            ))}
                          </div>
                        </div>

                        <Separator />

                        {/* Promotions */}
                        <div className="space-y-4">
                          <h3 className="font-medium flex items-center">
                            <Star className="h-4 w-4 mr-2 text-muted-foreground" />
                            Promoções
                          </h3>
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="only-promotions"
                              checked={filters.onlyPromotions}
                              onCheckedChange={handlePromotionsToggle}
                            />
                            <Label htmlFor="only-promotions">Mostrar apenas promoções</Label>
                          </div>
                        </div>

                        <div className="pt-4 flex gap-2">
                          <Button
                            variant="outline"
                            className="flex-1"
                            onClick={clearFilters}
                          >
                            Limpar
                          </Button>
                          <Button
                            className="flex-1 bg-cta hover:bg-cta-dark"
                            onClick={() => setIsFilterOpen(false)}
                          >
                            Aplicar
                          </Button>
                        </div>
                      </div>
                    </ScrollArea>
                  </SheetContent>
                </Sheet>
              </div>
            </div>

            {/* Active filters */}
            {activeFilters.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-4">
                {activeFilters.map((filter) => (
                  <Badge key={filter} variant="secondary" className="flex items-center gap-1">
                    {filter}
                    <X className="h-3 w-3 cursor-pointer" onClick={() => {
                      // Remove this specific filter
                      if (filter === "Categorias") setFilters({...filters, categories: []});
                      if (filter === "Preço") setFilters({...filters, priceRange: undefined});
                      if (filter === "Promoções") setFilters({...filters, onlyPromotions: false});
                      if (filter.includes("Preço:") || filter === "Melhor avaliados" ||
                          filter === "Mais próximos" || filter === "Menor tempo de entrega") {
                        setFilters({...filters, sortBy: undefined});
                      }
                    }} />
                  </Badge>
                ))}
                <Badge
                  variant="outline"
                  className="cursor-pointer"
                  onClick={clearFilters}
                >
                  Limpar todos
                </Badge>
              </div>
            )}

            <Tabs defaultValue={activeTab} onValueChange={(value) => setActiveTab(value as "products" | "shops")}>
              <TabsList className="grid w-full grid-cols-2 mb-6">
                <TabsTrigger value="products" className="flex items-center gap-2">
                  <ShoppingBag className="h-4 w-4" />
                  Produtos ({products.length})
                </TabsTrigger>
                <TabsTrigger value="shops" className="flex items-center gap-2">
                  <Store className="h-4 w-4" />
                  Lojas ({shops.length})
                </TabsTrigger>
              </TabsList>

              <TabsContent value="products">
                {isLoading ? (
                  <div className="flex justify-center items-center py-12">
                    <div className="h-8 w-8 animate-spin rounded-full border-4 border-cta border-t-transparent" />
                  </div>
                ) : products.length === 0 ? (
                  <div className="text-center py-12 bg-gray-50 rounded-lg border">
                    <ShoppingBag className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium mb-1">Nenhum produto encontrado</h3>
                    <p className="text-muted-foreground mb-4">
                      Tente buscar com outros termos ou filtros diferentes.
                    </p>
                    <Button
                      variant="outline"
                      onClick={clearFilters}
                      className="mx-auto"
                    >
                      Limpar filtros
                    </Button>
                  </div>
                ) : (
                  <AnimatedList className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    {products.map((product) => (
                      <ProductCard
                        key={product.id}
                        id={product.id}
                        name={product.name}
                        image={product.image}
                        price={product.price}
                        originalPrice={product.originalPrice}
                        shopName={product.shopName}
                        isPromo={product.isPromo}
                      />
                    ))}
                  </AnimatedList>
                )}
              </TabsContent>

              <TabsContent value="shops">
                {isLoading ? (
                  <div className="flex justify-center items-center py-12">
                    <div className="h-8 w-8 animate-spin rounded-full border-4 border-cta border-t-transparent" />
                  </div>
                ) : shops.length === 0 ? (
                  <div className="text-center py-12 bg-gray-50 rounded-lg border">
                    <Store className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <h3 className="text-lg font-medium mb-1">Nenhuma loja encontrada</h3>
                    <p className="text-muted-foreground mb-4">
                      Tente buscar com outros termos ou filtros diferentes.
                    </p>
                    <Button
                      variant="outline"
                      onClick={clearFilters}
                      className="mx-auto"
                    >
                      Limpar filtros
                    </Button>
                  </div>
                ) : (
                  <AnimatedList className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                    {shops.map((shop) => (
                      <ShopCard
                        key={shop.id}
                        id={shop.id}
                        name={shop.name}
                        image={shop.image}
                        category={shop.category}
                        rating={shop.rating}
                        deliveryTime={shop.deliveryTime}
                        deliveryFee={shop.deliveryFee}
                        featured={shop.featured}
                      />
                    ))}
                  </AnimatedList>
                )}
              </TabsContent>
            </Tabs>
          </>
        ) : (
          <FadeIn>
            <div className="space-y-8">
              {/* Popular searches */}
              <div>
                <h2 className="text-lg font-medium mb-4">Buscas populares</h2>
                <div className="flex flex-wrap gap-2">
                  {popularSearches.map((term) => (
                    <motion.button
                      key={term}
                      className="px-3 py-1.5 rounded-full text-sm bg-white border text-muted-foreground"
                      onClick={() => handleSearch(term)}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      {term}
                    </motion.button>
                  ))}
                </div>
              </div>

              {/* Recent searches */}
              {user && recentSearches.length > 0 && (
                <div>
                  <h2 className="text-lg font-medium mb-4">Buscas recentes</h2>
                  <div className="flex flex-wrap gap-2">
                    {recentSearches.map((term) => (
                      <motion.button
                        key={term}
                        className="px-3 py-1.5 rounded-full text-sm bg-white border text-muted-foreground flex items-center"
                        onClick={() => handleSearch(term)}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <Clock className="h-3 w-3 mr-1" />
                        {term}
                      </motion.button>
                    ))}
                  </div>
                </div>
              )}

              {/* Categories */}
              <div>
                <h2 className="text-lg font-medium mb-4">Categorias</h2>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                  {categories.map((category) => (
                    <motion.div
                      key={category.id}
                      className="flex flex-col items-center justify-center p-4 bg-white rounded-lg border cursor-pointer"
                      onClick={() => handleFilterChange({ categories: [category.name] })}
                      whileHover={{ y: -5, boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)" }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <div className="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-2">
                        {/* Render icon based on category name */}
                        {category.icon === 'ShoppingBag' && <ShoppingBag className="h-6 w-6 text-cta" />}
                        {category.icon === 'Utensils' && <Utensils className="h-6 w-6 text-cta" />}
                        {/* Add more icon mappings as needed */}
                      </div>
                      <span className="text-sm font-medium">{category.name}</span>
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>
          </FadeIn>
        )}
      </main>

      <BottomNav />
    </AnimatedPage>
  );
};

export default SearchPage;
