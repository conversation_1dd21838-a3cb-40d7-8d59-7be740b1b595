
import { <PERSON><PERSON><PERSON>, <PERSON>, Minus, Plus } from "lucide-react";
import { But<PERSON> } from "./button";
import { Separator } from "./separator";
import { useState } from "react";
import { cn } from "@/lib/utils";
import { useCart } from "@/hooks/useCart";
import { Link } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import { AnimatedButton } from "@/components/animations";

export function MiniCart() {
  const [isOpen, setIsOpen] = useState(false);
  const { items, removeItem, updateQuantity, totalItems, subtotal } = useCart();

  const formattedSubtotal = new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(subtotal);

  const handleRemoveItem = (id: string) => {
    removeItem(id);
  };

  const handleIncreaseQuantity = (id: string, currentQuantity: number) => {
    updateQuantity(id, currentQuantity + 1);
  };

  const handleDecreaseQuantity = (id: string, currentQuantity: number) => {
    if (currentQuantity > 1) {
      updateQuantity(id, currentQuantity - 1);
    }
  };

  return (
    <>
      <Button
        variant="ghost"
        size="icon"
        className="relative"
        onClick={() => setIsOpen(true)}
      >
        <ShoppingCart className="w-5 h-5" />
        {totalItems > 0 && (
          <span className="absolute top-0 right-0 flex items-center justify-center w-4 h-4 text-xs text-white bg-cta rounded-full">
            {totalItems}
          </span>
        )}
      </Button>

      {/* Cart Overlay and Panel with Framer Motion */}
      <AnimatePresence>
        {isOpen && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="fixed inset-0 bg-black bg-opacity-50 z-50"
              onClick={() => setIsOpen(false)}
            />

            <motion.div
              initial={{ x: "100%" }}
              animate={{ x: 0 }}
              exit={{ x: "100%" }}
              transition={{ type: "spring", damping: 25, stiffness: 300 }}
              className="fixed top-0 right-0 bottom-0 w-80 bg-white z-50 shadow-xl"
            >
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-lg font-medium">Meu Carrinho</h2>
          <Button variant="ghost" size="icon" onClick={() => setIsOpen(false)}>
            <X className="w-5 h-5" />
          </Button>
        </div>

        <div className="flex-1 overflow-auto p-4">
          {items.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-full text-center">
              <ShoppingCart className="w-12 h-12 text-muted-foreground mb-4" />
              <p className="text-muted-foreground">Seu carrinho está vazio</p>
              <Button
                className="mt-4 bg-cta hover:bg-cta-dark"
                onClick={() => setIsOpen(false)}
              >
                Explorar produtos
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {items.map(item => (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ type: "spring", damping: 25 }}
                  className="flex items-start">
                  <img
                    src={item.image}
                    alt={item.name}
                    className="w-16 h-16 object-cover rounded-md mr-3"
                  />
                  <div className="flex-1">
                    <p className="text-sm font-medium">{item.name}</p>
                    <div className="flex items-center justify-between mt-1">
                      <div className="flex items-center border rounded-full bg-gray-50">
                        <motion.button
                          whileTap={{ scale: 0.9 }}
                          className="px-2 py-1 text-muted-foreground hover:bg-gray-100 rounded-full"
                          onClick={() => handleDecreaseQuantity(item.id, item.quantity)}
                        >
                          <Minus className="h-3 w-3" />
                        </motion.button>
                        <span className="px-2 py-1">{item.quantity}</span>
                        <motion.button
                          whileTap={{ scale: 0.9 }}
                          className="px-2 py-1 text-muted-foreground hover:bg-gray-100 rounded-full"
                          onClick={() => handleIncreaseQuantity(item.id, item.quantity)}
                        >
                          <Plus className="h-3 w-3" />
                        </motion.button>
                      </div>
                      <p className="text-sm font-medium">
                        {new Intl.NumberFormat('pt-BR', {
                          style: 'currency',
                          currency: 'BRL',
                        }).format(item.price)}
                      </p>
                    </div>
                  </div>
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    className="h-6 w-6 flex items-center justify-center text-muted-foreground hover:text-destructive"
                    onClick={() => handleRemoveItem(item.id)}
                  >
                    <X className="w-4 h-4" />
                  </motion.button>
                </motion.div>
              ))}
            </div>
          )}
        </div>

        {items.length > 0 && (
          <div className="border-t p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-muted-foreground">Subtotal</span>
              <span className="text-sm font-medium">{formattedSubtotal}</span>
            </div>
            <div className="flex items-center justify-between mb-4">
              <span className="text-sm text-muted-foreground">Taxa de entrega</span>
              <span className="text-sm font-medium">R$ 5,00</span>
            </div>
            <Separator className="my-2" />
            <div className="flex items-center justify-between mb-4">
              <span className="text-sm font-medium">Total</span>
              <span className="text-lg font-bold">
                {new Intl.NumberFormat('pt-BR', {
                  style: 'currency',
                  currency: 'BRL',
                }).format(subtotal + 5)}
              </span>
            </div>
            <AnimatedButton
              className="w-full bg-cta hover:bg-cta-dark text-white"
              onClick={() => setIsOpen(false)}
              whileHoverScale={1.02}
              whileTapScale={0.98}
              asChild
            >
              <Link to="/checkout" className="w-full flex items-center justify-center">
                Finalizar Compra
              </Link>
            </AnimatedButton>
          </div>
        )}
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
}
