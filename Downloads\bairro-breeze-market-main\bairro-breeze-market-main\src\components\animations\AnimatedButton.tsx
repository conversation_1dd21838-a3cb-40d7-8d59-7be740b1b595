import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { ButtonProps } from '@radix-ui/react-button';
import { forwardRef } from 'react';

interface AnimatedButtonProps extends ButtonProps {
  whileHoverScale?: number;
  whileTapScale?: number;
}

export const AnimatedButton = forwardRef<HTMLButtonElement, AnimatedButtonProps>(
  ({ children, className, whileHoverScale = 1.05, whileTapScale = 0.95, ...props }, ref) => {
    return (
      <motion.div
        whileHover={{ scale: whileHoverScale }}
        whileTap={{ scale: whileTapScale }}
        transition={{ type: 'spring', stiffness: 400, damping: 17 }}
      >
        <Button ref={ref} className={className} {...props}>
          {children}
        </Button>
      </motion.div>
    );
  }
);
