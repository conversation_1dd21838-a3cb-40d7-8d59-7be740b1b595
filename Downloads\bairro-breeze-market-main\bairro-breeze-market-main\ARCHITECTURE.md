# Arquitetura do Já Comprei App

Este documento descreve a arquitetura técnica do aplicativo "Já Comprei", incluindo a estrutura do projeto, padrões de design, fluxo de dados e decisões técnicas.

## Visão Geral da Arquitetura

O "Já Comprei" é uma aplicação web moderna construída com React e TypeScript, seguindo uma arquitetura de componentes modular e orientada a serviços. A aplicação é dividida em camadas lógicas que separam a interface do usuário, a lógica de negócios e a comunicação com APIs.

```
┌─────────────────────────────────────────────────────────────┐
│                      Interface do Usuário                    │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │  Componentes │  │    Pages    │  │     Animações      │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                      Lógica de Negócios                      │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │    Hooks    │  │   Contexts  │  │      Utilities      │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
├─────────────────────────────────────────────────────────────┤
│                      Camada de Serviços                      │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │  API Client │  │  Mock Data  │  │  External Services  │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

## Tecnologias Principais

- **Frontend**: React, TypeScript, Vite
- **Estilização**: Tailwind CSS, shadcn/ui
- **Gerenciamento de Estado**: React Context API, React Query
- **Roteamento**: React Router
- **Animações**: Framer Motion
- **Backend** (Produção): Supabase (BaaS)
- **Backend** (Desenvolvimento): Dados simulados locais
- **Validação**: Zod
- **Ferramentas de Desenvolvimento**: ESLint, TypeScript

## Estrutura de Diretórios

```
src/
├── components/       # Componentes React reutilizáveis
│   ├── animations/   # Componentes de animação
│   ├── auth/         # Componentes de autenticação
│   ├── checkout/     # Componentes do fluxo de checkout
│   ├── customer/     # Componentes específicos para clientes
│   ├── deliverer/    # Componentes específicos para entregadores
│   ├── map/          # Componentes de mapa e localização
│   ├── merchant/     # Componentes específicos para comerciantes
│   ├── orders/       # Componentes de pedidos
│   ├── products/     # Componentes de produtos
│   └── ui/           # Componentes de UI base (botões, cards, etc.)
├── hooks/            # Hooks personalizados
├── lib/              # Utilitários e configurações
├── pages/            # Páginas da aplicação
│   ├── auth/         # Páginas de autenticação
│   ├── customer/     # Páginas para clientes
│   ├── deliverer/    # Páginas para entregadores
│   ├── merchant/     # Páginas para comerciantes
│   └── orders/       # Páginas de pedidos
├── services/         # Serviços e APIs
└── types/            # Definições de tipos TypeScript
```

## Padrões de Design

### Componentes

Os componentes seguem uma estrutura consistente:

1. **Componentes de UI Base**: Componentes reutilizáveis e sem estado que formam a base do design system.
2. **Componentes Compostos**: Combinam componentes de UI base para criar funcionalidades específicas.
3. **Componentes de Página**: Representam páginas completas e gerenciam o estado e a lógica específica da página.

### Hooks Personalizados

Os hooks personalizados encapsulam a lógica de negócios e o gerenciamento de estado, seguindo o princípio de separação de responsabilidades:

- `useAuth`: Gerencia autenticação e autorização
- `useCart`: Gerencia o carrinho de compras
- `useAsync`: Gerencia operações assíncronas com tratamento de erros
- `useForm`: Gerencia formulários com validação

### Gerenciamento de Estado

O gerenciamento de estado é dividido em:

1. **Estado Local**: Usando `useState` para estado específico de componente
2. **Estado Compartilhado**: Usando Context API para estado global
3. **Estado de Servidor**: Usando React Query para dados do servidor

### Comunicação com APIs

A comunicação com APIs é abstraída em serviços:

```typescript
// Exemplo de serviço
export class ApiService<T> {
  constructor(private endpoint: string) {}

  async getAll(): Promise<T[]> {
    // Implementação
  }

  async getById(id: string): Promise<T> {
    // Implementação
  }

  // Outros métodos
}
```

## Fluxo de Dados

O fluxo de dados na aplicação segue um padrão unidirecional:

1. **Eventos do Usuário**: Capturados pelos componentes
2. **Ações**: Disparadas pelos componentes, geralmente através de hooks
3. **Serviços**: Processam as ações e comunicam com APIs
4. **Estado**: Atualizado com base nas respostas dos serviços
5. **Renderização**: Componentes são re-renderizados com o novo estado

```
┌──────────┐    ┌──────────┐    ┌──────────┐    ┌──────────┐
│  Evento  │ -> │   Ação   │ -> │ Serviço  │ -> │  Estado  │
└──────────┘    └──────────┘    └──────────┘    └──────────┘
                                                      │
                     ┌──────────────────────────────┘
                     ▼
              ┌──────────────┐
              │ Renderização │
              └──────────────┘
```

## Autenticação e Autorização

A autenticação é gerenciada pelo hook `useAuth` que:

1. Verifica o estado de autenticação no carregamento da aplicação
2. Fornece métodos para login, registro e logout
3. Armazena informações do usuário, incluindo perfil e permissões
4. Controla o acesso a rotas protegidas com base no perfil do usuário

```typescript
// Exemplo de uso do hook useAuth
const { user, signIn, signOut } = useAuth();

// Verificação de perfil
if (user?.user_metadata.role === 'merchant') {
  // Lógica específica para comerciantes
}
```

## Modo de Desenvolvimento vs. Produção

A aplicação suporta dois modos de operação:

1. **Modo de Desenvolvimento**: Usa dados simulados quando as variáveis de ambiente do Supabase não estão configuradas
2. **Modo de Produção**: Conecta-se ao Supabase para autenticação e armazenamento de dados

Esta abordagem permite o desenvolvimento sem dependência de um backend real, facilitando o trabalho offline e testes.

## Estratégia de Testes

A estratégia de testes inclui:

1. **Testes Unitários**: Para componentes e hooks isolados
2. **Testes de Integração**: Para fluxos completos
3. **Testes End-to-End**: Para jornadas de usuário

## Considerações de Performance

Para garantir boa performance, a aplicação implementa:

1. **Code Splitting**: Carregamento sob demanda de componentes
2. **Memoização**: Prevenção de re-renderizações desnecessárias
3. **Virtualização**: Para listas longas
4. **Otimização de Imagens**: Carregamento lazy e dimensionamento adequado

## Decisões Técnicas

### Por que React?

React foi escolhido pela sua popularidade, ecossistema rico e facilidade de desenvolvimento de interfaces complexas.

### Por que TypeScript?

TypeScript adiciona tipagem estática, melhorando a qualidade do código, facilitando a manutenção e proporcionando melhor experiência de desenvolvimento.

### Por que Tailwind CSS?

Tailwind CSS permite desenvolvimento rápido de interfaces com design consistente, sem necessidade de escrever CSS personalizado.

### Por que Supabase?

Supabase oferece uma solução completa de backend como serviço, incluindo autenticação, banco de dados e armazenamento, acelerando o desenvolvimento.

## Evolução da Arquitetura

À medida que o aplicativo cresce, a arquitetura evoluirá para:

1. **Microfrontends**: Separação de funcionalidades em aplicações menores
2. **API Gateway**: Centralização de chamadas de API
3. **Serverless Functions**: Para lógica de backend específica
4. **Observabilidade**: Monitoramento e logging avançados

## Conclusão

A arquitetura do "Já Comprei" foi projetada para ser modular, escalável e fácil de manter. A separação clara de responsabilidades e a estrutura organizada permitem o desenvolvimento ágil e a evolução contínua do aplicativo.
