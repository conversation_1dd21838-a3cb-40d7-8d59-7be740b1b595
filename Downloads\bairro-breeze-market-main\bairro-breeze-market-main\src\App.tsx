import React, { Suspense, lazy, useState, useEffect } from 'react';
import ErrorBoundary from "./components/ErrorBoundary";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, useLocation } from "react-router-dom";
import { AuthProvider } from "./hooks/useAuth";
import { CartProvider } from "./hooks/useCart";
import { ProtectedRoute } from "./components/ProtectedRoute";
import { MotionLayout } from "./components/animations/MotionLayout";
import { ScrollProgress } from "./components/animations/ScrollAnimations";
import { SkipToContent } from "./components/ui/a11y";
import { Loader2 } from "lucide-react";

// Componente de loading para lazy loading
const PageLoading = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="flex flex-col items-center space-y-4">
      <Loader2 className="h-12 w-12 animate-spin text-primary" />
      <p className="text-muted-foreground">Carregando...</p>
    </div>
  </div>
);

// Lazy loading de páginas
const Index = lazy(() => import("./pages/Index"));
const SimpleIndex = lazy(() => import("./pages/SimpleIndex"));
const NotFound = lazy(() => import("./pages/NotFound"));
const MerchantDashboard = lazy(() => import("./pages/merchant/MerchantDashboard"));
const MerchantOrders = lazy(() => import("./pages/merchant/Orders"));
const DelivererDashboard = lazy(() => import("./pages/deliverer/DelivererDashboard"));
const DelivererOrders = lazy(() => import("./pages/deliverer/Orders"));
const DelivererProfile = lazy(() => import("./pages/deliverer/DelivererProfile"));
const RoleRedirect = lazy(() => import("./components/auth/RoleRedirect")).then(module => ({ default: module.RoleRedirect }));
const Login = lazy(() => import("./pages/auth/Login"));
const SimpleLogin = lazy(() => import("./pages/auth/SimpleLogin"));
const Register = lazy(() => import("./pages/auth/Register"));
const SimpleRegister = lazy(() => import("./pages/auth/SimpleRegister"));
const ProductDetails = lazy(() => import("./pages/ProductDetails"));
const SimpleProductDetails = lazy(() => import("./pages/SimpleProductDetails"));
const ShopDetails = lazy(() => import("./pages/ShopDetails"));
const Checkout = lazy(() => import("./pages/Checkout"));
const Profile = lazy(() => import("./pages/user/Profile"));
const Orders = lazy(() => import("./pages/user/Orders"));
const CustomerOrders = lazy(() => import("./pages/customer/Orders"));
const OrderTrackingPage = lazy(() => import("./pages/orders/OrderTrackingPage"));
const Search = lazy(() => import("./pages/Search"));

const queryClient = new QueryClient();

// Componente de layout com animações
const AnimatedRoutes = () => {
  const location = useLocation();

  return (
    <MotionLayout transitionType="fade" duration={0.3}>
      <main id="main-content">
        <Suspense fallback={<PageLoading />}>
          <Routes location={location} key={location.pathname}>

            <Route path="/" element={<SimpleIndex />} />
            <Route path="/home" element={<Index />} />
            <Route path="/login" element={<SimpleLogin />} />
            <Route path="/login-full" element={<Login />} />
            <Route path="/register" element={<SimpleRegister />} />
            <Route path="/register-full" element={<Register />} />
            <Route path="/redirect" element={<RoleRedirect />} />
            <Route path="/product/:id" element={<SimpleProductDetails />} />
            <Route path="/product-full/:id" element={<ProductDetails />} />
            <Route path="/shop/:id" element={<ShopDetails />} />
            <Route path="/search" element={<Search />} />

            {/* Protected routes */}
            <Route path="/checkout" element={
              <ProtectedRoute>
                <Checkout />
              </ProtectedRoute>
            } />
            <Route path="/profile" element={
              <ProtectedRoute>
                <Profile />
              </ProtectedRoute>
            } />
            <Route path="/orders" element={
              <ProtectedRoute>
                <CustomerOrders />
              </ProtectedRoute>
            } />
            <Route path="/orders/:orderId" element={
              <ProtectedRoute>
                <OrderTrackingPage />
              </ProtectedRoute>
            } />
            {/* Merchant Routes */}
            <Route path="/merchant" element={
              <ProtectedRoute requiredRole="merchant">
                <MerchantDashboard />
              </ProtectedRoute>
            } />
            <Route path="/merchant/orders" element={
              <ProtectedRoute requiredRole="merchant">
                <MerchantOrders />
              </ProtectedRoute>
            } />
            <Route path="/merchant/products" element={
              <ProtectedRoute requiredRole="merchant">
                <MerchantDashboard activeTab="products" />
              </ProtectedRoute>
            } />
            <Route path="/merchant/sales" element={
              <ProtectedRoute requiredRole="merchant">
                <MerchantDashboard activeTab="analytics" />
              </ProtectedRoute>
            } />

            {/* Deliverer Routes */}
            <Route path="/deliverer" element={
              <ProtectedRoute requiredRole="deliverer">
                <DelivererDashboard />
              </ProtectedRoute>
            } />
            <Route path="/deliverer/deliveries" element={
              <ProtectedRoute requiredRole="deliverer">
                <DelivererDashboard activeTab="pending" />
              </ProtectedRoute>
            } />
            <Route path="/deliverer/map" element={
              <ProtectedRoute requiredRole="deliverer">
                <DelivererDashboard activeTab="map" />
              </ProtectedRoute>
            } />
            <Route path="/deliverer/orders" element={
              <ProtectedRoute requiredRole="deliverer">
                <DelivererOrders />
              </ProtectedRoute>
            } />
            <Route path="/deliverer/profile" element={
              <ProtectedRoute requiredRole="deliverer">
                <DelivererProfile />
              </ProtectedRoute>
            } />

            {/* Catch-all route */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </Suspense>
      </main>
    </MotionLayout>
  );
};

const App = () => {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simular carregamento inicial
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center space-y-4">
          <div className="h-12 w-12 animate-spin rounded-full border-4 border-primary border-t-transparent" />
          <p className="text-muted-foreground">Carregando aplicativo...</p>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <BrowserRouter>
        <AuthProvider>
          <CartProvider>
            <SkipToContent contentId="main-content" />
            <ScrollProgress color="var(--trust)" />
            <Toaster />
            <Sonner />
            <AnimatedRoutes />
          </CartProvider>
        </AuthProvider>
      </BrowserRouter>
    </TooltipProvider>
  </QueryClientProvider>
    </ErrorBoundary>
  );
};

export default App;
