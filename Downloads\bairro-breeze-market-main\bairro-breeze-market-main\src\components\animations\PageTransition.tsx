import { motion } from 'framer-motion';
import { ReactNode } from 'react';

interface PageTransitionProps {
  children: ReactNode;
  className?: string;
  direction?: 'up' | 'down' | 'left' | 'right' | 'fade';
  duration?: number;
  delay?: number;
}

/**
 * Componente de transição de página com animações suaves e configuráveis
 */
export function PageTransition({
  children,
  className = '',
  direction = 'up',
  duration = 0.3,
  delay = 0,
}: PageTransitionProps) {
  // Configurações de animação baseadas na direção
  const getVariants = () => {
    switch (direction) {
      case 'up':
        return {
          initial: { opacity: 0, y: 20 },
          animate: { opacity: 1, y: 0 },
          exit: { opacity: 0, y: -20 },
        };
      case 'down':
        return {
          initial: { opacity: 0, y: -20 },
          animate: { opacity: 1, y: 0 },
          exit: { opacity: 0, y: 20 },
        };
      case 'left':
        return {
          initial: { opacity: 0, x: 20 },
          animate: { opacity: 1, x: 0 },
          exit: { opacity: 0, x: -20 },
        };
      case 'right':
        return {
          initial: { opacity: 0, x: -20 },
          animate: { opacity: 1, x: 0 },
          exit: { opacity: 0, x: 20 },
        };
      case 'fade':
      default:
        return {
          initial: { opacity: 0 },
          animate: { opacity: 1 },
          exit: { opacity: 0 },
        };
    }
  };

  const variants = getVariants();

  return (
    <motion.div
      className={className}
      initial="initial"
      animate="animate"
      exit="exit"
      variants={variants}
      transition={{
        duration,
        delay,
        ease: [0.25, 0.1, 0.25, 1.0], // Curva de bezier para easing suave
      }}
    >
      {children}
    </motion.div>
  );
}

/**
 * Componente de transição de página com efeito de slide
 */
export function SlideTransition({
  children,
  className = '',
  direction = 'right',
  duration = 0.4,
  delay = 0,
}: PageTransitionProps) {
  // Configurações de animação para slide
  const getSlideVariants = () => {
    switch (direction) {
      case 'left':
        return {
          initial: { opacity: 0, x: '100%' },
          animate: { opacity: 1, x: 0 },
          exit: { opacity: 0, x: '-100%' },
        };
      case 'right':
      default:
        return {
          initial: { opacity: 0, x: '-100%' },
          animate: { opacity: 1, x: 0 },
          exit: { opacity: 0, x: '100%' },
        };
    }
  };

  const slideVariants = getSlideVariants();

  return (
    <motion.div
      className={className}
      initial="initial"
      animate="animate"
      exit="exit"
      variants={slideVariants}
      transition={{
        type: 'spring',
        stiffness: 300,
        damping: 30,
        duration,
        delay,
      }}
    >
      {children}
    </motion.div>
  );
}

/**
 * Componente de transição de página com efeito de zoom
 */
export function ZoomTransition({
  children,
  className = '',
  duration = 0.4,
  delay = 0,
}: Omit<PageTransitionProps, 'direction'>) {
  const zoomVariants = {
    initial: { opacity: 0, scale: 0.95 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 1.05 },
  };

  return (
    <motion.div
      className={className}
      initial="initial"
      animate="animate"
      exit="exit"
      variants={zoomVariants}
      transition={{
        type: 'spring',
        stiffness: 300,
        damping: 30,
        duration,
        delay,
      }}
    >
      {children}
    </motion.div>
  );
}
