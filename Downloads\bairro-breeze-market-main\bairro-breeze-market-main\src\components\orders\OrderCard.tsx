
import { Order } from "@/types/order";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { CheckCircle, Clock, Package, XCircle, ChevronRight, MapPin } from "lucide-react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { useState } from "react";

const statusMap = {
  pending: { label: "Pendente", icon: Clock, className: "bg-yellow-500", progressPercent: 25 },
  in_progress: { label: "Em andamento", icon: Package, className: "bg-blue-500", progressPercent: 50 },
  delivered: { label: "Entregue", icon: CheckCircle, className: "bg-green-500", progressPercent: 100 },
  cancelled: { label: "Cancelado", icon: XCircle, className: "bg-red-500", progressPercent: 0 },
};

export function OrderCard({ order }: { order: Order }) {
  const [expanded, setExpanded] = useState(false);
  const status = statusMap[order.status];
  const StatusIcon = status.icon;
  const date = new Date(order.createdAt).toLocaleDateString('pt-BR');
  const time = new Date(order.createdAt).toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });

  return (
    <motion.div
      whileHover={{ y: -5 }}
      transition={{ type: "spring", stiffness: 300, damping: 20 }}
    >
      <Card className="overflow-hidden">
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <div className="flex flex-col">
            <p className="font-medium">{order.shopName}</p>
            <p className="text-sm text-muted-foreground">Pedido #{order.id}</p>
          </div>
          <Badge className={status.className}>
            <StatusIcon className="w-4 h-4 mr-1" />
            {status.label}
          </Badge>
        </CardHeader>

        {/* Progress bar */}
        {order.status !== 'cancelled' && (
          <div className="px-6 pb-2">
            <div className="h-1.5 w-full bg-gray-200 rounded-full overflow-hidden">
              <motion.div
                className={`h-full ${status.className}`}
                initial={{ width: 0 }}
                animate={{ width: `${status.progressPercent}%` }}
                transition={{ duration: 1, delay: 0.2 }}
              />
            </div>
            <div className="flex justify-between text-xs text-muted-foreground mt-1">
              <span>Pedido recebido</span>
              <span>Em preparo</span>
              <span>Entregue</span>
            </div>
          </div>
        )}
      <CardContent>
        <div className="space-y-2">
          {/* Order summary - always visible */}
          <div className="flex justify-between font-medium">
            <span>{order.items.length} {order.items.length === 1 ? 'item' : 'itens'}</span>
            <span>
              {new Intl.NumberFormat('pt-BR', {
                style: 'currency',
                currency: 'BRL'
              }).format(order.total)}
            </span>
          </div>

          {/* Order details - expandable */}
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{
              height: expanded ? 'auto' : 0,
              opacity: expanded ? 1 : 0
            }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            <div className="pt-2 space-y-2">
              {order.items.map((item, index) => (
                <div key={index} className="flex justify-between text-sm">
                  <span>{item.quantity}x {item.name}</span>
                  <span className="font-medium">
                    {new Intl.NumberFormat('pt-BR', {
                      style: 'currency',
                      currency: 'BRL'
                    }).format(item.price * item.quantity)}
                  </span>
                </div>
              ))}
              <div className="flex items-center text-sm text-muted-foreground mt-2">
                <MapPin className="h-4 w-4 mr-1" />
                <span>Entrega em: {order.deliveryAddress}</span>
              </div>
            </div>
          </motion.div>

          <div className="flex justify-between items-center pt-2">
            <p className="text-xs text-muted-foreground">
              {date} às {time}
            </p>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 px-2 text-xs"
              onClick={() => setExpanded(!expanded)}
            >
              {expanded ? "Menos detalhes" : "Mais detalhes"}
              <motion.div
                animate={{ rotate: expanded ? 90 : 0 }}
                transition={{ duration: 0.2 }}
              >
                <ChevronRight className="h-4 w-4 ml-1" />
              </motion.div>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
    </motion.div>
  );
}
