// Location interface
export interface Location {
  latitude: number;
  longitude: number;
  address?: string;
}

// Coordinates interface for compatibility with DeliveryMap component
export interface Coordinates {
  lat: number;
  lng: number;
}

// Route interface
export interface Route {
  origin: Location;
  destination: Location;
  waypoints?: Location[];
  distance: number; // in meters
  duration: number; // in seconds
  polyline: string; // encoded polyline
}

// Mock locations for development mode
const mockLocations: Record<string, Location> = {
  'shop1': { latitude: -23.5505, longitude: -46.6333, address: 'Rua das Flores, 123 - Centro' },
  'shop2': { latitude: -23.5605, longitude: -46.6433, address: 'Av. Principal, 456 - Vila Nova' },
  'shop3': { latitude: -23.5705, longitude: -46.6533, address: 'Rua dos Açougueiros, 789 - <PERSON><PERSON><PERSON>' },
  'shop4': { latitude: -23.5805, longitude: -46.6633, address: 'Rua das Hortaliças, 321 - <PERSON><PERSON><PERSON>' },
  'customer1': { latitude: -23.5555, longitude: -46.6383, address: 'Rua Augusta, 567 - Consolação' },
  'customer2': { latitude: -23.5655, longitude: -46.6483, address: 'Alameda Santos, 234 - Cerqueira César' },
  'customer3': { latitude: -23.5755, longitude: -46.6583, address: 'Rua dos Pinheiros, 789 - Pinheiros' },
  'deliverer1': { latitude: -23.5605, longitude: -46.6383 },
  'deliverer2': { latitude: -23.5705, longitude: -46.6483 }
};

/**
 * Get current location
 * @returns Promise with location
 */
export const getCurrentLocation = async (): Promise<Location> => {
  try {
    // In a real app, this would use the browser's geolocation API
    return new Promise((resolve, reject) => {
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          position => {
            resolve({
              latitude: position.coords.latitude,
              longitude: position.coords.longitude
            });
          },
          error => {
            console.error('Error getting current location:', error);
            // Return a default location for development
            resolve(mockLocations.deliverer1);
          },
          { enableHighAccuracy: true, timeout: 5000, maximumAge: 0 }
        );
      } else {
        reject(new Error('Geolocation is not supported by this browser.'));
      }
    });
  } catch (error) {
    console.error('Error in getCurrentLocation:', error);
    // Return a default location for development
    return mockLocations.deliverer1;
  }
};

/**
 * Get location by address
 * @param address Address to geocode
 * @returns Promise with location
 */
export const getLocationByAddress = async (address: string): Promise<Location | null> => {
  try {
    // In a real app, this would use a geocoding API

    // For now, return a mock location
    // Find a mock location with a matching address
    for (const key in mockLocations) {
      if (mockLocations[key].address?.includes(address)) {
        return mockLocations[key];
      }
    }

    // If no match, return a random location
    const keys = Object.keys(mockLocations);
    const randomKey = keys[Math.floor(Math.random() * keys.length)];
    return {
      ...mockLocations[randomKey],
      address
    };
  } catch (error) {
    console.error(`Error geocoding address "${address}":`, error);
    return null;
  }
};

/**
 * Get location by address (Coordinates version for DeliveryMap)
 * @param address Address to geocode
 * @returns Promise with coordinates
 */
export const getLocationByAddressAsCoordinates = async (address: string): Promise<Coordinates | null> => {
  const location = await getLocationByAddress(address);
  if (!location) return null;

  return {
    lat: location.latitude,
    lng: location.longitude
  };
};

/**
 * Get address by location
 * @param location Location to reverse geocode
 * @returns Promise with address
 */
export const getAddressByLocation = async (
  location: Pick<Location, 'latitude' | 'longitude'>
): Promise<string | null> => {
  try {
    // In a real app, this would use a reverse geocoding API

    // For now, return a mock address
    // Find a mock location with matching coordinates
    for (const key in mockLocations) {
      const mockLocation = mockLocations[key];
      if (
        Math.abs(mockLocation.latitude - location.latitude) < 0.001 &&
        Math.abs(mockLocation.longitude - location.longitude) < 0.001 &&
        mockLocation.address
      ) {
        return mockLocation.address;
      }
    }

    // If no match, return a generic address
    return `Latitude: ${location.latitude.toFixed(4)}, Longitude: ${location.longitude.toFixed(4)}`;
  } catch (error) {
    console.error(`Error reverse geocoding location (${location.latitude}, ${location.longitude}):`, error);
    return null;
  }
};

/**
 * Calculate route between two locations
 * @param origin Origin location
 * @param destination Destination location
 * @param waypoints Optional waypoints
 * @returns Promise with route
 */
export const calculateRoute = async (
  origin: Location,
  destination: Location,
  waypoints?: Location[]
): Promise<Route | null> => {
  try {
    // In a real app, this would use a routing API

    // For now, return a mock route
    // Calculate mock distance and duration
    const distance = Math.sqrt(
      Math.pow(destination.latitude - origin.latitude, 2) +
      Math.pow(destination.longitude - origin.longitude, 2)
    ) * 111000; // Convert degrees to meters (roughly)

    const duration = distance / 5; // Assume 5 m/s (18 km/h)

    // Generate a mock polyline
    const polyline = 'mock_polyline';

    return {
      origin,
      destination,
      waypoints,
      distance,
      duration,
      polyline
    };
  } catch (error) {
    console.error('Error calculating route:', error);
    return null;
  }
};

/**
 * Calculate route between two coordinates (for DeliveryMap)
 * @param origin Origin coordinates
 * @param destination Destination coordinates
 * @returns Promise with route info
 */
export const calculateRouteWithCoordinates = async (
  origin: Coordinates,
  destination: Coordinates
): Promise<{ distance: number; duration: number; path: Coordinates[] } | null> => {
  try {
    // Convert coordinates to locations
    const originLocation: Location = {
      latitude: origin.lat,
      longitude: origin.lng
    };

    const destinationLocation: Location = {
      latitude: destination.lat,
      longitude: destination.lng
    };

    // Calculate route
    const route = await calculateRoute(originLocation, destinationLocation);
    if (!route) return null;

    // Generate path points
    const path: Coordinates[] = [];

    // Add origin
    path.push(origin);

    // Add intermediate points
    const numPoints = 5;
    for (let i = 1; i <= numPoints; i++) {
      const fraction = i / (numPoints + 1);

      // Linear interpolation
      const lat = origin.lat + (destination.lat - origin.lat) * fraction;
      const lng = origin.lng + (destination.lng - origin.lng) * fraction;

      // Add some randomness
      const jitter = 0.001;
      path.push({
        lat: lat + (Math.random() - 0.5) * jitter,
        lng: lng + (Math.random() - 0.5) * jitter
      });
    }

    // Add destination
    path.push(destination);

    return {
      distance: route.distance,
      duration: route.duration,
      path
    };
  } catch (error) {
    console.error('Error calculating route with coordinates:', error);
    return null;
  }
};

/**
 * Track location updates
 * @param callback Callback function to handle location updates
 * @returns Unsubscribe function
 */
export const trackLocation = (
  callback: (location: Location) => void
): () => void => {
  try {
    // In a real app, this would use the browser's geolocation API
    // with watchPosition

    if (navigator.geolocation) {
      const watchId = navigator.geolocation.watchPosition(
        position => {
          callback({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude
          });
        },
        error => {
          console.error('Error tracking location:', error);

          // For development, simulate movement
          let currentLocation = { ...mockLocations.deliverer1 };

          const intervalId = setInterval(() => {
            // Simulate random movement
            currentLocation = {
              latitude: currentLocation.latitude + (Math.random() - 0.5) * 0.001,
              longitude: currentLocation.longitude + (Math.random() - 0.5) * 0.001
            };

            callback(currentLocation);
          }, 5000);

          return () => clearInterval(intervalId);
        },
        { enableHighAccuracy: true, timeout: 5000, maximumAge: 0 }
      );

      return () => navigator.geolocation.clearWatch(watchId);
    } else {
      console.error('Geolocation is not supported by this browser.');

      // For development, simulate movement
      let currentLocation = { ...mockLocations.deliverer1 };

      const intervalId = setInterval(() => {
        // Simulate random movement
        currentLocation = {
          latitude: currentLocation.latitude + (Math.random() - 0.5) * 0.001,
          longitude: currentLocation.longitude + (Math.random() - 0.5) * 0.001
        };

        callback(currentLocation);
      }, 5000);

      return () => clearInterval(intervalId);
    }
  } catch (error) {
    console.error('Error in trackLocation:', error);

    // For development, simulate movement
    let currentLocation = { ...mockLocations.deliverer1 };

    const intervalId = setInterval(() => {
      // Simulate random movement
      currentLocation = {
        latitude: currentLocation.latitude + (Math.random() - 0.5) * 0.001,
        longitude: currentLocation.longitude + (Math.random() - 0.5) * 0.001
      };

      callback(currentLocation);
    }, 5000);

    return () => clearInterval(intervalId);
  }
};

/**
 * Get estimated time of arrival
 * @param origin Origin location
 * @param destination Destination location
 * @returns Promise with ETA in seconds
 */
export const getETA = async (
  origin: Location,
  destination: Location
): Promise<number | null> => {
  try {
    // In a real app, this would use a routing API

    // For now, calculate a mock ETA
    const route = await calculateRoute(origin, destination);
    return route?.duration || null;
  } catch (error) {
    console.error('Error calculating ETA:', error);
    return null;
  }
};

/**
 * Get location updates for a deliverer (for DeliveryMap)
 * @param delivererId Deliverer ID
 * @param origin Origin coordinates
 * @param destination Destination coordinates
 * @param path Path coordinates
 * @param callback Callback function to handle location updates
 * @returns Unsubscribe function
 */
export const getLocationUpdates = (
  delivererId: string,
  origin: Coordinates,
  destination: Coordinates,
  path: Coordinates[],
  callback: (location: Coordinates) => void
): () => void => {
  console.log(`Starting location updates for deliverer ${delivererId}`);

  // Use the provided path or generate a simple one
  const routePath = path.length > 0 ? path : [
    origin,
    {
      lat: (origin.lat + destination.lat) / 2,
      lng: (origin.lng + destination.lng) / 2
    },
    destination
  ];

  // Current position in the path
  let currentIndex = 0;

  // Start with the origin
  callback(origin);

  // Update location every few seconds
  const intervalId = setInterval(() => {
    currentIndex++;

    if (currentIndex < routePath.length) {
      // Move along the path
      callback(routePath[currentIndex]);
    } else {
      // Reached destination
      callback(destination);
      clearInterval(intervalId);
      console.log(`Deliverer ${delivererId} reached destination`);
    }
  }, 3000); // Update every 3 seconds

  // Return function to stop updates
  return () => {
    clearInterval(intervalId);
    console.log(`Location updates stopped for deliverer ${delivererId}`);
  };
};

/**
 * Format distance in meters to a human-readable string
 * @param distance Distance in meters
 * @returns Formatted distance string
 */
export const formatDistance = (distance: number): string => {
  if (distance < 1000) {
    return `${Math.round(distance)}m`;
  } else {
    return `${(distance / 1000).toFixed(1)}km`;
  }
};

/**
 * Format duration in seconds to a human-readable string
 * @param duration Duration in seconds
 * @returns Formatted duration string
 */
export const formatDuration = (duration: number): string => {
  const minutes = Math.floor(duration / 60);
  const seconds = Math.floor(duration % 60);

  if (minutes < 1) {
    return `${seconds}s`;
  } else if (minutes < 60) {
    return `${minutes}min${seconds > 0 ? ` ${seconds}s` : ''}`;
  } else {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return `${hours}h${remainingMinutes > 0 ? ` ${remainingMinutes}min` : ''}`;
  }
};
