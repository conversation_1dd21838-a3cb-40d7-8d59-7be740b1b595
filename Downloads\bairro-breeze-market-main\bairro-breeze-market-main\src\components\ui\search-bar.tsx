
import { Search as SearchIcon } from "lucide-react";
import { Input } from "./input";
import { useState, useEffect } from "react";
import { Button } from "./button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem
} from "./dropdown-menu";
import { Filter, Star, MapPin, Clock } from "lucide-react";

interface SearchBarProps {
  onSearch?: (term: string, filters?: SearchFilters) => void;
  className?: string;
  initialValue?: string;
}

export interface SearchFilters {
  sortBy?: 'price-asc' | 'price-desc' | 'rating' | 'distance' | 'delivery-time';
  categories?: string[];
  priceRange?: [number, number];
  onlyPromotions?: boolean;
}

export function SearchBar({ onSearch, className, initialValue = "" }: SearchBarProps) {
  const [searchTerm, setSearchTerm] = useState(initialValue);
  const [filters, setFilters] = useState<SearchFilters>({});

  const handleSearch = () => {
    if (onSearch) {
      onSearch(searchTerm, filters);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  const handleFilterChange = (type: 'sortBy' | 'onlyPromotions', value: any) => {
    setFilters(prev => {
      if (type === 'sortBy') {
        return {
          ...prev,
          sortBy: value as SearchFilters['sortBy']
        };
      } else if (type === 'onlyPromotions') {
        return {
          ...prev,
          onlyPromotions: value
        };
      }
      return prev;
    });
  };

  // Apply filters when they change
  useEffect(() => {
    if (searchTerm) {
      handleSearch();
    }
  }, [filters]);

  return (
    <div className={`relative w-full flex ${className}`}>
      <div className="relative flex-1">
        <SearchIcon className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          type="search"
          placeholder="O que você está procurando?"
          className="pl-10 bg-white"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          onKeyDown={handleKeyDown}
        />
      </div>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size="icon" className="ml-2">
            <Filter className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuLabel>Filtrar por</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuCheckboxItem
            checked={filters.sortBy === 'price-asc'}
            onCheckedChange={() => handleFilterChange('sortBy', 'price-asc')}
          >
            <span className="flex items-center">
              <Star className="mr-2 h-4 w-4" />
              Preço: Menor para maior
            </span>
          </DropdownMenuCheckboxItem>
          <DropdownMenuCheckboxItem
            checked={filters.sortBy === 'price-desc'}
            onCheckedChange={() => handleFilterChange('sortBy', 'price-desc')}
          >
            <span className="flex items-center">
              <Star className="mr-2 h-4 w-4" />
              Preço: Maior para menor
            </span>
          </DropdownMenuCheckboxItem>
          <DropdownMenuSeparator />
          <DropdownMenuCheckboxItem
            checked={filters.sortBy === 'rating'}
            onCheckedChange={() => handleFilterChange('sortBy', 'rating')}
          >
            <span className="flex items-center">
              <Star className="mr-2 h-4 w-4" />
              Avaliação: Melhor avaliados
            </span>
          </DropdownMenuCheckboxItem>
          <DropdownMenuSeparator />
          <DropdownMenuCheckboxItem
            checked={filters.sortBy === 'distance'}
            onCheckedChange={() => handleFilterChange('sortBy', 'distance')}
          >
            <span className="flex items-center">
              <MapPin className="mr-2 h-4 w-4" />
              Distância: Mais próximos
            </span>
          </DropdownMenuCheckboxItem>
          <DropdownMenuCheckboxItem
            checked={filters.sortBy === 'delivery-time'}
            onCheckedChange={() => handleFilterChange('sortBy', 'delivery-time')}
          >
            <span className="flex items-center">
              <Clock className="mr-2 h-4 w-4" />
              Tempo de entrega: Mais rápido
            </span>
          </DropdownMenuCheckboxItem>
          <DropdownMenuSeparator />
          <DropdownMenuCheckboxItem
            checked={filters.onlyPromotions}
            onCheckedChange={(checked) => handleFilterChange('onlyPromotions', checked)}
          >
            <span className="flex items-center">
              <Star className="mr-2 h-4 w-4" />
              Apenas promoções
            </span>
          </DropdownMenuCheckboxItem>
          {/* Removed duplicate entry */}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
