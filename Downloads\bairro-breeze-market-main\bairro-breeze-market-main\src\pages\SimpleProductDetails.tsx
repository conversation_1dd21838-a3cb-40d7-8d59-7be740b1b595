import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, ShoppingCart, Store, Star, Plus, Minus, Info, Clock } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

// Mock product data
const mockProducts = [
  {
    id: '1',
    name: '<PERSON><PERSON>an<PERSON>',
    description: 'Pão francês fresco, crocante por fora e macio por dentro.',
    price: 0.75,
    originalPrice: 0.85,
    image: 'https://images.unsplash.com/photo-1608198093002-ad4e005484ec',
    category: 'Padaria',
    shopId: '1',
    shopName: 'Padaria do Bairro',
    isPromo: true,
    stock: 100,
    rating: 4.8,
    ratingCount: 120
  },
  {
    id: '2',
    name: 'Filé Mignon Premium kg',
    description: 'Corte nobre e macio, perfeito para ocasiões especiais.',
    price: 69.90,
    originalPrice: null,
    image: 'https://images.unsplash.com/photo-1588168333986-5078d3ae3976',
    category: 'Açougue',
    shopId: '2',
    shopName: 'Açougue Premium',
    isPromo: false,
    stock: 20,
    rating: 4.9,
    ratingCount: 85
  },
  {
    id: '3',
    name: 'Café Especial 250g',
    description: 'Café especial torrado e moído na hora.',
    price: 24.90,
    originalPrice: 29.90,
    image: 'https://images.unsplash.com/photo-1559056199-641a0ac8b55e',
    category: 'Mercearia',
    shopId: '3',
    shopName: 'Café Especial',
    isPromo: true,
    stock: 50,
    rating: 4.7,
    ratingCount: 210
  }
];

export default function SimpleProductDetails() {
  const { id } = useParams<{ id: string }>();
  const [product, setProduct] = useState<any>(null);
  const [quantity, setQuantity] = useState(1);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const [addedToCart, setAddedToCart] = useState(false);
  const navigate = useNavigate();
  
  useEffect(() => {
    const loadProduct = async () => {
      try {
        setIsLoading(true);
        
        // Simular atraso de rede
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Encontrar produto pelo ID
        const foundProduct = mockProducts.find(p => p.id === id);
        
        if (foundProduct) {
          setProduct(foundProduct);
        } else {
          // Produto não encontrado, usar o primeiro como fallback
          setProduct(mockProducts[0]);
        }
      } catch (error) {
        console.error('Erro ao carregar produto:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadProduct();
  }, [id]);
  
  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity >= 1 && newQuantity <= (product?.stock || 100)) {
      setQuantity(newQuantity);
    }
  };
  
  const handleAddToCart = async () => {
    try {
      setIsAddingToCart(true);
      
      // Simular atraso de rede
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Adicionar ao carrinho (mock)
      const cartItem = {
        id: product.id,
        name: product.name,
        price: product.price,
        quantity,
        image: product.image,
        shopId: product.shopId,
        shopName: product.shopName
      };
      
      // Salvar no localStorage
      const existingCart = localStorage.getItem('cart');
      let cart = existingCart ? JSON.parse(existingCart) : { items: [] };
      
      // Verificar se já existe o item no carrinho
      const existingItemIndex = cart.items.findIndex((item: any) => item.id === cartItem.id);
      
      if (existingItemIndex >= 0) {
        // Atualizar quantidade
        cart.items[existingItemIndex].quantity += quantity;
      } else {
        // Adicionar novo item
        cart.items.push(cartItem);
      }
      
      localStorage.setItem('cart', JSON.stringify(cart));
      
      // Mostrar feedback
      setAddedToCart(true);
      setTimeout(() => setAddedToCart(false), 3000);
    } catch (error) {
      console.error('Erro ao adicionar ao carrinho:', error);
    } finally {
      setIsAddingToCart(false);
    }
  };
  
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse">
          <div className="h-6 w-24 bg-gray-200 rounded mb-4"></div>
          <div className="h-64 bg-gray-200 rounded-lg mb-6"></div>
          <div className="h-8 w-3/4 bg-gray-200 rounded mb-4"></div>
          <div className="h-4 w-1/2 bg-gray-200 rounded mb-6"></div>
          <div className="h-10 bg-gray-200 rounded-lg"></div>
        </div>
      </div>
    );
  }
  
  if (!product) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <Info className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
        <h2 className="text-2xl font-bold mb-2">Produto não encontrado</h2>
        <p className="text-muted-foreground mb-6">
          Não foi possível encontrar o produto solicitado.
        </p>
        <Button onClick={() => navigate('/')}>
          Voltar para a página inicial
        </Button>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto px-4 py-8">
      <Button
        variant="ghost"
        size="sm"
        className="mb-6"
        onClick={() => navigate(-1)}
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Voltar
      </Button>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Imagem do Produto */}
        <div className="relative">
          <div className="aspect-square rounded-lg overflow-hidden bg-muted">
            <img
              src={product.image}
              alt={product.name}
              className="w-full h-full object-cover"
              onError={(e) => {
                (e.target as HTMLImageElement).src = 'https://placehold.co/600x600/e2e8f0/64748b?text=Imagem+indisponível';
              }}
            />
          </div>
          
          {product.isPromo && (
            <Badge className="absolute top-4 left-4 bg-red-500">
              Promoção
            </Badge>
          )}
        </div>
        
        {/* Detalhes do Produto */}
        <div>
          <div className="mb-6">
            <h1 className="text-2xl font-bold mb-2">{product.name}</h1>
            
            <div className="flex items-center mb-2">
              <Link to={`/shop/${product.shopId}`} className="text-sm text-muted-foreground hover:underline flex items-center">
                <Store className="h-4 w-4 mr-1" />
                {product.shopName}
              </Link>
              <Separator orientation="vertical" className="mx-2 h-4" />
              <div className="flex items-center text-sm text-muted-foreground">
                <Star className="h-4 w-4 text-yellow-400 mr-1" />
                <span>{product.rating}</span>
                <span className="ml-1">({product.ratingCount})</span>
              </div>
            </div>
            
            <div className="flex items-center mb-4">
              {product.originalPrice ? (
                <>
                  <span className="text-2xl font-bold text-primary mr-2">
                    {formatCurrency(product.price)}
                  </span>
                  <span className="text-sm text-muted-foreground line-through">
                    {formatCurrency(product.originalPrice)}
                  </span>
                </>
              ) : (
                <span className="text-2xl font-bold text-primary">
                  {formatCurrency(product.price)}
                </span>
              )}
            </div>
            
            <p className="text-muted-foreground mb-6">
              {product.description}
            </p>
            
            <div className="flex items-center mb-6">
              <Button
                variant="outline"
                size="icon"
                onClick={() => handleQuantityChange(quantity - 1)}
                disabled={quantity <= 1}
              >
                <Minus className="h-4 w-4" />
              </Button>
              <span className="mx-4 font-medium">{quantity}</span>
              <Button
                variant="outline"
                size="icon"
                onClick={() => handleQuantityChange(quantity + 1)}
                disabled={quantity >= product.stock}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            
            <div className="flex flex-col space-y-2">
              <Button
                onClick={handleAddToCart}
                disabled={isAddingToCart || addedToCart}
                className="w-full"
              >
                {isAddingToCart ? (
                  <span className="flex items-center">
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Adicionando...
                  </span>
                ) : addedToCart ? (
                  <span className="flex items-center">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Adicionado ao carrinho
                  </span>
                ) : (
                  <span className="flex items-center">
                    <ShoppingCart className="mr-2 h-4 w-4" />
                    Adicionar ao carrinho
                  </span>
                )}
              </Button>
              
              <Link to="/cart">
                <Button variant="outline" className="w-full">
                  Ver carrinho
                </Button>
              </Link>
            </div>
          </div>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center text-sm">
                <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                <span className="text-muted-foreground">Entrega em 30-45 minutos</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
