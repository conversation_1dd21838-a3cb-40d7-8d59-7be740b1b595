import React, { useState, useEffect, ImgHTMLAttributes } from 'react';
import { Loader2, ImageOff } from 'lucide-react';

interface OptimizedImageProps extends ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  fallbackSrc?: string;
  aspectRatio?: string;
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down';
  loadingComponent?: React.ReactNode;
  errorComponent?: React.ReactNode;
  lazyLoad?: boolean;
  blur?: boolean;
  priority?: boolean;
  onLoad?: () => void;
  onError?: () => void;
}

/**
 * Componente de imagem otimizada com suporte a lazy loading, fallback e estados de carregamento
 */
export function OptimizedImage({
  src,
  alt,
  fallbackSrc,
  aspectRatio = 'auto',
  objectFit = 'cover',
  loadingComponent,
  errorComponent,
  lazyLoad = true,
  blur = false,
  priority = false,
  onLoad,
  onError,
  className,
  ...props
}: OptimizedImageProps) {
  const [loading, setLoading] = useState(!priority);
  const [error, setError] = useState(false);
  const [currentSrc, setCurrentSrc] = useState(priority ? src : '');

  useEffect(() => {
    // Reset states when src changes
    if (src !== currentSrc && !priority) {
      setLoading(true);
      setError(false);
    }

    // If priority is true, we already set the src
    if (priority) return;

    // If lazyLoad is false, load immediately
    if (!lazyLoad) {
      setCurrentSrc(src);
      return;
    }

    // Use IntersectionObserver for lazy loading
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          setCurrentSrc(src);
          observer.disconnect();
        }
      });
    });

    const element = document.getElementById(`image-${props.id || src}`);
    if (element) {
      observer.observe(element);
    }

    return () => {
      observer.disconnect();
    };
  }, [src, currentSrc, lazyLoad, priority, props.id]);

  const handleLoad = () => {
    setLoading(false);
    setError(false);
    onLoad?.();
  };

  const handleError = () => {
    setLoading(false);
    setError(true);
    
    // Try fallback if available
    if (fallbackSrc && currentSrc !== fallbackSrc) {
      setCurrentSrc(fallbackSrc);
    } else {
      onError?.();
    }
  };

  // Combine classes
  const imageClasses = [
    className || '',
    objectFit ? `object-${objectFit}` : '',
    blur && loading ? 'blur-sm' : '',
    'transition-opacity duration-300',
    loading ? 'opacity-0' : 'opacity-100',
  ].filter(Boolean).join(' ');

  return (
    <div
      id={`image-${props.id || src}`}
      className="relative overflow-hidden"
      style={{ aspectRatio }}
    >
      {/* Loading state */}
      {loading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          {loadingComponent || (
            <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          )}
        </div>
      )}

      {/* Error state */}
      {error && !fallbackSrc && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          {errorComponent || (
            <div className="flex flex-col items-center text-muted-foreground">
              <ImageOff className="h-8 w-8 mb-2" />
              <span className="text-xs">Falha ao carregar imagem</span>
            </div>
          )}
        </div>
      )}

      {/* Image */}
      {currentSrc && (
        <img
          src={currentSrc}
          alt={alt}
          className={imageClasses}
          onLoad={handleLoad}
          onError={handleError}
          loading={lazyLoad && !priority ? 'lazy' : undefined}
          {...props}
        />
      )}
    </div>
  );
}

/**
 * Componente de imagem de produto otimizada
 */
export function ProductImage({
  src,
  alt,
  className,
  ...props
}: Omit<OptimizedImageProps, 'aspectRatio' | 'objectFit'>) {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      aspectRatio="1/1"
      objectFit="cover"
      className={`rounded-md ${className || ''}`}
      fallbackSrc="/images/product-placeholder.jpg"
      {...props}
    />
  );
}

/**
 * Componente de imagem de avatar otimizada
 */
export function AvatarImage({
  src,
  alt,
  size = 'md',
  className,
  ...props
}: Omit<OptimizedImageProps, 'aspectRatio' | 'objectFit'> & {
  size?: 'sm' | 'md' | 'lg' | 'xl';
}) {
  const sizeClasses = {
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12',
    xl: 'h-16 w-16',
  };

  return (
    <OptimizedImage
      src={src}
      alt={alt}
      aspectRatio="1/1"
      objectFit="cover"
      className={`rounded-full ${sizeClasses[size]} ${className || ''}`}
      fallbackSrc="/images/avatar-placeholder.jpg"
      {...props}
    />
  );
}

/**
 * Componente de imagem de banner otimizada
 */
export function BannerImage({
  src,
  alt,
  height = 'md',
  className,
  ...props
}: Omit<OptimizedImageProps, 'aspectRatio' | 'objectFit'> & {
  height?: 'sm' | 'md' | 'lg';
}) {
  const aspectRatios = {
    sm: '16/3',
    md: '16/5',
    lg: '16/7',
  };

  return (
    <OptimizedImage
      src={src}
      alt={alt}
      aspectRatio={aspectRatios[height]}
      objectFit="cover"
      className={`rounded-md w-full ${className || ''}`}
      fallbackSrc="/images/banner-placeholder.jpg"
      {...props}
    />
  );
}
