import { supabaseClient } from '@/lib/supabase';
import { orderService } from './orderService';
import { Tables } from '@/types/database';

// Sales summary interface
export interface SalesSummary {
  totalSales: number;
  totalOrders: number;
  averageOrderValue: number;
  period: 'day' | 'week' | 'month' | 'year' | 'all';
}

// Sales by category interface
export interface SalesByCategory {
  category: string;
  amount: number;
  percentage: number;
}

// Sales by time interface
export interface SalesByTime {
  hour: number;
  amount: number;
  orders: number;
}

// Customer summary interface
export interface CustomerSummary {
  totalCustomers: number;
  newCustomers: number;
  returningCustomers: number;
  period: 'day' | 'week' | 'month' | 'year' | 'all';
}

/**
 * Get sales summary for a merchant
 * @param shopId Shop ID
 * @param period Time period
 * @returns Promise with sales summary
 */
export const getSalesSummary = async (
  shopId: string,
  period: SalesSummary['period'] = 'month'
): Promise<SalesSummary> => {
  try {
    // In a real app, this would fetch from a database
    
    // For now, return mock data
    return {
      totalSales: 12500,
      totalOrders: 250,
      averageOrderValue: 50,
      period
    };
  } catch (error) {
    console.error(`Error fetching sales summary for shop ${shopId}:`, error);
    return {
      totalSales: 0,
      totalOrders: 0,
      averageOrderValue: 0,
      period
    };
  }
};

/**
 * Get sales by category for a merchant
 * @param shopId Shop ID
 * @param period Time period
 * @returns Promise with sales by category
 */
export const getSalesByCategory = async (
  shopId: string,
  period: SalesSummary['period'] = 'month'
): Promise<SalesByCategory[]> => {
  try {
    // In a real app, this would fetch from a database
    
    // For now, return mock data
    return [
      { category: 'Frutas', amount: 3500, percentage: 28 },
      { category: 'Verduras', amount: 2800, percentage: 22.4 },
      { category: 'Carnes', amount: 4200, percentage: 33.6 },
      { category: 'Laticínios', amount: 1500, percentage: 12 },
      { category: 'Outros', amount: 500, percentage: 4 }
    ];
  } catch (error) {
    console.error(`Error fetching sales by category for shop ${shopId}:`, error);
    return [];
  }
};

/**
 * Get sales by time for a merchant
 * @param shopId Shop ID
 * @param period Time period
 * @returns Promise with sales by time
 */
export const getSalesByTime = async (
  shopId: string,
  period: SalesSummary['period'] = 'day'
): Promise<SalesByTime[]> => {
  try {
    // In a real app, this would fetch from a database
    
    // For now, return mock data
    return Array.from({ length: 24 }, (_, i) => ({
      hour: i,
      amount: Math.floor(Math.random() * 1000) + 100,
      orders: Math.floor(Math.random() * 20) + 1
    }));
  } catch (error) {
    console.error(`Error fetching sales by time for shop ${shopId}:`, error);
    return [];
  }
};

/**
 * Get customer summary for a merchant
 * @param shopId Shop ID
 * @param period Time period
 * @returns Promise with customer summary
 */
export const getCustomerSummary = async (
  shopId: string,
  period: CustomerSummary['period'] = 'month'
): Promise<CustomerSummary> => {
  try {
    // In a real app, this would fetch from a database
    
    // For now, return mock data
    return {
      totalCustomers: 150,
      newCustomers: 30,
      returningCustomers: 120,
      period
    };
  } catch (error) {
    console.error(`Error fetching customer summary for shop ${shopId}:`, error);
    return {
      totalCustomers: 0,
      newCustomers: 0,
      returningCustomers: 0,
      period
    };
  }
};

/**
 * Get earnings summary for a deliverer
 * @param delivererId Deliverer ID
 * @param period Time period
 * @returns Promise with earnings summary
 */
export const getDelivererEarningsSummary = async (
  delivererId: string,
  period: SalesSummary['period'] = 'month'
): Promise<{
  totalEarnings: number;
  totalDeliveries: number;
  averageEarningsPerDelivery: number;
  period: SalesSummary['period'];
}> => {
  try {
    // In a real app, this would fetch from a database
    
    // For now, return mock data
    return {
      totalEarnings: 1500,
      totalDeliveries: 75,
      averageEarningsPerDelivery: 20,
      period
    };
  } catch (error) {
    console.error(`Error fetching earnings summary for deliverer ${delivererId}:`, error);
    return {
      totalEarnings: 0,
      totalDeliveries: 0,
      averageEarningsPerDelivery: 0,
      period
    };
  }
};

/**
 * Get earnings by day for a deliverer
 * @param delivererId Deliverer ID
 * @param days Number of days
 * @returns Promise with earnings by day
 */
export const getDelivererEarningsByDay = async (
  delivererId: string,
  days = 7
): Promise<{
  day: string;
  earnings: number;
  deliveries: number;
}[]> => {
  try {
    // In a real app, this would fetch from a database
    
    // For now, return mock data
    return Array.from({ length: days }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - i);
      
      return {
        day: date.toISOString().split('T')[0],
        earnings: Math.floor(Math.random() * 300) + 100,
        deliveries: Math.floor(Math.random() * 10) + 1
      };
    }).reverse();
  } catch (error) {
    console.error(`Error fetching earnings by day for deliverer ${delivererId}:`, error);
    return [];
  }
};
