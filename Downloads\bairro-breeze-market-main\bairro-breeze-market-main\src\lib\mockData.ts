/**
 * Dados simulados para desenvolvimento
 * Estes dados são usados quando o Supabase não está configurado
 */

import { Tables } from "@/types/database";

export const mockData = {
  // Produtos simulados
  products: [
    {
      id: "1",
      created_at: "2024-04-01T10:00:00Z",
      name: "<PERSON><PERSON> Francês",
      description: "Pão francês fresco, crocante por fora e macio por dentro.",
      price: 0.75,
      original_price: 0.85,
      image: "https://images.unsplash.com/photo-1608198093002-ad4e005484ec",
      category: "Padaria",
      shop_id: "1",
      is_promo: true,
      stock: 100,
      active: true
    },
    {
      id: "2",
      created_at: "2024-04-01T10:00:00Z",
      name: "Filé Mignon Premium kg",
      description: "Corte nobre e macio, perfeito para ocasiões especiais.",
      price: 69.90,
      original_price: null,
      image: "https://images.unsplash.com/photo-1588168333986-5078d3ae3976",
      category: "Açougue",
      shop_id: "2",
      is_promo: false,
      stock: 20,
      active: true
    },
    {
      id: "3",
      created_at: "2024-04-01T10:00:00Z",
      name: "Café Especial 250g",
      description: "Café especial torrado e moído na hora.",
      price: 24.90,
      original_price: 29.90,
      image: "https://images.unsplash.com/photo-1559056199-641a0ac8b55e",
      category: "Mercearia",
      shop_id: "3",
      is_promo: true,
      stock: 50,
      active: true
    },
    {
      id: "4",
      created_at: "2024-04-01T10:00:00Z",
      name: "Pizza Margherita",
      description: "Pizza tradicional italiana com molho de tomate, mussarela e manjericão.",
      price: 39.90,
      original_price: null,
      image: "https://images.unsplash.com/photo-1604382354936-07c5d9983bd3",
      category: "Pizzaria",
      shop_id: "4",
      is_promo: false,
      stock: 15,
      active: true
    },
    {
      id: "5",
      created_at: "2024-04-01T10:00:00Z",
      name: "Salmão Fresco kg",
      description: "Salmão fresco de alta qualidade, ideal para pratos especiais.",
      price: 79.90,
      original_price: 89.90,
      image: "https://images.unsplash.com/photo-1519708227418-c8fd9a32b7a2",
      category: "Peixaria",
      shop_id: "5",
      is_promo: true,
      stock: 10,
      active: true
    }
  ] as Tables<'products'>[],

  // Lojas simuladas
  shops: [
    {
      id: "1",
      created_at: "2024-03-01T10:00:00Z",
      name: "Padaria do Bairro",
      description: "Pães frescos e doces artesanais todos os dias.",
      image: "https://images.unsplash.com/photo-1568254183919-78a4f43a2877",
      cover_image: "https://images.unsplash.com/photo-1509440159596-0249088772ff",
      category: "Padaria",
      delivery_time: "15-30 min",
      delivery_fee: 3.50,
      rating: 4.8,
      address: "Rua das Flores, 123",
      phone: "(11) 99999-1234",
      opening_hours: "06:00 - 20:00",
      featured: true,
      user_id: "user1",
      active: true
    },
    {
      id: "2",
      created_at: "2024-03-01T10:00:00Z",
      name: "Açougue Premium",
      description: "Carnes selecionadas de alta qualidade.",
      image: "https://images.unsplash.com/photo-1545155015-75c9d55a690a",
      cover_image: "https://images.unsplash.com/photo-1607623814075-e51df1bdc82f",
      category: "Açougue",
      delivery_time: "30-45 min",
      delivery_fee: 5.00,
      rating: 4.6,
      address: "Av. Principal, 456",
      phone: "(11) 99999-5678",
      opening_hours: "08:00 - 18:00",
      featured: true,
      user_id: "user2",
      active: true
    },
    {
      id: "3",
      created_at: "2024-03-01T10:00:00Z",
      name: "Café Especial",
      description: "Cafés especiais e produtos gourmet.",
      image: "https://images.unsplash.com/photo-1501339847302-ac426a4a7cbb",
      cover_image: "https://images.unsplash.com/photo-1554118811-1e0d58224f24",
      category: "Cafeteria",
      delivery_time: "20-35 min",
      delivery_fee: 4.00,
      rating: 4.9,
      address: "Rua do Café, 789",
      phone: "(11) 99999-9012",
      opening_hours: "07:00 - 19:00",
      featured: true,
      user_id: "user3",
      active: true
    },
    {
      id: "4",
      created_at: "2024-03-01T10:00:00Z",
      name: "Pizzaria Napolitana",
      description: "Pizzas artesanais no forno a lenha.",
      image: "https://images.unsplash.com/photo-1513104890138-7c749659a591",
      cover_image: "https://images.unsplash.com/photo-1555396273-367ea4eb4db5",
      category: "Pizzaria",
      delivery_time: "30-45 min",
      delivery_fee: 5.00,
      rating: 4.7,
      address: "Av. Itália, 321",
      phone: "(11) 99999-3456",
      opening_hours: "18:00 - 23:00",
      featured: false,
      user_id: "user4",
      active: true
    },
    {
      id: "5",
      created_at: "2024-03-01T10:00:00Z",
      name: "Peixaria do Mar",
      description: "Peixes e frutos do mar frescos todos os dias.",
      image: "https://images.unsplash.com/photo-1498654896293-37aacf113fd9",
      cover_image: "https://images.unsplash.com/photo-1534080564583-6be75777b70a",
      category: "Peixaria",
      delivery_time: "30-50 min",
      delivery_fee: 6.00,
      rating: 4.5,
      address: "Rua do Porto, 654",
      phone: "(11) 99999-7890",
      opening_hours: "08:00 - 17:00",
      featured: false,
      user_id: "user5",
      active: true
    }
  ] as Tables<'shops'>[],

  // Categorias simuladas
  categories: [
    {
      id: "1",
      created_at: "2024-03-01T10:00:00Z",
      name: "Padaria",
      icon: "Croissant"
    },
    {
      id: "2",
      created_at: "2024-03-01T10:00:00Z",
      name: "Açougue",
      icon: "Beef"
    },
    {
      id: "3",
      created_at: "2024-03-01T10:00:00Z",
      name: "Cafeteria",
      icon: "Coffee"
    },
    {
      id: "4",
      created_at: "2024-03-01T10:00:00Z",
      name: "Pizzaria",
      icon: "Pizza"
    },
    {
      id: "5",
      created_at: "2024-03-01T10:00:00Z",
      name: "Peixaria",
      icon: "Fish"
    },
    {
      id: "6",
      created_at: "2024-03-01T10:00:00Z",
      name: "Mercado",
      icon: "ShoppingCart"
    },
    {
      id: "7",
      created_at: "2024-03-01T10:00:00Z",
      name: "Farmácia",
      icon: "Pill"
    },
    {
      id: "8",
      created_at: "2024-03-01T10:00:00Z",
      name: "Restaurante",
      icon: "Utensils"
    }
  ] as Tables<'categories'>[],

  // Pedidos simulados
  orders: [
    {
      id: "1",
      created_at: "2024-04-18T14:30:00Z",
      status: "delivered",
      total: 45.80,
      delivery_address: "Rua das Flores, 123, Apto 101",
      user_id: "user1",
      shop_id: "1",
      deliverer_id: "deliverer1",
      payment_method: "credit_card",
      payment_status: "paid",
      delivery_instructions: "Deixar na portaria"
    },
    {
      id: "2",
      created_at: "2024-04-18T15:45:00Z",
      status: "in_progress",
      total: 79.90,
      delivery_address: "Av. Principal, 456, Casa 2",
      user_id: "user1",
      shop_id: "2",
      deliverer_id: "deliverer2",
      payment_method: "pix",
      payment_status: "paid",
      delivery_instructions: null
    },
    {
      id: "3",
      created_at: "2024-04-18T16:20:00Z",
      status: "pending",
      total: 24.90,
      delivery_address: "Rua do Café, 789",
      user_id: "user1",
      shop_id: "3",
      deliverer_id: null,
      payment_method: "credit_card",
      payment_status: "paid",
      delivery_instructions: "Tocar a campainha"
    },
    {
      id: "4",
      created_at: "2024-04-17T19:15:00Z",
      status: "delivered",
      total: 39.90,
      delivery_address: "Av. Itália, 321, Bloco B, Apto 202",
      user_id: "user1",
      shop_id: "4",
      deliverer_id: "deliverer1",
      payment_method: "cash",
      payment_status: "paid",
      delivery_instructions: null
    },
    {
      id: "5",
      created_at: "2024-04-16T12:45:00Z",
      status: "cancelled",
      total: 79.90,
      delivery_address: "Rua do Porto, 654",
      user_id: "user1",
      shop_id: "5",
      deliverer_id: null,
      payment_method: "credit_card",
      payment_status: "failed",
      delivery_instructions: null
    }
  ] as Tables<'orders'>[],

  // Perfis simulados
  profiles: [
    {
      id: "user1",
      created_at: "2024-03-01T10:00:00Z",
      updated_at: "2024-04-18T10:00:00Z",
      name: "João Silva",
      role: "customer",
      phone: "(11) 99999-1234",
      address: "Rua das Flores, 123, Apto 101",
      avatar_url: "https://i.pravatar.cc/150?u=user1"
    },
    {
      id: "merchant1",
      created_at: "2024-03-01T10:00:00Z",
      updated_at: "2024-04-18T10:00:00Z",
      name: "Maria Souza",
      role: "merchant",
      phone: "(11) 99999-5678",
      address: "Av. Principal, 456",
      avatar_url: "https://i.pravatar.cc/150?u=merchant1"
    },
    {
      id: "deliverer1",
      created_at: "2024-03-01T10:00:00Z",
      updated_at: "2024-04-18T10:00:00Z",
      name: "Pedro Santos",
      role: "deliverer",
      phone: "(11) 99999-9012",
      address: "Rua do Café, 789",
      avatar_url: "https://i.pravatar.cc/150?u=deliverer1"
    }
  ] as Tables<'profiles'>[]
};

// Função para obter dados simulados por tabela
export const getMockData = <T extends keyof typeof mockData>(
  table: T
): typeof mockData[T] => {
  return mockData[table];
};
