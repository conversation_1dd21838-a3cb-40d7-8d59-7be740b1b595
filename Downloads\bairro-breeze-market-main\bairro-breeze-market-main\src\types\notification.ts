/**
 * Tipos de notificações suportados
 */
export type NotificationType = 'order' | 'delivery' | 'payment' | 'system' | 'promo';

/**
 * Representa uma notificação
 */
export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: NotificationType;
  read: boolean;
  createdAt: string;
  expiresAt?: string;
  data?: Record<string, any>;
  actionUrl?: string;
  actionLabel?: string;
  imageUrl?: string;
}

/**
 * Dados necessários para criar uma nova notificação
 */
export interface CreateNotificationRequest {
  userId: string;
  title: string;
  message: string;
  type: NotificationType;
  data?: Record<string, any>;
  actionUrl?: string;
  actionLabel?: string;
  imageUrl?: string;
  expiresAt?: string;
}

/**
 * Filtros para busca de notificações
 */
export interface NotificationFilters {
  userId?: string;
  type?: NotificationType;
  read?: boolean;
  startDate?: string;
  endDate?: string;
  limit?: number;
  offset?: number;
}

/**
 * Configurações de notificação do usuário
 */
export interface NotificationSettings {
  userId: string;
  email: boolean;
  push: boolean;
  sms: boolean;
  orderUpdates: boolean;
  promotions: boolean;
  systemMessages: boolean;
}
