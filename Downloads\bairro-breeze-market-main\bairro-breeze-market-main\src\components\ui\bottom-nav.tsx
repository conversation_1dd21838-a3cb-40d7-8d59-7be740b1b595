import { Home, Search, ShoppingBag, User, Package, Store, Truck, TrendingUp, MapPin, Wallet } from "lucide-react";
import { cn } from "@/lib/utils";
import { Link, useLocation } from "react-router-dom";
import { useAuth } from "@/hooks/useAuth";
import { motion, AnimatePresence } from "framer-motion";
import { useEffect, useState } from "react";
import { useCart } from "@/hooks/useCart";

interface BottomNavItemProps {
  icon: React.ReactNode;
  label: string;
  isActive?: boolean;
  href: string;
  badge?: number | string;
}

function BottomNavItem({ icon, label, isActive = false, href, badge }: BottomNavItemProps) {
  return (
    <Link
      to={href}
      className={cn(
        "flex flex-col items-center justify-center flex-1 py-2 relative",
        isActive ? "text-cta" : "text-muted-foreground"
      )}
    >
      <motion.div
        className="relative"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        transition={{ type: "spring", stiffness: 400, damping: 17 }}
      >
        <motion.div
          animate={isActive ? { y: [0, -3, 0] } : { y: 0 }}
          transition={isActive ? { repeat: Infinity, duration: 2 } : {}}
        >
          {icon}
        </motion.div>
        {badge && (
          <motion.span
            className="absolute -top-2 -right-2 bg-cta text-white text-xs rounded-full flex items-center justify-center"
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ type: "spring", stiffness: 400, damping: 17 }}
            style={{ minWidth: "16px", height: "16px" }}
          >
            {badge}
          </motion.span>
        )}
      </motion.div>
      <span className="text-xs mt-1">{label}</span>
      {isActive && (
        <motion.div
          className="absolute bottom-0 left-1/4 right-1/4 h-0.5 bg-cta"
          layoutId="bottomNavIndicator"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.2 }}
        />
      )}
    </Link>
  );
}

export function BottomNav() {
  const location = useLocation();
  const pathname = location.pathname;
  const { user } = useAuth();
  const { items } = useCart();
  const userRole = user?.user_metadata?.role || 'customer';
  const [isVisible, setIsVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);

  // Esconder BottomNav quando rolar para baixo e mostrar quando rolar para cima
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      if (currentScrollY > lastScrollY && currentScrollY > 100) {
        // Rolando para baixo
        setIsVisible(false);
      } else {
        // Rolando para cima ou no topo
        setIsVisible(true);
      }

      setLastScrollY(currentScrollY);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [lastScrollY]);

  // Determine which navigation items to show based on user role
  const renderNavItems = () => {
    // Default navigation for customers or not logged in users
    if (!user || userRole === 'customer') {
      return (
        <>
          <BottomNavItem
            icon={<Home className="w-5 h-5" />}
            label="Início"
            isActive={pathname === "/"}
            href="/"
          />
          <BottomNavItem
            icon={<Search className="w-5 h-5" />}
            label="Buscar"
            isActive={pathname === "/search"}
            href="/search"
          />
          <BottomNavItem
            icon={<ShoppingBag className="w-5 h-5" />}
            label="Pedidos"
            isActive={pathname === "/orders"}
            href="/orders"
          />
          <BottomNavItem
            icon={<User className="w-5 h-5" />}
            label="Perfil"
            isActive={pathname === "/profile"}
            href="/profile"
          />
        </>
      );
    }

    // Navigation for merchants
    if (userRole === 'merchant') {
      return (
        <>
          <BottomNavItem
            icon={<Store className="w-5 h-5" />}
            label="Dashboard"
            isActive={pathname === "/merchant"}
            href="/merchant"
          />
          <BottomNavItem
            icon={<Package className="w-5 h-5" />}
            label="Pedidos"
            isActive={pathname.includes("/merchant/orders")}
            href="/merchant/orders"
            badge={3}
          />
          <BottomNavItem
            icon={<TrendingUp className="w-5 h-5" />}
            label="Vendas"
            isActive={pathname.includes("/merchant/sales")}
            href="/merchant/sales"
          />
          <BottomNavItem
            icon={<User className="w-5 h-5" />}
            label="Perfil"
            isActive={pathname === "/profile"}
            href="/profile"
          />
        </>
      );
    }

    // Navigation for deliverers
    if (userRole === 'deliverer') {
      return (
        <>
          <BottomNavItem
            icon={<Truck className="w-5 h-5" />}
            label="Dashboard"
            isActive={pathname === "/deliverer"}
            href="/deliverer"
          />
          <BottomNavItem
            icon={<Package className="w-5 h-5" />}
            label="Entregas"
            isActive={pathname.includes("/deliverer/deliveries")}
            href="/deliverer/deliveries"
            badge={2}
          />
          <BottomNavItem
            icon={<MapPin className="w-5 h-5" />}
            label="Mapa"
            isActive={pathname.includes("/deliverer/map")}
            href="/deliverer/map"
          />
          <BottomNavItem
            icon={<User className="w-5 h-5" />}
            label="Perfil"
            isActive={pathname === "/profile"}
            href="/profile"
          />
        </>
      );
    }

    // Fallback to default navigation
    return (
      <>
        <BottomNavItem
          icon={<Home className="w-5 h-5" />}
          label="Início"
          isActive={pathname === "/"}
          href="/"
        />
        <BottomNavItem
          icon={<Search className="w-5 h-5" />}
          label="Buscar"
          isActive={pathname === "/search"}
          href="/search"
        />
        <BottomNavItem
          icon={<ShoppingBag className="w-5 h-5" />}
          label="Pedidos"
          isActive={pathname === "/orders"}
          href="/orders"
        />
        <BottomNavItem
          icon={<User className="w-5 h-5" />}
          label="Perfil"
          isActive={pathname === "/profile"}
          href="/profile"
        />
      </>
    );
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className="fixed bottom-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-sm border-t shadow-lg md:hidden"
          initial={{ y: 100 }}
          animate={{ y: 0 }}
          exit={{ y: 100 }}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
        >
          <motion.div
            className="flex items-center justify-around"
            layoutId="bottomNavLayout"
          >
            {renderNavItems()}
          </motion.div>

          {/* Indicador de progresso de rolagem */}
          <motion.div
            className="h-0.5 bg-gradient-to-r from-cta to-trust absolute top-0 left-0"
            style={{
              width: `${Math.min((lastScrollY / (document.body.scrollHeight - window.innerHeight)) * 100, 100)}%`,
              scaleX: lastScrollY > 0 ? 1 : 0
            }}
            transition={{ duration: 0.2 }}
          />
        </motion.div>
      )}
    </AnimatePresence>
  );
}
