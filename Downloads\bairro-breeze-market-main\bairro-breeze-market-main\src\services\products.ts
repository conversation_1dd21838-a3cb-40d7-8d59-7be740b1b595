import { Product } from "@/types/product";
import { productService } from "./productService";
import { Tables } from "@/types/database";

// Convert database product to frontend product model
const mapDatabaseProductToProduct = (dbProduct: Tables<'products'>): Product => ({
  id: dbProduct.id,
  name: dbProduct.name,
  image: dbProduct.image,
  price: dbProduct.price,
  originalPrice: dbProduct.original_price || undefined,
  shopName: dbProduct.shop_id, // TODO: Fetch shop name when implementing shop details
  isPromo: dbProduct.is_promo,
});

// Fetch all products
export const fetchProducts = async (): Promise<Product[]> => {
  try {
    const products = await productService.getAll({
      orderBy: { column: 'created_at', ascending: false },
      filters: [{ column: 'active', value: true }]
    });

    return products.map(mapDatabaseProductToProduct);
  } catch (error) {
    console.error('Error fetching products:', error);

    // Return mock data in development mode
    return [
      {
        id: "1",
        name: "Caixa de Morangos Orgânicos 500g",
        image: "https://images.unsplash.com/photo-1465146344425-f00d5f5c8f07",
        price: 15.9,
        originalPrice: 19.9,
        shopName: "Feira da Terra",
        isPromo: true,
      },
      {
        id: "2",
        name: "Filé Mignon Premium kg",
        image: "https://images.unsplash.com/photo-1582562124811-c09040d0a901",
        price: 69.9,
        shopName: "Açougue Premium",
        isPromo: false,
      },
      {
        id: "3",
        name: "Café Especial Torrado 250g",
        image: "https://images.unsplash.com/photo-1559525839-b184a4d698c7",
        price: 24.9,
        originalPrice: 29.9,
        shopName: "Padaria São José",
        isPromo: true,
      },
      {
        id: "4",
        name: "Pão Artesanal Integral",
        image: "https://images.unsplash.com/photo-1509440159596-0249088772ff",
        price: 12.5,
        shopName: "Padaria São José",
        isPromo: false,
      },
      {
        id: "5",
        name: "Queijo Minas Fresco kg",
        image: "https://images.unsplash.com/photo-1486297678162-eb2a19b0a32d",
        price: 39.9,
        originalPrice: 45.9,
        shopName: "Feira da Terra",
        isPromo: true,
      },
      {
        id: "6",
        name: "Picanha Especial kg",
        image: "https://images.unsplash.com/photo-1603048297172-c83f4fcd3244",
        price: 89.9,
        shopName: "Açougue Premium",
        isPromo: false,
      },
      {
        id: "7",
        name: "Bolo de Chocolate",
        image: "https://images.unsplash.com/photo-1606890737304-57a1ca8a5b62",
        price: 32.5,
        originalPrice: 38.9,
        shopName: "Padaria São José",
        isPromo: true,
      },
      {
        id: "8",
        name: "Cesta de Legumes Orgânicos",
        image: "https://images.unsplash.com/photo-1557844352-761f2565b576",
        price: 45.9,
        shopName: "Feira da Terra",
        isPromo: false,
      },
    ];
  }
};

// Fetch product by ID
export const fetchProductById = async (id: string): Promise<Product | null> => {
  try {
    const product = await productService.getById(id);
    return product ? mapDatabaseProductToProduct(product) : null;
  } catch (error) {
    console.error(`Error fetching product with ID ${id}:`, error);

    // Return mock data in development mode
    const mockProducts = await fetchProducts();
    return mockProducts.find(p => p.id === id) || null;
  }
};

// Fetch products by shop ID
export const fetchProductsByShopId = async (shopId: string): Promise<Product[]> => {
  try {
    const products = await productService.getByShopId(shopId);
    return products.map(mapDatabaseProductToProduct);
  } catch (error) {
    console.error(`Error fetching products for shop ${shopId}:`, error);

    // Return mock data in development mode
    const mockProducts = await fetchProducts();
    return mockProducts.filter(p => p.shopName === shopId);
  }
};

// Fetch promotional products
export const fetchPromotionalProducts = async (limit = 10): Promise<Product[]> => {
  try {
    const products = await productService.getPromotions(limit);
    return products.map(mapDatabaseProductToProduct);
  } catch (error) {
    console.error('Error fetching promotional products:', error);

    // Return mock data in development mode
    const mockProducts = await fetchProducts();
    return mockProducts.filter(p => p.isPromo).slice(0, limit);
  }
};

// Search products
export const searchProducts = async (query: string): Promise<Product[]> => {
  try {
    const products = await productService.search(query);
    return products.map(mapDatabaseProductToProduct);
  } catch (error) {
    console.error(`Error searching products with query "${query}":`, error);

    // Return mock data in development mode
    const mockProducts = await fetchProducts();
    return mockProducts.filter(p =>
      p.name.toLowerCase().includes(query.toLowerCase()) ||
      p.shopName.toLowerCase().includes(query.toLowerCase())
    );
  }
};
