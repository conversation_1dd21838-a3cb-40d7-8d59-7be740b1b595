import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import { Link } from "react-router-dom";

interface PromotionBannerProps {
  title: string;
  description: string;
  image: string;
  link: string;
  colorFrom?: string;
  colorTo?: string;
  className?: string;
}

export function PromotionBanner({
  title,
  description,
  image,
  link,
  colorFrom = "from-cta-100",
  colorTo = "to-cta-300",
  className,
}: PromotionBannerProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={cn(
        "relative overflow-hidden rounded-xl bg-gradient-to-r",
        colorFrom,
        colorTo,
        "p-6 shadow-md",
        className
      )}
    >
      <div className="flex flex-col md:flex-row items-center">
        <div className="flex-1 z-10">
          <h3 className="text-xl md:text-2xl font-bold text-white mb-2">{title}</h3>
          <p className="text-white/90 mb-4 max-w-md">{description}</p>
          <Button
            asChild
            className="bg-white text-cta hover:bg-white/90 group"
          >
            <Link to={link}>
              Ver ofertas
              <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Link>
          </Button>
        </div>
        <motion.div
          whileHover={{ scale: 1.05, rotate: 2 }}
          className="flex-shrink-0 mt-4 md:mt-0 md:ml-4"
        >
          <img
            src={image}
            alt={title}
            className="w-32 h-32 md:w-40 md:h-40 object-contain"
          />
        </motion.div>
      </div>
      
      {/* Decorative circles */}
      <div className="absolute top-0 right-0 w-32 h-32 rounded-full bg-white/10 -mr-10 -mt-10" />
      <div className="absolute bottom-0 left-0 w-24 h-24 rounded-full bg-white/10 -ml-10 -mb-10" />
    </motion.div>
  );
}
