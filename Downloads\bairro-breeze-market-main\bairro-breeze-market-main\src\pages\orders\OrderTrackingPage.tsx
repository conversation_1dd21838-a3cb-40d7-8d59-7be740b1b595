import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Navbar } from "@/components/ui/navbar";
import { BottomNav } from "@/components/ui/bottom-nav";
import { OrderTracking } from "@/components/orders/OrderTracking";
import { AnimatedPage } from "@/components/animations";
import { LoadingState } from "@/components/ui/async-state";
import { toast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";

export default function OrderTrackingPage() {
  const { orderId } = useParams<{ orderId: string }>();
  const { user } = useAuth();
  const navigate = useNavigate();
  
  const [isLoading, setIsLoading] = useState(true);
  
  // Check if user is authenticated
  useEffect(() => {
    if (!user) {
      toast({
        title: "Acesso negado",
        description: "Você precisa estar logado para acessar esta página.",
        variant: "destructive",
      });
      navigate("/login", { replace: true });
      return;
    }
    
    if (!orderId) {
      toast({
        title: "Pedido não encontrado",
        description: "O ID do pedido não foi fornecido.",
        variant: "destructive",
      });
      navigate("/orders", { replace: true });
      return;
    }
    
    setIsLoading(false);
  }, [user, orderId, navigate]);
  
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingState message="Carregando..." />
      </div>
    );
  }
  
  return (
    <AnimatedPage className="min-h-screen flex flex-col bg-gray-50">
      <Navbar />
      
      <main className="flex-1 container px-4 py-6 max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">Rastreamento de Pedido</h1>
        
        {orderId && <OrderTracking orderId={orderId} />}
      </main>
      
      <BottomNav />
    </AnimatedPage>
  );
}
