import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog";
import { 
  CreditCard, 
  Plus, 
  Trash2, 
  Loader2, 
  CheckCircle,
  AlertCircle,
  QrCode,
  Banknote
} from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";
import { PaymentMethod, addPaymentMethod, getSavedPaymentMethods, removePaymentMethod } from "@/services/checkout";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

export function UserPaymentForm() {
  const { user } = useAuth();
  
  const [isLoading, setIsLoading] = useState(true);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  
  // Form state
  const [paymentType, setPaymentType] = useState<PaymentMethod["type"]>("credit_card");
  const [cardName, setCardName] = useState("");
  const [cardNumber, setCardNumber] = useState("");
  const [cardExpiry, setCardExpiry] = useState("");
  const [cardCvv, setCardCvv] = useState("");
  
  // Load payment methods
  useEffect(() => {
    const loadPaymentMethods = async () => {
      if (!user) return;
      
      setIsLoading(true);
      
      try {
        const methods = await getSavedPaymentMethods();
        setPaymentMethods(methods);
      } catch (error) {
        console.error("Error loading payment methods:", error);
        toast({
          title: "Erro ao carregar métodos de pagamento",
          description: "Não foi possível carregar seus métodos de pagamento. Tente novamente mais tarde.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    loadPaymentMethods();
  }, [user]);
  
  // Reset form
  const resetForm = () => {
    setPaymentType("credit_card");
    setCardName("");
    setCardNumber("");
    setCardExpiry("");
    setCardCvv("");
  };
  
  // Format card number
  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, "").replace(/[^0-9]/gi, "");
    const matches = v.match(/\d{4,16}/g);
    const match = (matches && matches[0]) || "";
    const parts = [];
    
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    
    if (parts.length) {
      return parts.join(" ");
    } else {
      return value;
    }
  };
  
  // Format expiry date
  const formatExpiryDate = (value: string) => {
    const v = value.replace(/\s+/g, "").replace(/[^0-9]/gi, "");
    
    if (v.length >= 2) {
      return `${v.substring(0, 2)}/${v.substring(2, 4)}`;
    }
    
    return v;
  };
  
  // Handle card number change
  const handleCardNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formattedValue = formatCardNumber(e.target.value);
    setCardNumber(formattedValue);
  };
  
  // Handle expiry date change
  const handleExpiryDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formattedValue = formatExpiryDate(e.target.value);
    setCardExpiry(formattedValue);
  };
  
  // Handle add payment method
  const handleAddPaymentMethod = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user) {
      toast({
        title: "Erro",
        description: "Você precisa estar logado para adicionar um método de pagamento.",
        variant: "destructive",
      });
      return;
    }
    
    // Validate form
    if (paymentType === "credit_card" || paymentType === "debit_card") {
      if (!cardName.trim()) {
        toast({
          title: "Nome obrigatório",
          description: "Por favor, informe o nome impresso no cartão.",
          variant: "destructive",
        });
        return;
      }
      
      if (cardNumber.replace(/\s+/g, "").length < 16) {
        toast({
          title: "Número de cartão inválido",
          description: "Por favor, informe um número de cartão válido.",
          variant: "destructive",
        });
        return;
      }
      
      if (cardExpiry.length < 5) {
        toast({
          title: "Data de validade inválida",
          description: "Por favor, informe a data de validade no formato MM/AA.",
          variant: "destructive",
        });
        return;
      }
      
      if (cardCvv.length < 3) {
        toast({
          title: "CVV inválido",
          description: "Por favor, informe o código de segurança do cartão.",
          variant: "destructive",
        });
        return;
      }
    }
    
    setIsSaving(true);
    
    try {
      let newPaymentMethod: Omit<PaymentMethod, "id">;
      
      if (paymentType === "credit_card" || paymentType === "debit_card") {
        newPaymentMethod = {
          type: paymentType,
          name: cardName,
          last4: cardNumber.replace(/\s+/g, "").slice(-4),
          expiryDate: cardExpiry,
        };
      } else {
        newPaymentMethod = {
          type: paymentType,
          name: paymentType === "pix" ? "PIX" : "Dinheiro",
        };
      }
      
      const addedMethod = await addPaymentMethod(newPaymentMethod);
      
      setPaymentMethods([...paymentMethods, addedMethod]);
      
      toast({
        title: "Método de pagamento adicionado",
        description: "Seu método de pagamento foi adicionado com sucesso.",
      });
      
      resetForm();
      setIsAddDialogOpen(false);
    } catch (error) {
      console.error("Error adding payment method:", error);
      toast({
        title: "Erro ao adicionar método de pagamento",
        description: "Não foi possível adicionar seu método de pagamento. Tente novamente mais tarde.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };
  
  // Handle delete payment method
  const handleDeletePaymentMethod = async () => {
    if (!user || !selectedPaymentMethod) {
      toast({
        title: "Erro",
        description: "Você precisa estar logado para remover um método de pagamento.",
        variant: "destructive",
      });
      return;
    }
    
    setIsSaving(true);
    
    try {
      const success = await removePaymentMethod(selectedPaymentMethod.id);
      
      if (success) {
        setPaymentMethods(paymentMethods.filter(method => method.id !== selectedPaymentMethod.id));
        
        toast({
          title: "Método de pagamento removido",
          description: "Seu método de pagamento foi removido com sucesso.",
        });
        
        setIsDeleteDialogOpen(false);
        setSelectedPaymentMethod(null);
      } else {
        throw new Error("Falha ao remover método de pagamento");
      }
    } catch (error) {
      console.error("Error removing payment method:", error);
      toast({
        title: "Erro ao remover método de pagamento",
        description: "Não foi possível remover seu método de pagamento. Tente novamente mais tarde.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };
  
  // Get payment method icon
  const getPaymentMethodIcon = (type: PaymentMethod["type"]) => {
    switch (type) {
      case "credit_card":
      case "debit_card":
        return <CreditCard className="h-4 w-4" />;
      case "pix":
        return <QrCode className="h-4 w-4" />;
      case "cash":
        return <Banknote className="h-4 w-4" />;
      default:
        return <CreditCard className="h-4 w-4" />;
    }
  };
  
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Métodos de Pagamento</CardTitle>
              <CardDescription>
                Gerencie seus cartões e métodos de pagamento
              </CardDescription>
            </div>
            <Button 
              className="bg-cta hover:bg-cta-dark"
              onClick={() => {
                resetForm();
                setIsAddDialogOpen(true);
              }}
            >
              <Plus className="h-4 w-4 mr-2" />
              Adicionar
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {paymentMethods.length === 0 ? (
            <div className="text-center py-8">
              <CreditCard className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-1">Nenhum método de pagamento cadastrado</h3>
              <p className="text-muted-foreground mb-4">
                Adicione um método de pagamento para facilitar suas compras
              </p>
              <Button 
                className="bg-cta hover:bg-cta-dark"
                onClick={() => {
                  resetForm();
                  setIsAddDialogOpen(true);
                }}
              >
                <Plus className="h-4 w-4 mr-2" />
                Adicionar Método de Pagamento
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {paymentMethods.map((method) => (
                <div 
                  key={method.id} 
                  className="border rounded-lg p-4"
                >
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <div className="p-2 rounded-full bg-muted mr-3">
                        {getPaymentMethodIcon(method.type)}
                      </div>
                      <div>
                        <p className="font-medium">{method.name}</p>
                        {method.last4 && (
                          <p className="text-sm text-muted-foreground">
                            **** **** **** {method.last4}
                            {method.expiryDate && ` • ${method.expiryDate}`}
                          </p>
                        )}
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="text-destructive"
                      onClick={() => {
                        setSelectedPaymentMethod(method);
                        setIsDeleteDialogOpen(true);
                      }}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* Add Payment Method Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Adicionar Método de Pagamento</DialogTitle>
            <DialogDescription>
              Escolha o tipo de pagamento que deseja adicionar
            </DialogDescription>
          </DialogHeader>
          
          <form onSubmit={handleAddPaymentMethod} className="space-y-6">
            <RadioGroup
              value={paymentType}
              onValueChange={(value) => setPaymentType(value as PaymentMethod["type"])}
              className="grid grid-cols-2 gap-4"
            >
              <div className={`border rounded-lg p-4 cursor-pointer ${paymentType === "credit_card" ? "border-cta bg-cta/5" : ""}`}>
                <RadioGroupItem value="credit_card" id="credit_card" className="sr-only" />
                <Label htmlFor="credit_card" className="flex flex-col items-center cursor-pointer">
                  <CreditCard className={`h-8 w-8 mb-2 ${paymentType === "credit_card" ? "text-cta" : "text-muted-foreground"}`} />
                  <span className="font-medium">Cartão de Crédito</span>
                </Label>
              </div>
              
              <div className={`border rounded-lg p-4 cursor-pointer ${paymentType === "debit_card" ? "border-cta bg-cta/5" : ""}`}>
                <RadioGroupItem value="debit_card" id="debit_card" className="sr-only" />
                <Label htmlFor="debit_card" className="flex flex-col items-center cursor-pointer">
                  <CreditCard className={`h-8 w-8 mb-2 ${paymentType === "debit_card" ? "text-cta" : "text-muted-foreground"}`} />
                  <span className="font-medium">Cartão de Débito</span>
                </Label>
              </div>
              
              <div className={`border rounded-lg p-4 cursor-pointer ${paymentType === "pix" ? "border-cta bg-cta/5" : ""}`}>
                <RadioGroupItem value="pix" id="pix" className="sr-only" />
                <Label htmlFor="pix" className="flex flex-col items-center cursor-pointer">
                  <QrCode className={`h-8 w-8 mb-2 ${paymentType === "pix" ? "text-cta" : "text-muted-foreground"}`} />
                  <span className="font-medium">PIX</span>
                </Label>
              </div>
              
              <div className={`border rounded-lg p-4 cursor-pointer ${paymentType === "cash" ? "border-cta bg-cta/5" : ""}`}>
                <RadioGroupItem value="cash" id="cash" className="sr-only" />
                <Label htmlFor="cash" className="flex flex-col items-center cursor-pointer">
                  <Banknote className={`h-8 w-8 mb-2 ${paymentType === "cash" ? "text-cta" : "text-muted-foreground"}`} />
                  <span className="font-medium">Dinheiro</span>
                </Label>
              </div>
            </RadioGroup>
            
            {(paymentType === "credit_card" || paymentType === "debit_card") && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="cardName">Nome no cartão</Label>
                  <Input
                    id="cardName"
                    value={cardName}
                    onChange={(e) => setCardName(e.target.value)}
                    placeholder="Nome impresso no cartão"
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="cardNumber">Número do cartão</Label>
                  <Input
                    id="cardNumber"
                    value={cardNumber}
                    onChange={handleCardNumberChange}
                    placeholder="0000 0000 0000 0000"
                    maxLength={19}
                    required
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="cardExpiry">Validade</Label>
                    <Input
                      id="cardExpiry"
                      value={cardExpiry}
                      onChange={handleExpiryDateChange}
                      placeholder="MM/AA"
                      maxLength={5}
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="cardCvv">CVV</Label>
                    <Input
                      id="cardCvv"
                      value={cardCvv}
                      onChange={(e) => setCardCvv(e.target.value.replace(/\D/g, ""))}
                      placeholder="123"
                      maxLength={4}
                      required
                    />
                  </div>
                </div>
              </div>
            )}
            
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsAddDialogOpen(false)}
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                className="bg-cta hover:bg-cta-dark"
                disabled={isSaving}
              >
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Salvando...
                  </>
                ) : (
                  <>
                    <Plus className="mr-2 h-4 w-4" />
                    Adicionar
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
      
      {/* Delete Payment Method Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Remover Método de Pagamento</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja remover este método de pagamento? Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>
          
          {selectedPaymentMethod && (
            <div className="py-4">
              <div className="flex items-center">
                <div className="p-2 rounded-full bg-muted mr-3">
                  {getPaymentMethodIcon(selectedPaymentMethod.type)}
                </div>
                <div>
                  <p className="font-medium">{selectedPaymentMethod.name}</p>
                  {selectedPaymentMethod.last4 && (
                    <p className="text-sm text-muted-foreground">
                      **** **** **** {selectedPaymentMethod.last4}
                      {selectedPaymentMethod.expiryDate && ` • ${selectedPaymentMethod.expiryDate}`}
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancelar
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeletePaymentMethod}
              disabled={isSaving}
            >
              {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Removendo...
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Remover
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
