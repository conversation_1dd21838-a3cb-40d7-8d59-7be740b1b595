import { supabaseClient } from '@/lib/supabase';

/**
 * Send password reset email
 * @param email User email
 * @returns Promise with success status
 */
export const sendPasswordResetEmail = async (email: string): Promise<boolean> => {
  try {
    const { error } = await supabaseClient.auth.resetPasswordForEmail(email, {
      redirectTo: `${window.location.origin}/reset-password`,
    });
    
    if (error) {
      console.error('Error sending password reset email:', error);
      throw error;
    }
    
    return true;
  } catch (error) {
    console.error('Error in sendPasswordResetEmail:', error);
    
    // In development mode, simulate success
    if (process.env.NODE_ENV === 'development') {
      console.info(`[DEV] Password reset email would be sent to ${email}`);
      return true;
    }
    
    return false;
  }
};

/**
 * Reset password with token
 * @param newPassword New password
 * @returns Promise with success status
 */
export const resetPassword = async (newPassword: string): Promise<boolean> => {
  try {
    const { error } = await supabaseClient.auth.updateUser({
      password: newPassword,
    });
    
    if (error) {
      console.error('Error resetting password:', error);
      throw error;
    }
    
    return true;
  } catch (error) {
    console.error('Error in resetPassword:', error);
    
    // In development mode, simulate success
    if (process.env.NODE_ENV === 'development') {
      console.info('[DEV] Password would be reset');
      return true;
    }
    
    return false;
  }
};

/**
 * Verify email with token
 * @returns Promise with success status
 */
export const verifyEmail = async (): Promise<boolean> => {
  try {
    // The email verification is handled automatically by Supabase
    // when the user clicks the link in the email
    // This function is just for checking if the verification was successful
    
    const { data, error } = await supabaseClient.auth.getSession();
    
    if (error) {
      console.error('Error verifying email:', error);
      throw error;
    }
    
    // Check if the user is verified
    return data.session?.user?.email_confirmed_at != null;
  } catch (error) {
    console.error('Error in verifyEmail:', error);
    
    // In development mode, simulate success
    if (process.env.NODE_ENV === 'development') {
      console.info('[DEV] Email would be verified');
      return true;
    }
    
    return false;
  }
};

/**
 * Update user email
 * @param email New email
 * @returns Promise with success status
 */
export const updateEmail = async (email: string): Promise<boolean> => {
  try {
    const { error } = await supabaseClient.auth.updateUser({
      email,
    });
    
    if (error) {
      console.error('Error updating email:', error);
      throw error;
    }
    
    return true;
  } catch (error) {
    console.error('Error in updateEmail:', error);
    
    // In development mode, simulate success
    if (process.env.NODE_ENV === 'development') {
      console.info(`[DEV] Email would be updated to ${email}`);
      return true;
    }
    
    return false;
  }
};

/**
 * Update user password
 * @param currentPassword Current password
 * @param newPassword New password
 * @returns Promise with success status
 */
export const updatePassword = async (
  currentPassword: string,
  newPassword: string
): Promise<boolean> => {
  try {
    // First, verify the current password by signing in
    const { error: signInError } = await supabaseClient.auth.signInWithPassword({
      email: supabaseClient.auth.getSession().then(({ data }) => data.session?.user?.email || ''),
      password: currentPassword,
    });
    
    if (signInError) {
      console.error('Error verifying current password:', signInError);
      throw new Error('Current password is incorrect');
    }
    
    // Then, update the password
    const { error } = await supabaseClient.auth.updateUser({
      password: newPassword,
    });
    
    if (error) {
      console.error('Error updating password:', error);
      throw error;
    }
    
    return true;
  } catch (error) {
    console.error('Error in updatePassword:', error);
    
    // In development mode, simulate success
    if (process.env.NODE_ENV === 'development') {
      console.info('[DEV] Password would be updated');
      return true;
    }
    
    return false;
  }
};
