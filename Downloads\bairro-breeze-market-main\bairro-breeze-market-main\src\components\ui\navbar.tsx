import { Menu, Search, User, LogOut, X, Home, ShoppingBag, Settings } from "lucide-react";
import { Button } from "./button";
import { Input } from "./input";
import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { MiniCart } from "./mini-cart";
import { useAuth } from "@/hooks/useAuth";
import { Link, useLocation } from "react-router-dom";
import { motion, AnimatePresence } from "framer-motion";
import { useMediaQuery } from "@/hooks/use-breakpoint";
import { useDebounce } from "@/hooks/use-memoization";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const { user, signOut } = useAuth();
  const location = useLocation();
  const isDesktop = useMediaQuery("md");

  // Fechar menu mobile quando mudar de rota
  useEffect(() => {
    setIsMenuOpen(false);
  }, [location.pathname]);

  // Detectar scroll para mudar aparência da navbar
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Debounce para busca
  const [debouncedSearch, isSearching] = useDebounce((query: string) => {
    console.log("Searching for:", query);
    // Implementar lógica de busca aqui
  }, 500);

  // Atualizar busca com debounce
  useEffect(() => {
    if (searchQuery.trim()) {
      debouncedSearch(searchQuery);
    }
  }, [searchQuery, debouncedSearch]);

  const handleSignOut = async () => {
    await signOut();
  };

  return (
    <motion.nav
      className={cn(
        "sticky top-0 z-50 w-full transition-all duration-200",
        isScrolled ? "bg-white/95 backdrop-blur-md shadow-md" : "bg-white border-b"
      )}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
    >
      <div className="container flex items-center justify-between h-16 px-4 md:px-6">
        {/* Logo */}
        <div className="flex items-center">
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden mr-2 transition-colors"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <AnimatePresence mode="wait" initial={false}>
              <motion.div
                key={isMenuOpen ? "close" : "menu"}
                initial={{ opacity: 0, rotate: isMenuOpen ? -90 : 90 }}
                animate={{ opacity: 1, rotate: 0 }}
                exit={{ opacity: 0, rotate: isMenuOpen ? 90 : -90 }}
                transition={{ duration: 0.2 }}
              >
                {isMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
              </motion.div>
            </AnimatePresence>
          </Button>
          <Link to="/" className="flex items-center">
            <motion.div
              className="flex items-center"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <span className="text-xl font-bold text-trust-dark">Já</span>
              <span className="text-xl font-bold text-cta">Comprei</span>
            </motion.div>
          </Link>
        </div>

        {/* Search */}
        <div className="hidden md:flex flex-1 mx-8 max-w-md relative">
          <div className="relative w-full">
            <Search className={cn(
              "absolute left-3 top-2.5 h-4 w-4 transition-colors",
              isSearching ? "text-primary" : "text-muted-foreground"
            )} />
            <Input
              type="search"
              placeholder="Buscar produtos, lojas ou categorias..."
              className={cn(
                "pl-10 transition-all",
                isSearching ? "border-primary ring-1 ring-primary" : ""
              )}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center space-x-1">
          <Button variant="ghost" size="icon" className="md:hidden">
            <Search className="w-5 h-5" />
          </Button>

          {user ? (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <User className="w-5 h-5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="bg-white">
                <DropdownMenuLabel>
                  {user.user_metadata.name || 'Minha Conta'}
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <Link to="/profile" className="w-full">Perfil</Link>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Link to="/orders" className="w-full">Meus Pedidos</Link>
                </DropdownMenuItem>
                {user.user_metadata.role === 'merchant' && (
                  <DropdownMenuItem>
                    <Link to="/merchant" className="w-full">Painel do Comerciante</Link>
                  </DropdownMenuItem>
                )}
                {user.user_metadata.role === 'deliverer' && (
                  <DropdownMenuItem>
                    <Link to="/deliverer" className="w-full">Painel do Entregador</Link>
                  </DropdownMenuItem>
                )}
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleSignOut} className="text-red-500">
                  <LogOut className="w-4 h-4 mr-2" />
                  <span>Sair</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            <Button variant="ghost" size="icon" asChild>
              <Link to="/login">
                <User className="w-5 h-5" />
              </Link>
            </Button>
          )}

          <MiniCart />
        </div>
      </div>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            className="md:hidden overflow-hidden"
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3, ease: [0.25, 0.1, 0.25, 1.0] }}
          >
            <div className="px-4 py-3 space-y-3 bg-white border-b">
          <div className="relative">
            <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input type="search" placeholder="Buscar..." className="pl-10" />
          </div>
          <div className="space-y-1">
            <Button variant="ghost" className="w-full justify-start" asChild>
              <Link to="/">Início</Link>
            </Button>
            <Button variant="ghost" className="w-full justify-start">Categorias</Button>
            <Button variant="ghost" className="w-full justify-start" asChild>
              <Link to="/profile">Meu Perfil</Link>
            </Button>
            <Button variant="ghost" className="w-full justify-start" asChild>
              <Link to="/orders">Meus Pedidos</Link>
            </Button>
            {user?.user_metadata.role === 'merchant' && (
              <Button variant="ghost" className="w-full justify-start" asChild>
                <Link to="/merchant">Área do Comerciante</Link>
              </Button>
            )}
            {user?.user_metadata.role === 'deliverer' && (
              <Button variant="ghost" className="w-full justify-start" asChild>
                <Link to="/deliverer">Área do Entregador</Link>
              </Button>
            )}
            {!user && (
              <Button variant="ghost" className="w-full justify-start" asChild>
                <Link to="/login">Entrar</Link>
              </Button>
            )}
            {user && (
              <Button variant="ghost" className="w-full justify-start text-red-500" onClick={handleSignOut}>
                Sair
              </Button>
            )}
          </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.nav>
  );
}
