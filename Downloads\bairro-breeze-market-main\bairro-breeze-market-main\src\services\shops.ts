import { shopService } from './shopService';
import { Tables } from '@/types/database';

// Shop interface for frontend
export interface Shop {
  id: string;
  name: string;
  image: string;
  coverImage?: string;
  category: string;
  deliveryTime: string;
  rating: number;
  deliveryFee: number;
  address?: string;
  phone?: string;
  openingHours?: string;
  description?: string;
  featured: boolean;
}

// Convert database shop to frontend shop model
const mapDatabaseShopToShop = (dbShop: Tables<'shops'>): Shop => ({
  id: dbShop.id,
  name: dbShop.name,
  image: dbShop.image,
  coverImage: dbShop.cover_image || undefined,
  category: dbShop.category,
  deliveryTime: dbShop.delivery_time,
  rating: dbShop.rating,
  deliveryFee: dbShop.delivery_fee,
  address: dbShop.address,
  phone: dbShop.phone,
  openingHours: dbShop.opening_hours,
  description: dbShop.description || undefined,
  featured: dbShop.featured
});

// Mock shops for development mode
const mockShops: Shop[] = [
  {
    id: "1",
    name: "Mercado do Bairro",
    image: "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9",
    coverImage: "https://images.unsplash.com/photo-1542838132-92c53300491e",
    category: "Mercado",
    deliveryTime: "15-30 min",
    rating: 4.8,
    deliveryFee: 5.0,
    address: "Rua das Flores, 123 - Centro",
    phone: "(11) 99999-9999",
    openingHours: "08:00 - 22:00",
    description: "Mercado de bairro com produtos frescos e de qualidade. Atendemos a região com entregas rápidas e preços justos.",
    featured: true,
  },
  {
    id: "2",
    name: "Padaria São José",
    image: "https://images.unsplash.com/photo-1608198093002-ad4e005484ec",
    coverImage: "https://images.unsplash.com/photo-1517433670267-08bbd4be890f",
    category: "Padaria",
    deliveryTime: "10-20 min",
    rating: 4.5,
    deliveryFee: 3.5,
    address: "Av. Principal, 456 - Vila Nova",
    phone: "(11) 98888-8888",
    openingHours: "06:00 - 20:00",
    description: "Padaria tradicional com pães artesanais, bolos, doces e salgados. Café da manhã e lanches a qualquer hora.",
    featured: false,
  },
  {
    id: "3",
    name: "Açougue Premium",
    image: "https://images.unsplash.com/photo-1582562124811-c09040d0a901",
    coverImage: "https://images.unsplash.com/photo-1607623814075-e51df1bdc82f",
    category: "Açougue",
    deliveryTime: "20-35 min",
    rating: 4.7,
    deliveryFee: 6.0,
    address: "Rua dos Açougueiros, 789 - Jardim Paulista",
    phone: "(11) 97777-7777",
    openingHours: "08:00 - 18:00",
    description: "Carnes selecionadas de alta qualidade. Cortes especiais e atendimento personalizado.",
    featured: false,
  },
  {
    id: "4",
    name: "Feira da Terra",
    image: "https://images.unsplash.com/photo-1506484381205-f7945653044d",
    coverImage: "https://images.unsplash.com/photo-1488459716781-31db52582fe9",
    category: "Feira",
    deliveryTime: "25-40 min",
    rating: 4.6,
    deliveryFee: 4.5,
    address: "Rua das Hortaliças, 321 - Jardim Botânico",
    phone: "(11) 96666-6666",
    openingHours: "07:00 - 14:00",
    description: "Frutas, verduras e legumes frescos direto do produtor. Produtos orgânicos e convencionais.",
    featured: true,
  },
];

// Fetch all shops
export const fetchShops = async (): Promise<Shop[]> => {
  try {
    const shops = await shopService.getAll({
      orderBy: { column: 'rating', ascending: false },
      filters: [{ column: 'active', value: true }]
    });
    
    return shops.map(mapDatabaseShopToShop);
  } catch (error) {
    console.error('Error fetching shops:', error);
    return mockShops;
  }
};

// Fetch shop by ID
export const fetchShopById = async (id: string): Promise<Shop | null> => {
  try {
    const shop = await shopService.getById(id);
    return shop ? mapDatabaseShopToShop(shop) : null;
  } catch (error) {
    console.error(`Error fetching shop with ID ${id}:`, error);
    return mockShops.find(s => s.id === id) || null;
  }
};

// Fetch shops by category
export const fetchShopsByCategory = async (category: string): Promise<Shop[]> => {
  try {
    const shops = await shopService.getByCategory(category);
    return shops.map(mapDatabaseShopToShop);
  } catch (error) {
    console.error(`Error fetching shops for category ${category}:`, error);
    return mockShops.filter(s => s.category === category);
  }
};

// Fetch featured shops
export const fetchFeaturedShops = async (limit = 10): Promise<Shop[]> => {
  try {
    const shops = await shopService.getFeatured(limit);
    return shops.map(mapDatabaseShopToShop);
  } catch (error) {
    console.error('Error fetching featured shops:', error);
    return mockShops.filter(s => s.featured).slice(0, limit);
  }
};

// Fetch shop by user ID (merchant)
export const fetchShopByUserId = async (userId: string): Promise<Shop | null> => {
  try {
    const shop = await shopService.getByUserId(userId);
    return shop ? mapDatabaseShopToShop(shop) : null;
  } catch (error) {
    console.error(`Error fetching shop for user ${userId}:`, error);
    return mockShops[0]; // Return first mock shop for development
  }
};

// Search shops
export const searchShops = async (query: string): Promise<Shop[]> => {
  try {
    const shops = await shopService.search(query);
    return shops.map(mapDatabaseShopToShop);
  } catch (error) {
    console.error(`Error searching shops with query "${query}":`, error);
    return mockShops.filter(s => 
      s.name.toLowerCase().includes(query.toLowerCase()) ||
      s.category.toLowerCase().includes(query.toLowerCase()) ||
      (s.description && s.description.toLowerCase().includes(query.toLowerCase()))
    );
  }
};
