# Já Comprei App

Um aplicativo de marketplace local que conecta clientes, comerciantes e entregadores para facilitar compras e entregas rápidas no bairro.

## Visão Geral

O "Já Comprei" é uma plataforma completa que permite aos usuários:

- **Clientes**: Navegar por produtos locais, fazer pedidos e rastrear entregas em tempo real
- **Comerciantes**: Gerenciar produtos, receber pedidos e processar vendas
- **Entregadores**: Aceitar entregas, visualizar rotas e completar entregas

## Funcionalidades Implementadas

### Experiência do Cliente
- Navegação intuitiva por produtos e lojas locais
- Carrinho de compras com validação de produtos da mesma loja
- Fluxo de checkout completo com seleção de endereço e método de pagamento
- Rastreamento de pedidos em tempo real com mapa interativo
- Histórico de pedidos com detalhes completos
- Perfil de usuário com gerenciamento de endereços e métodos de pagamento

### Dashboard do Comerciante
- Gerenciamento de produtos (adicionar, editar, remover)
- Processamento de pedidos (aceitar, recusar, marcar como pronto)
- Configurações da loja (horários, taxas de entrega, categorias)
- Análises de vendas e desempenho
- Gerenciamento de promoções e descontos

### Experiência do Entregador
- Visualização de entregas disponíveis na região
- Navegação com mapa para localizar endereços
- Atualização de status de entrega em tempo real
- Histórico de entregas e ganhos
- Configuração de disponibilidade e área de atuação

### Recursos Técnicos
- Autenticação e autorização baseada em perfis
- Integração com Supabase para backend (ou modo de desenvolvimento com dados simulados)
- Animações fluidas com Framer Motion
- Design responsivo para todas as telas
- Componentes reutilizáveis e bem documentados

## Configuração do Projeto

### Pré-requisitos

- Node.js 16+ e npm
- Conta no Supabase (opcional para desenvolvimento)

## How can I edit this code?

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/860c5386-3f52-432c-853e-cfa608b3a0b0) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS
- Supabase (para autenticação e banco de dados)
- Framer Motion (para animações)
- React Router (para navegação)
- React Query (para gerenciamento de estado e cache)

## Configuração do Ambiente de Desenvolvimento

1. Clone o repositório
2. Instale as dependências com `npm install`
3. Copie o arquivo `.env.example` para `.env`
4. Configure as variáveis de ambiente no arquivo `.env`
   - Para desenvolvimento, você pode deixar as variáveis do Supabase vazias para usar dados simulados
   - Para produção, crie um projeto no Supabase e configure as variáveis `VITE_SUPABASE_URL` e `VITE_SUPABASE_ANON_KEY`
5. Execute o servidor de desenvolvimento com `npm run dev`

### Modo de Desenvolvimento com Dados Simulados

O aplicativo foi projetado para funcionar em dois modos:

1. **Modo de Desenvolvimento**: Quando as variáveis do Supabase não estão configuradas, o aplicativo usa dados simulados para todas as operações. Isso permite que você desenvolva e teste o aplicativo sem precisar configurar um backend real.

2. **Modo de Produção**: Quando as variáveis do Supabase estão configuradas, o aplicativo se conecta ao backend real para todas as operações.

Os dados simulados incluem:
- Produtos e lojas pré-definidos
- Usuários com diferentes perfis (cliente, comerciante, entregador)
- Pedidos com diferentes status
- Simulação de localização e rastreamento para entregas

### Configuração do Supabase (Para Produção)

1. Crie uma conta no [Supabase](https://supabase.com/)
2. Crie um novo projeto
3. No painel do projeto, vá para Settings > API
4. Copie a URL do projeto e a chave anônima para as variáveis de ambiente
5. Configure as tabelas do banco de dados conforme o esquema em `src/types/database.ts`
6. Configure a autenticação conforme necessário

## Estrutura do Projeto

O projeto segue uma arquitetura modular e bem organizada:

```
src/
├── components/       # Componentes React reutilizáveis
│   ├── animations/     # Componentes de animação
│   ├── auth/          # Componentes de autenticação
│   ├── checkout/       # Componentes do fluxo de checkout
│   ├── customer/       # Componentes específicos para clientes
│   ├── deliverer/      # Componentes específicos para entregadores
│   ├── map/            # Componentes de mapa e localização
│   ├── merchant/       # Componentes específicos para comerciantes
│   ├── orders/         # Componentes de pedidos
│   ├── products/       # Componentes de produtos
│   └── ui/             # Componentes de UI base (botões, cards, etc.)
├── hooks/            # Hooks personalizados
├── lib/              # Utilitários e configurações
├── pages/            # Páginas da aplicação
│   ├── auth/          # Páginas de autenticação
│   ├── customer/       # Páginas para clientes
│   ├── deliverer/      # Páginas para entregadores
│   ├── merchant/       # Páginas para comerciantes
│   └── orders/         # Páginas de pedidos
├── services/         # Serviços e APIs
└── types/            # Definições de tipos TypeScript
```

## Fluxos de Usuário

### Fluxo do Cliente
```
[Login/Registro] → [Home/Explorar] → [Detalhes do Produto] → [Adicionar ao Carrinho] → [Carrinho] → [Checkout] → [Confirmação] → [Acompanhar Pedido]
```

### Fluxo do Comerciante
```
[Login/Registro] → [Dashboard] → [Gerenciar Produtos/Pedidos/Configurações]
```

### Fluxo do Entregador
```
[Login/Registro] → [Dashboard] → [Ver Entregas Disponíveis] → [Aceitar Entrega] → [Navegar até Loja] → [Retirar Pedido] → [Entregar ao Cliente] → [Confirmar Entrega]
```

## Como fazer o deploy deste projeto

O "Já Comprei" app está pronto para deploy em produção. Siga as instruções detalhadas no arquivo [DEPLOY.md](./DEPLOY.md) para fazer o deploy em várias plataformas como Vercel, Netlify, Firebase, AWS Amplify ou GitHub Pages.

### Deploy Rápido

```bash
# Construir o aplicativo para produção
npm run build

# Testar o build localmente
npm run preview
```

### CI/CD

Este projeto inclui configuração para GitHub Actions que automatiza o processo de build e deploy. Veja o arquivo `.github/workflows/deploy.yml` para mais detalhes.
