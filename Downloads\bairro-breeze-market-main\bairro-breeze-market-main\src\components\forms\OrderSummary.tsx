import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { ShoppingBag, CheckCircle, Loader2, AlertCircle } from "lucide-react";
import { useCart } from "@/hooks/useCart";
import { useAuth } from "@/hooks/useAuth";
import { createOrder } from "@/services/orders";
import { processPayment } from "@/services/payment";
import { toast } from "@/hooks/use-toast";
import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import { sendOrderConfirmationEmail } from "@/services/notification";
import { formatCurrency } from "@/lib/validations";
import { CreateOrderRequest } from "@/types/order";
import { ProcessPaymentRequest } from "@/types/payment";
import {
  Alert,
  AlertTitle,
  AlertDescription,
} from "@/components/ui/alert";

interface OrderSummaryProps {
  deliveryAddress: string;
  deliveryInstructions?: string;
  paymentMethodId: string;
  onComplete: () => void;
}

export function OrderSummary({ 
  deliveryAddress, 
  deliveryInstructions, 
  paymentMethodId, 
  onComplete 
}: OrderSummaryProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { items, subtotal, clearCart } = useCart();
  const { user } = useAuth();
  const navigate = useNavigate();
  
  // Calcular valores
  const deliveryFee = 5.0;
  const discount = items.some(item => item.price < (item as any).originalPrice) ? 4.0 : 0;
  const total = subtotal + deliveryFee - discount;

  // Validar pedido
  const validateOrder = (): boolean => {
    if (!user) {
      setError("Você precisa estar logado para finalizar a compra.");
      return false;
    }
    
    if (items.length === 0) {
      setError("Seu carrinho está vazio.");
      return false;
    }
    
    if (!deliveryAddress) {
      setError("Endereço de entrega não informado.");
      return false;
    }
    
    if (!paymentMethodId) {
      setError("Método de pagamento não selecionado.");
      return false;
    }
    
    // Verificar se todos os itens são do mesmo estabelecimento
    const shopNames = new Set(items.map(item => item.shopName));
    if (shopNames.size > 1) {
      setError("Não é possível finalizar um pedido com itens de diferentes estabelecimentos.");
      return false;
    }
    
    return true;
  };

  // Processar pedido
  const processOrder = async () => {
    // Limpar erro anterior
    setError(null);
    
    // Validar pedido
    if (!validateOrder()) {
      return;
    }
    
    setIsProcessing(true);
    
    try {
      // Criar pedido
      const orderData: CreateOrderRequest = {
        items: items.map(item => ({
          productId: item.id,
          quantity: item.quantity,
          price: item.price,
          name: item.name,
          image: item.image
        })),
        total,
        deliveryAddress,
        deliveryInstructions,
        paymentMethod: paymentMethodId,
        userId: user!.id,
        shopId: items[0].shopName // Em uma aplicação real, isso seria o ID real da loja
      };
      
      const order = await createOrder(orderData);
      
      if (!order) {
        throw new Error("Falha ao criar pedido");
      }
      
      // Processar pagamento
      const paymentData: ProcessPaymentRequest = {
        orderId: order.id,
        amount: total,
        paymentMethodId,
        userId: user!.id,
        description: `Pedido #${order.id}`,
        metadata: {
          items: items.length,
          deliveryAddress
        }
      };
      
      const paymentResponse = await processPayment(paymentData);
      
      if (!paymentResponse.success) {
        throw new Error(paymentResponse.error?.message || "Falha ao processar pagamento");
      }
      
      // Enviar email de confirmação
      await sendOrderConfirmationEmail(user!.email, order);
      
      // Limpar carrinho
      clearCart();
      
      // Mostrar mensagem de sucesso
      toast({
        title: "Pedido realizado com sucesso!",
        description: "Seu pedido foi recebido e está sendo processado.",
      });
      
      // Completar checkout
      onComplete();
      
      // Navegar para página de pedidos
      setTimeout(() => {
        navigate('/orders');
      }, 1500);
    } catch (error: any) {
      console.error("Error processing order:", error);
      setError(error.message || "Ocorreu um erro ao processar seu pedido. Tente novamente.");
      
      toast({
        title: "Erro ao finalizar pedido",
        description: error.message || "Ocorreu um erro ao processar seu pedido. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Erro</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Resumo do pedido</h3>
        
        <div className="space-y-4 bg-white p-4 rounded-lg border">
          {/* Itens */}
          <div className="space-y-3">
            {items.map((item) => (
              <div key={item.id} className="flex justify-between">
                <div className="flex items-start">
                  <span className="text-muted-foreground mr-2">{item.quantity}x</span>
                  <span>{item.name}</span>
                </div>
                <span>{formatCurrency(item.price * item.quantity)}</span>
              </div>
            ))}
          </div>
          
          <Separator />
          
          {/* Totais */}
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Subtotal</span>
              <span>{formatCurrency(subtotal)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Taxa de entrega</span>
              <span>{formatCurrency(deliveryFee)}</span>
            </div>
            {discount > 0 && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">Desconto</span>
                <span className="text-eco">-{formatCurrency(discount)}</span>
              </div>
            )}
            <Separator />
            <div className="flex justify-between font-bold">
              <span>Total</span>
              <span>{formatCurrency(total)}</span>
            </div>
          </div>
        </div>
        
        {/* Endereço de entrega */}
        <div className="bg-white p-4 rounded-lg border">
          <h4 className="font-medium mb-2">Endereço de entrega</h4>
          <p className="text-sm text-muted-foreground">{deliveryAddress}</p>
          {deliveryInstructions && (
            <p className="text-sm text-muted-foreground mt-2">
              <span className="font-medium">Instruções: </span>
              {deliveryInstructions}
            </p>
          )}
        </div>
        
        {/* Método de pagamento */}
        <div className="bg-white p-4 rounded-lg border">
          <h4 className="font-medium mb-2">Forma de pagamento</h4>
          <p className="text-sm text-muted-foreground">
            {paymentMethodId.includes("pix") ? "PIX" : 
             paymentMethodId.includes("cash") ? "Dinheiro na entrega" : 
             "Cartão de crédito"}
          </p>
        </div>
        
        {/* Botão de confirmação */}
        <Button 
          className="w-full h-12 text-lg bg-cta hover:bg-cta-dark text-white"
          onClick={processOrder}
          disabled={isProcessing}
        >
          {isProcessing ? (
            <div className="flex items-center">
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Processando...
            </div>
          ) : (
            `Finalizar Compra • ${formatCurrency(total)}`
          )}
        </Button>
      </div>
    </div>
  );
}
