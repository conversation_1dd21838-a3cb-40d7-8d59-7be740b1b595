import { useState, useCallback, useEffect } from 'react';
import { toast } from '@/hooks/use-toast';

interface AsyncState<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
}

type AsyncFn<T, <PERSON>rg<PERSON> extends any[]> = (...args: Args) => Promise<T>;

/**
 * Hook para gerenciar operações assíncronas com tratamento de estado e erros
 * @param asyncFunction Função assíncrona a ser executada
 * @param immediate Se verdadeiro, executa a função imediatamente
 * @param initialData Dados iniciais (opcional)
 * @param onSuccess Callback a ser chamado em caso de sucesso
 * @param onError Callback a ser chamado em caso de erro
 * @param showErrorToast Se verdadeiro, mostra um toast de erro automaticamente
 * @returns Estado da operação e função para executá-la
 */
export function useAsync<T, Args extends any[] = any[]>(
  asyncFunction: AsyncFn<T, Args>,
  {
    immediate = false,
    initialData = null,
    onSuccess,
    onError,
    showErrorToast = true,
    retryCount = 0,
    retryDelay = 1000,
  }: {
    immediate?: boolean;
    initialData?: T | null;
    onSuccess?: (data: T) => void;
    onError?: (error: Error) => void;
    showErrorToast?: boolean;
    retryCount?: number;
    retryDelay?: number;
  } = {}
) {
  const [state, setState] = useState<AsyncState<T>>({
    data: initialData,
    loading: immediate,
    error: null,
  });

  const [retries, setRetries] = useState(0);

  // Função para executar a operação assíncrona
  const execute = useCallback(
    async (...args: Args) => {
      setState(prevState => ({ ...prevState, loading: true, error: null }));
      
      try {
        const data = await asyncFunction(...args);
        setState({ data, loading: false, error: null });
        
        if (onSuccess) {
          onSuccess(data);
        }
        
        // Resetar contagem de retentativas
        setRetries(0);
        
        return data;
      } catch (error: any) {
        // Tentar novamente se configurado
        if (retries < retryCount) {
          setRetries(prev => prev + 1);
          
          // Agendar nova tentativa após o delay
          setTimeout(() => {
            execute(...args);
          }, retryDelay);
          
          return null;
        }
        
        const errorObj = error instanceof Error ? error : new Error(error?.message || 'Erro desconhecido');
        setState({ data: null, loading: false, error: errorObj });
        
        if (onError) {
          onError(errorObj);
        }
        
        if (showErrorToast) {
          toast({
            title: 'Erro',
            description: errorObj.message || 'Ocorreu um erro inesperado.',
            variant: 'destructive',
          });
        }
        
        throw errorObj;
      }
    },
    [asyncFunction, onSuccess, onError, showErrorToast, retries, retryCount, retryDelay]
  );

  // Executar imediatamente se configurado
  useEffect(() => {
    if (immediate) {
      execute();
    }
  }, [execute, immediate]);

  // Função para resetar o estado
  const reset = useCallback(() => {
    setState({ data: initialData, loading: false, error: null });
    setRetries(0);
  }, [initialData]);

  return {
    ...state,
    execute,
    reset,
    retry: () => {
      setRetries(0);
      return execute();
    },
    retryCount: retries,
  };
}

/**
 * Hook para gerenciar operações assíncronas com tratamento de estado e erros,
 * específico para operações de API
 */
export function useApiCall<T, Args extends any[] = any[]>(
  apiFunction: AsyncFn<T, Args>,
  options: {
    immediate?: boolean;
    initialData?: T | null;
    onSuccess?: (data: T) => void;
    onError?: (error: Error) => void;
    showErrorToast?: boolean;
    retryCount?: number;
    retryDelay?: number;
    successMessage?: string;
  } = {}
) {
  const { successMessage, ...restOptions } = options;
  
  const onSuccess = (data: T) => {
    if (successMessage) {
      toast({
        title: 'Sucesso',
        description: successMessage,
      });
    }
    
    if (options.onSuccess) {
      options.onSuccess(data);
    }
  };
  
  return useAsync(apiFunction, {
    ...restOptions,
    onSuccess,
  });
}

/**
 * Hook para gerenciar operações de formulário com tratamento de estado e erros
 */
export function useFormSubmit<T, FormData>(
  submitFunction: (data: FormData) => Promise<T>,
  options: {
    onSuccess?: (data: T) => void;
    onError?: (error: Error) => void;
    showErrorToast?: boolean;
    successMessage?: string;
  } = {}
) {
  const { successMessage, ...restOptions } = options;
  
  const onSuccess = (data: T) => {
    if (successMessage) {
      toast({
        title: 'Sucesso',
        description: successMessage,
      });
    }
    
    if (options.onSuccess) {
      options.onSuccess(data);
    }
  };
  
  const { execute, loading, error } = useAsync(submitFunction, {
    ...restOptions,
    onSuccess,
  });
  
  const handleSubmit = async (formData: FormData) => {
    try {
      return await execute(formData);
    } catch (error) {
      // Erro já tratado pelo useAsync
      return null;
    }
  };
  
  return {
    handleSubmit,
    isSubmitting: loading,
    error,
  };
}
