/**
 * Representa um item no carrinho de compras
 */
export interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  image?: string;
  shopId: string;
  shopName: string;
  originalPrice?: number | null;
  category?: string;
}

/**
 * Representa o estado do carrinho de compras
 */
export interface CartState {
  items: CartItem[];
  subtotal: number;
  shopId: string | null;
  shopName: string | null;
}

/**
 * Ações possíveis para o carrinho de compras
 */
export type CartAction =
  | { type: 'ADD_ITEM'; payload: CartItem }
  | { type: 'REMOVE_ITEM'; payload: { id: string } }
  | { type: 'UPDATE_QUANTITY'; payload: { id: string; quantity: number } }
  | { type: 'CLEAR_CART' };
