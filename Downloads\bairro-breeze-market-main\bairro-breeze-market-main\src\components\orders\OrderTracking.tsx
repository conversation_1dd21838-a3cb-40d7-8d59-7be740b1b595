import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  CheckCircle, 
  Clock, 
  Package, 
  ShoppingBag, 
  Truck, 
  MapPin, 
  ChevronRight, 
  Navigation, 
  Phone, 
  MessageCircle,
  Loader2
} from "lucide-react";
import { DeliveryMap } from "@/components/map/DeliveryMap";
import { Order } from "@/types/order";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { motion, AnimatePresence } from "framer-motion";
import { toast } from "@/hooks/use-toast";
import { formatDistance, formatDuration } from "@/services/map";

interface OrderTrackingProps {
  orderId: string;
  className?: string;
}

export function OrderTracking({ orderId, className = "" }: OrderTrackingProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [order, setOrder] = useState<Order | null>(null);
  const [delivererInfo, setDelivererInfo] = useState<any | null>(null);
  const [estimatedArrival, setEstimatedArrival] = useState<Date | null>(null);
  const [distance, setDistance] = useState<number | null>(null);
  const [duration, setDuration] = useState<number | null>(null);
  
  // Fetch order details
  useEffect(() => {
    const fetchOrderDetails = async () => {
      setIsLoading(true);
      
      try {
        // In a real app, this would call an API to get the order details
        // For now, simulate API call with a delay
        setTimeout(() => {
          // Mock order data
          const mockOrder: Order = {
            id: orderId,
            status: "in_progress",
            items: [
              { productId: "1", name: "Pizza Margherita", price: 45.90, quantity: 1, shopName: "Pizzaria Napolitana" },
              { productId: "2", name: "Refrigerante 2L", price: 12.90, quantity: 1, shopName: "Pizzaria Napolitana" }
            ],
            total: 58.80,
            createdAt: new Date().toISOString(),
            shopName: "Pizzaria Napolitana",
            deliveryAddress: "Rua das Flores, 123, Apto 101, Centro, São Paulo, SP",
            paymentMethod: "credit_card",
            delivererId: "deliverer_1",
            delivererName: "João Entregador",
            notes: "Deixar na portaria"
          };
          
          setOrder(mockOrder);
          
          // Mock deliverer info
          setDelivererInfo({
            id: "deliverer_1",
            name: "João Entregador",
            phone: "(11) 99999-9999",
            rating: 4.8,
            totalDeliveries: 245,
            photo: null
          });
          
          // Mock estimated arrival
          const now = new Date();
          now.setMinutes(now.getMinutes() + 25);
          setEstimatedArrival(now);
          
          // Mock distance and duration
          setDistance(3200); // 3.2 km
          setDuration(1500); // 25 minutes
          
          setIsLoading(false);
        }, 1500);
      } catch (error) {
        console.error("Error fetching order details:", error);
        setError("Não foi possível carregar os detalhes do pedido. Tente novamente mais tarde.");
        
        toast({
          title: "Erro ao carregar pedido",
          description: "Não foi possível carregar os detalhes do pedido.",
          variant: "destructive",
        });
        
        setIsLoading(false);
      }
    };
    
    fetchOrderDetails();
  }, [orderId]);
  
  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return format(date, "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });
  };
  
  // Format time
  const formatTime = (date: Date) => {
    return format(date, "HH:mm", { locale: ptBR });
  };
  
  // Get status info
  const getStatusInfo = (status: Order["status"]) => {
    switch (status) {
      case "pending":
        return {
          label: "Pedido recebido",
          description: "Aguardando confirmação do estabelecimento",
          icon: <Clock className="h-5 w-5" />,
          color: "text-yellow-500",
          bgColor: "bg-yellow-100",
          progress: 25
        };
      case "in_progress":
        return {
          label: "Em preparo e entrega",
          description: "Seu pedido está sendo preparado e logo sairá para entrega",
          icon: <Package className="h-5 w-5" />,
          color: "text-blue-500",
          bgColor: "bg-blue-100",
          progress: 50
        };
      case "delivered":
        return {
          label: "Entregue",
          description: "Seu pedido foi entregue com sucesso",
          icon: <CheckCircle className="h-5 w-5" />,
          color: "text-green-500",
          bgColor: "bg-green-100",
          progress: 100
        };
      case "cancelled":
        return {
          label: "Cancelado",
          description: "Seu pedido foi cancelado",
          icon: <ShoppingBag className="h-5 w-5" />,
          color: "text-red-500",
          bgColor: "bg-red-100",
          progress: 0
        };
      default:
        return {
          label: "Status desconhecido",
          description: "Não foi possível determinar o status do pedido",
          icon: <ShoppingBag className="h-5 w-5" />,
          color: "text-gray-500",
          bgColor: "bg-gray-100",
          progress: 0
        };
    }
  };
  
  if (isLoading) {
    return (
      <div className={`flex flex-col items-center justify-center p-8 ${className}`}>
        <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">Carregando informações do pedido...</p>
      </div>
    );
  }
  
  if (error || !order) {
    return (
      <div className={`flex flex-col items-center justify-center p-8 ${className}`}>
        <div className="bg-red-100 p-3 rounded-full mb-4">
          <ShoppingBag className="h-8 w-8 text-red-500" />
        </div>
        <h3 className="text-lg font-medium mb-2">Erro ao carregar pedido</h3>
        <p className="text-muted-foreground text-center mb-6">{error || "Não foi possível carregar as informações do pedido."}</p>
        <Button onClick={() => window.location.reload()}>
          Tentar Novamente
        </Button>
      </div>
    );
  }
  
  const statusInfo = getStatusInfo(order.status);
  
  return (
    <div className={`space-y-6 ${className}`}>
      {/* Order Status */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <CardTitle>Pedido #{order.id.substring(0, 8)}</CardTitle>
              <CardDescription>
                {formatDate(order.createdAt)}
              </CardDescription>
            </div>
            <Badge
              variant="outline"
              className={`${statusInfo.bgColor} ${statusInfo.color} border-none`}
            >
              {statusInfo.icon}
              <span className="ml-1">{statusInfo.label}</span>
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Progress Bar */}
            <div>
              <div className="relative h-2 bg-gray-100 rounded-full overflow-hidden">
                <motion.div
                  className="absolute top-0 left-0 h-full bg-cta"
                  initial={{ width: 0 }}
                  animate={{ width: `${statusInfo.progress}%` }}
                  transition={{ duration: 0.5, ease: "easeInOut" }}
                />
              </div>
              <div className="flex justify-between mt-2 text-xs text-muted-foreground">
                <span>Pedido recebido</span>
                <span>Em preparo</span>
                <span>Entregue</span>
              </div>
            </div>
            
            {/* Status Description */}
            <div className="bg-muted p-4 rounded-lg">
              <p className="text-sm">{statusInfo.description}</p>
              
              {order.status === "in_progress" && estimatedArrival && (
                <div className="mt-2">
                  <p className="text-sm font-medium">Entrega prevista para: {formatTime(estimatedArrival)}</p>
                </div>
              )}
            </div>
            
            {/* Delivery Map */}
            {order.status === "in_progress" && (
              <div className="space-y-4">
                <DeliveryMap 
                  order={order}
                  delivererName={order.delivererName || "Entregador"}
                  estimatedTime={25}
                  className="h-[300px]"
                />
                
                {/* Deliverer Info */}
                {delivererInfo && (
                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div className="flex items-center">
                      <div className="h-10 w-10 rounded-full bg-trust-light flex items-center justify-center mr-3">
                        <Truck className="h-5 w-5 text-trust" />
                      </div>
                      <div>
                        <p className="font-medium">{delivererInfo.name}</p>
                        <div className="flex items-center text-sm text-muted-foreground">
                          <span className="flex items-center">
                            <CheckCircle className="h-3 w-3 mr-1 text-green-500" />
                            {delivererInfo.rating}
                          </span>
                          <span className="mx-2">•</span>
                          <span>{delivererInfo.totalDeliveries} entregas</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
                        <Phone className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
                        <MessageCircle className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                )}
                
                {/* Delivery Info */}
                {distance && duration && (
                  <div className="grid grid-cols-2 gap-4">
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center">
                          <Navigation className="h-5 w-5 text-muted-foreground mr-2" />
                          <div>
                            <p className="text-sm text-muted-foreground">Distância</p>
                            <p className="font-medium">{formatDistance(distance)}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center">
                          <Clock className="h-5 w-5 text-muted-foreground mr-2" />
                          <div>
                            <p className="text-sm text-muted-foreground">Tempo estimado</p>
                            <p className="font-medium">{formatDuration(duration)}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                )}
              </div>
            )}
            
            {/* Order Details */}
            <div>
              <h3 className="text-sm font-medium mb-2">Detalhes do Pedido</h3>
              
              <div className="space-y-4">
                <div>
                  <p className="text-sm text-muted-foreground mb-1">Estabelecimento</p>
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 mr-2 text-cta" />
                    <p className="font-medium">{order.shopName}</p>
                  </div>
                </div>
                
                <div>
                  <p className="text-sm text-muted-foreground mb-1">Endereço de Entrega</p>
                  <div className="flex items-start">
                    <MapPin className="h-4 w-4 mr-2 text-muted-foreground flex-shrink-0 mt-0.5" />
                    <p className="font-medium">{order.deliveryAddress}</p>
                  </div>
                </div>
                
                {order.notes && (
                  <div>
                    <p className="text-sm text-muted-foreground mb-1">Observações</p>
                    <p className="text-sm">{order.notes}</p>
                  </div>
                )}
                
                <div>
                  <p className="text-sm text-muted-foreground mb-2">Itens do Pedido</p>
                  <div className="space-y-2">
                    {order.items.map((item, index) => (
                      <div key={index} className="flex justify-between items-center py-2 border-b">
                        <div className="flex items-center">
                          <div className="mr-3 font-medium">{item.quantity}x</div>
                          <div>{item.name}</div>
                        </div>
                        <div className="font-medium">{formatCurrency(item.price * item.quantity)}</div>
                      </div>
                    ))}
                    <div className="flex justify-between items-center pt-2">
                      <div className="font-medium">Total</div>
                      <div className="font-bold">{formatCurrency(order.total)}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button 
            variant="outline" 
            className="w-full"
            onClick={() => window.history.back()}
          >
            Voltar
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
