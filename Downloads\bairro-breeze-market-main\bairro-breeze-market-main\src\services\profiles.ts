import { profileService } from './profileService';
import { Tables } from '@/types/database';

// User profile interface for frontend
export interface UserProfile {
  id: string;
  name: string;
  role: 'customer' | 'merchant' | 'deliverer';
  phone?: string;
  address?: string;
  avatarUrl?: string;
  createdAt: string;
  updatedAt: string;
}

// Convert database profile to frontend profile model
const mapDatabaseProfileToProfile = (dbProfile: Tables<'profiles'>): UserProfile => ({
  id: dbProfile.id,
  name: dbProfile.name,
  role: dbProfile.role,
  phone: dbProfile.phone || undefined,
  address: dbProfile.address || undefined,
  avatarUrl: dbProfile.avatar_url || undefined,
  createdAt: dbProfile.created_at,
  updatedAt: dbProfile.updated_at
});

// Mock profiles for development mode
const mockProfiles: UserProfile[] = [
  {
    id: 'user1',
    name: '<PERSON>',
    role: 'customer',
    phone: '(11) 99999-9999',
    address: '<PERSON><PERSON>, 123 - Centro',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  },
  {
    id: 'user2',
    name: 'Maria Oliveira',
    role: 'merchant',
    phone: '(11) 98888-8888',
    address: 'Av. Principal, 456 - Vila Nova',
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-02T00:00:00Z'
  },
  {
    id: 'user3',
    name: 'Carlos Santos',
    role: 'deliverer',
    phone: '(11) 97777-7777',
    createdAt: '2024-01-03T00:00:00Z',
    updatedAt: '2024-01-03T00:00:00Z'
  }
];

// Fetch profile by user ID
export const fetchProfileByUserId = async (userId: string): Promise<UserProfile | null> => {
  try {
    const profile = await profileService.getByUserId(userId);
    return profile ? mapDatabaseProfileToProfile(profile) : null;
  } catch (error) {
    console.error(`Error fetching profile for user ${userId}:`, error);
    return mockProfiles.find(p => p.id === userId) || mockProfiles[0];
  }
};

// Update profile
export const updateProfile = async (
  userId: string,
  profileData: Partial<UserProfile>
): Promise<UserProfile | null> => {
  try {
    // Convert frontend profile to database profile
    const dbProfileData: Partial<Tables<'profiles'>> = {
      name: profileData.name,
      phone: profileData.phone,
      address: profileData.address,
      role: profileData.role
    };
    
    const profile = await profileService.updateProfile(userId, dbProfileData);
    return profile ? mapDatabaseProfileToProfile(profile) : null;
  } catch (error) {
    console.error(`Error updating profile for user ${userId}:`, error);
    
    // Update mock profile in development mode
    const profileIndex = mockProfiles.findIndex(p => p.id === userId);
    if (profileIndex >= 0) {
      mockProfiles[profileIndex] = {
        ...mockProfiles[profileIndex],
        ...profileData,
        updatedAt: new Date().toISOString()
      };
      return mockProfiles[profileIndex];
    }
    
    return null;
  }
};

// Upload avatar
export const uploadAvatar = async (userId: string, file: File): Promise<string | null> => {
  try {
    return await profileService.uploadAvatar(userId, file);
  } catch (error) {
    console.error(`Error uploading avatar for user ${userId}:`, error);
    
    // Return mock avatar URL in development mode
    const mockAvatarUrl = 'https://ui-avatars.com/api/?name=User&background=random';
    
    // Update mock profile
    const profileIndex = mockProfiles.findIndex(p => p.id === userId);
    if (profileIndex >= 0) {
      mockProfiles[profileIndex] = {
        ...mockProfiles[profileIndex],
        avatarUrl: mockAvatarUrl,
        updatedAt: new Date().toISOString()
      };
    }
    
    return mockAvatarUrl;
  }
};

// Get users by role
export const fetchUsersByRole = async (
  role: 'customer' | 'merchant' | 'deliverer'
): Promise<UserProfile[]> => {
  try {
    const profiles = await profileService.getByRole(role);
    return profiles.map(mapDatabaseProfileToProfile);
  } catch (error) {
    console.error(`Error fetching users with role ${role}:`, error);
    return mockProfiles.filter(p => p.role === role);
  }
};
