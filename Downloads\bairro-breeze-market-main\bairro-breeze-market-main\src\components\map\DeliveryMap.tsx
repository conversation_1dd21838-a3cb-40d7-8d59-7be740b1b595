import React, { useState, useEffect, useRef } from "react";
import { Truck, Navigation, MapPin, Home, Store } from "lucide-react";
import { motion } from "framer-motion";
import { Order } from "@/types/order";

// Importar serviços de mapa apenas se existirem, caso contrário, criar funções mock
let mapServices: any = {
  getCurrentLocation: async () => ({ latitude: -23.5505, longitude: -46.6333 }),
  trackLocation: (callback: (location: any) => void) => {
    callback({ latitude: -23.5505, longitude: -46.6333 });
    return () => {};
  },
  calculateRoute: async () => null,
  getLocationByAddress: async (address: string) => ({
    latitude: -23.5505,
    longitude: -46.6333,
    address
  })
};

try {
  const importedServices = require("@/services/map");
  mapServices = {
    getCurrentLocation: importedServices.getCurrentLocation,
    trackLocation: importedServices.trackLocation,
    calculateRoute: importedServices.calculateRoute,
    getLocationByAddress: importedServices.getLocationByAddress
  };
} catch (error) {
  console.warn('Serviços de mapa não encontrados, usando funções mock');
}

const { getCurrentLocation, trackLocation, calculateRoute, getLocationByAddress } = mapServices;

interface DeliveryMapProps {
  order: Order;
  delivererName?: string;
  estimatedTime?: number; // in minutes
  className?: string;
}

interface MapMarker {
  type: 'shop' | 'customer' | 'deliverer';
  position: { lat: number; lng: number };
  label?: string;
}

export function DeliveryMap({ order, delivererName = "Entregador", estimatedTime = 15, className = "" }: DeliveryMapProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [shopLocation, setShopLocation] = useState<{ lat: number; lng: number } | null>(null);
  const [customerLocation, setCustomerLocation] = useState<{ lat: number; lng: number } | null>(null);
  const [delivererLocation, setDelivererLocation] = useState<{ lat: number; lng: number } | null>(null);
  const [routePath, setRoutePath] = useState<{ lat: number; lng: number }[]>([]);
  const [progress, setProgress] = useState(0);
  const mapRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number | null>(null);

  // Initialize map and locations
  useEffect(() => {
    const initializeMap = async () => {
      try {
        setIsLoading(true);

        // Get shop location from shop name/address
        const shopLocationResult = await getLocationByAddress(order.shopName);
        if (shopLocationResult) {
          setShopLocation({
            lat: shopLocationResult.latitude,
            lng: shopLocationResult.longitude
          });
        }

        // Get customer location from delivery address
        const customerLocationResult = await getLocationByAddress(order.deliveryAddress);
        if (customerLocationResult) {
          setCustomerLocation({
            lat: customerLocationResult.latitude,
            lng: customerLocationResult.longitude
          });
        }

        // Get initial deliverer location (could be near the shop)
        const delivererLocationResult = await getCurrentLocation();
        setDelivererLocation({
          lat: delivererLocationResult.latitude,
          lng: delivererLocationResult.longitude
        });

        // Calculate route
        if (shopLocationResult && customerLocationResult) {
          const route = await calculateRoute(
            shopLocationResult,
            customerLocationResult
          );

          if (route) {
            // For demo purposes, create a simple route path
            // In a real app, this would use the polyline from the route
            const steps = 20;
            const path = [];

            for (let i = 0; i <= steps; i++) {
              const lat = shopLocationResult.latitude +
                (customerLocationResult.latitude - shopLocationResult.latitude) * (i / steps);
              const lng = shopLocationResult.longitude +
                (customerLocationResult.longitude - shopLocationResult.longitude) * (i / steps);

              path.push({ lat, lng });
            }

            setRoutePath(path);
          }
        }
      } catch (err) {
        console.error("Error initializing map:", err);
        setError("Não foi possível carregar o mapa. Tente novamente mais tarde.");
      } finally {
        setIsLoading(false);
      }
    };

    initializeMap();

    // Start tracking deliverer location
    const unsubscribe = trackLocation((location) => {
      setDelivererLocation({
        lat: location.latitude,
        lng: location.longitude
      });
    });

    return () => {
      unsubscribe();
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [order]);

  // Animate deliverer along the route
  useEffect(() => {
    if (!isLoading && routePath.length > 0 && order.status === "in_progress") {
      let startTime: number | null = null;
      const duration = estimatedTime * 60 * 1000; // Convert minutes to milliseconds

      const animate = (timestamp: number) => {
        if (!startTime) startTime = timestamp;
        const elapsed = timestamp - startTime;
        const newProgress = Math.min(elapsed / duration, 1);
        setProgress(newProgress);

        if (newProgress < 1) {
          animationRef.current = requestAnimationFrame(animate);
        }
      };

      animationRef.current = requestAnimationFrame(animate);
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isLoading, routePath, order.status, estimatedTime]);

  // Calculate current position along the route
  const getCurrentPosition = () => {
    if (routePath.length === 0 || progress === 0) {
      return delivererLocation;
    }

    const index = Math.floor(progress * (routePath.length - 1));
    const nextIndex = Math.min(index + 1, routePath.length - 1);
    const segmentProgress = progress * (routePath.length - 1) - index;

    const currentPoint = routePath[index];
    const nextPoint = routePath[nextIndex];

    return {
      lat: currentPoint.lat + (nextPoint.lat - currentPoint.lat) * segmentProgress,
      lng: currentPoint.lng + (nextPoint.lng - currentPoint.lng) * segmentProgress
    };
  };

  // Render map markers
  const renderMarkers = () => {
    const markers: MapMarker[] = [];

    if (shopLocation) {
      markers.push({
        type: 'shop',
        position: shopLocation,
        label: order.shopName
      });
    }

    if (customerLocation) {
      markers.push({
        type: 'customer',
        position: customerLocation,
        label: 'Seu endereço'
      });
    }

    if (delivererLocation) {
      markers.push({
        type: 'deliverer',
        position: getCurrentPosition() || delivererLocation,
        label: delivererName
      });
    }

    return markers.map((marker, index) => (
      <div
        key={`${marker.type}-${index}`}
        className="absolute transform -translate-x-1/2 -translate-y-1/2"
        style={{
          left: `${50 + (marker.position.lng - (shopLocation?.lng || 0)) * 100}%`,
          top: `${50 + (marker.position.lat - (shopLocation?.lat || 0)) * 100}%`
        }}
      >
        <div className="flex flex-col items-center">
          <div className={`
            w-10 h-10 rounded-full flex items-center justify-center
            ${marker.type === 'shop' ? 'bg-cta-light' : ''}
            ${marker.type === 'customer' ? 'bg-eco-light' : ''}
            ${marker.type === 'deliverer' ? 'bg-trust-light' : ''}
          `}>
            {marker.type === 'shop' && <Store className="h-5 w-5 text-cta" />}
            {marker.type === 'customer' && <Home className="h-5 w-5 text-eco" />}
            {marker.type === 'deliverer' && (
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              >
                <Truck className="h-5 w-5 text-trust" />
              </motion.div>
            )}
          </div>
          <div className="mt-1 px-2 py-1 bg-white rounded-md shadow-sm text-xs font-medium">
            {marker.label}
          </div>
        </div>
      </div>
    ));
  };

  // Render route path
  const renderRoutePath = () => {
    if (routePath.length === 0) return null;

    const points = routePath.map((point, index) => {
      const x = 50 + (point.lng - (shopLocation?.lng || 0)) * 100;
      const y = 50 + (point.lat - (shopLocation?.lat || 0)) * 100;
      return `${x}% ${y}%`;
    }).join(', ');

    return (
      <svg className="absolute inset-0 w-full h-full" style={{ zIndex: 0 }}>
        <polyline
          points={points}
          fill="none"
          stroke="#3b82f6"
          strokeWidth="3"
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeDasharray="5,5"
        />
      </svg>
    );
  };

  if (isLoading) {
    return (
      <div className="relative w-full h-[400px] bg-gray-100 rounded-md flex items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-4 border-cta border-t-transparent" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="relative w-full h-[400px] bg-gray-100 rounded-md flex items-center justify-center">
        <div className="text-center">
          <MapPin className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-1">Erro ao carregar mapa</h3>
          <p className="text-muted-foreground max-w-md mx-auto">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div
      ref={mapRef}
      className={`relative w-full h-[400px] bg-gray-100 rounded-md overflow-hidden ${className}`}
    >
      {/* Map background */}
      <div className="absolute inset-0 bg-gray-200 opacity-50">
        <div className="absolute inset-0 grid grid-cols-4 grid-rows-4">
          {Array.from({ length: 16 }).map((_, i) => (
            <div key={i} className="border border-gray-300" />
          ))}
        </div>
      </div>

      {/* Route path */}
      {renderRoutePath()}

      {/* Markers */}
      {renderMarkers()}

      {/* Progress indicator */}
      <div className="absolute bottom-4 left-4 right-4 bg-white rounded-md p-3 shadow-md">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium">Progresso da entrega</span>
          <span className="text-sm font-medium">{Math.round(progress * 100)}%</span>
        </div>
        <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
          <div
            className="h-full bg-trust rounded-full"
            style={{ width: `${progress * 100}%` }}
          />
        </div>
        <div className="flex justify-between mt-2">
          <div className="flex items-center">
            <Store className="h-4 w-4 text-cta mr-1" />
            <span className="text-xs">Loja</span>
          </div>
          <div className="flex items-center">
            <Home className="h-4 w-4 text-eco mr-1" />
            <span className="text-xs">Seu endereço</span>
          </div>
        </div>
      </div>
    </div>
  );
}


