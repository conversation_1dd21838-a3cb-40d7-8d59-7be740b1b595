import React, { useState } from 'react';
import { useNavigate, Link, useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, Loader2 } from 'lucide-react';

export default function SimpleLogin() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [role, setRole] = useState('customer');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  
  // Get role from URL if provided
  React.useEffect(() => {
    const roleParam = searchParams.get('role');
    if (roleParam && ['customer', 'merchant', 'deliverer'].includes(roleParam)) {
      setRole(roleParam);
    }
  }, [searchParams]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email || !password) {
      setError('Por favor, preencha todos os campos.');
      return;
    }
    
    try {
      setIsLoading(true);
      setError(null);
      
      // Simular login bem-sucedido
      const mockUser = {
        id: 'user1',
        email,
        profile: {
          name: 'Usuário Teste',
          role: role,
          address: 'Rua Teste, 123',
          phone: '(11) 99999-9999'
        }
      };
      
      // Simular atraso de rede
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Salvar usuário no localStorage
      localStorage.setItem('mockUser', JSON.stringify(mockUser));
      
      // Redirecionar com base no perfil
      if (role === 'merchant') {
        navigate('/merchant');
      } else if (role === 'deliverer') {
        navigate('/deliverer');
      } else {
        navigate('/');
      }
    } catch (err: any) {
      setError(err.message || 'Ocorreu um erro ao fazer login. Tente novamente.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container max-w-md mx-auto px-4 py-8">
      <div className="text-center mb-6">
        <Link to="/" className="inline-block">
          <h1 className="text-2xl font-bold text-primary">Já Comprei</h1>
        </Link>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Entrar</CardTitle>
          <CardDescription>
            Acesse sua conta para continuar
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={role} onValueChange={setRole}>
            <TabsList className="grid grid-cols-3 mb-6">
              <TabsTrigger value="customer">Cliente</TabsTrigger>
              <TabsTrigger value="merchant">Comerciante</TabsTrigger>
              <TabsTrigger value="deliverer">Entregador</TabsTrigger>
            </TabsList>
            
            <TabsContent value="customer">
              <p className="text-sm text-muted-foreground mb-6">
                Entre como cliente para comprar produtos de lojas locais.
              </p>
            </TabsContent>
            
            <TabsContent value="merchant">
              <p className="text-sm text-muted-foreground mb-6">
                Entre como comerciante para gerenciar sua loja.
              </p>
            </TabsContent>
            
            <TabsContent value="deliverer">
              <p className="text-sm text-muted-foreground mb-6">
                Entre como entregador para realizar entregas.
              </p>
            </TabsContent>
          </Tabs>
          
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          <form onSubmit={handleLogin} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={isLoading}
              />
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <Label htmlFor="password">Senha</Label>
                <Link to="/forgot-password" className="text-xs text-primary hover:underline">
                  Esqueceu a senha?
                </Link>
              </div>
              <Input
                id="password"
                type="password"
                placeholder="••••••••"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                disabled={isLoading}
              />
            </div>
            
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Entrando...
                </>
              ) : (
                'Entrar'
              )}
            </Button>
          </form>
        </CardContent>
        <CardFooter className="flex flex-col space-y-4">
          <div className="text-sm text-center">
            Não tem uma conta?{' '}
            <Link to={`/register?role=${role}`} className="text-primary hover:underline">
              Cadastre-se
            </Link>
          </div>
          
          <div className="text-xs text-center text-muted-foreground">
            Ao entrar, você concorda com nossos{' '}
            <Link to="/terms" className="hover:underline">
              Termos de Serviço
            </Link>{' '}
            e{' '}
            <Link to="/privacy" className="hover:underline">
              Política de Privacidade
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
