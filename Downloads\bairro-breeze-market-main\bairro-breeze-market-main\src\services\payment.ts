import { supabaseClient } from '@/lib/supabase';
import { Tables } from '@/types/database';
import {
  PaymentMethod,
  Payment,
  PaymentStatus,
  PaymentMethodType,
  ProcessPaymentRequest,
  PaymentResponse
} from '@/types/payment';

// Mock payment methods for development mode
const mockPaymentMethods: PaymentMethod[] = [
  {
    id: '1',
    userId: 'user1',
    type: 'credit_card',
    last4: '4242',
    brand: 'Visa',
    expiryMonth: 12,
    expiryYear: 2025,
    holderName: '<PERSON>',
    isDefault: true,
    createdAt: '2024-04-18T10:00:00Z'
  },
  {
    id: '2',
    userId: 'user1',
    type: 'pix',
    isDefault: false,
    createdAt: '2024-04-18T11:00:00Z'
  },
  {
    id: '3',
    userId: 'user1',
    type: 'cash',
    isDefault: false,
    createdAt: '2024-04-18T12:00:00Z'
  }
];

// Mock payments for development mode
const mockPayments: Payment[] = [
  {
    id: '1',
    orderId: '1',
    userId: 'user1',
    amount: 31.8,
    status: 'completed',
    method: 'credit_card',
    methodId: '1',
    transactionId: 'tx_123456',
    createdAt: '2024-04-18T10:05:00Z'
  },
  {
    id: '2',
    orderId: '2',
    userId: 'user1',
    amount: 69.9,
    status: 'completed',
    method: 'pix',
    methodId: '2',
    transactionId: 'tx_234567',
    createdAt: '2024-04-18T11:35:00Z'
  }
];

/**
 * Get payment methods for a user
 * @param userId User ID
 * @returns Promise with payment methods
 */
export const getPaymentMethods = async (userId: string): Promise<PaymentMethod[]> => {
  try {
    // In a real app, this would fetch from a payment gateway API
    // or from a database table

    // For now, return mock data
    return mockPaymentMethods;
  } catch (error) {
    console.error(`Error fetching payment methods for user ${userId}:`, error);
    return mockPaymentMethods;
  }
};

/**
 * Add a payment method
 * @param userId User ID
 * @param paymentMethod Payment method data
 * @returns Promise with added payment method
 */
export const addPaymentMethod = async (
  userId: string,
  paymentMethod: Omit<PaymentMethod, 'id' | 'isDefault'>
): Promise<PaymentMethod | null> => {
  try {
    // In a real app, this would call a payment gateway API
    // to tokenize and store the payment method

    // For now, create a mock payment method
    const newPaymentMethod: PaymentMethod = {
      id: `${mockPaymentMethods.length + 1}`,
      ...paymentMethod,
      isDefault: mockPaymentMethods.length === 0
    };

    mockPaymentMethods.push(newPaymentMethod);

    return newPaymentMethod;
  } catch (error) {
    console.error(`Error adding payment method for user ${userId}:`, error);
    return null;
  }
};

/**
 * Set default payment method
 * @param userId User ID
 * @param paymentMethodId Payment method ID
 * @returns Promise with success status
 */
export const setDefaultPaymentMethod = async (
  userId: string,
  paymentMethodId: string
): Promise<boolean> => {
  try {
    // In a real app, this would update the database

    // For now, update mock data
    for (const method of mockPaymentMethods) {
      method.isDefault = method.id === paymentMethodId;
    }

    return true;
  } catch (error) {
    console.error(`Error setting default payment method for user ${userId}:`, error);
    return false;
  }
};

/**
 * Remove payment method
 * @param userId User ID
 * @param paymentMethodId Payment method ID
 * @returns Promise with success status
 */
export const removePaymentMethod = async (
  userId: string,
  paymentMethodId: string
): Promise<boolean> => {
  try {
    // In a real app, this would call a payment gateway API
    // to remove the payment method

    // For now, update mock data
    const index = mockPaymentMethods.findIndex(method => method.id === paymentMethodId);
    if (index >= 0) {
      mockPaymentMethods.splice(index, 1);

      // If we removed the default method, set a new default
      if (mockPaymentMethods.length > 0 && !mockPaymentMethods.some(method => method.isDefault)) {
        mockPaymentMethods[0].isDefault = true;
      }

      return true;
    }

    return false;
  } catch (error) {
    console.error(`Error removing payment method for user ${userId}:`, error);
    return false;
  }
};

/**
 * Process payment for an order
 * @param request Payment request data
 * @returns Promise with payment response
 */
export const processPayment = async (
  request: ProcessPaymentRequest
): Promise<PaymentResponse> => {
  try {
    // In a real app, this would call a payment gateway API
    // to process the payment

    // For now, create a mock payment
    const paymentMethod = mockPaymentMethods.find(method => method.id === request.paymentMethodId);

    if (!paymentMethod) {
      return {
        success: false,
        error: {
          code: 'payment_method_not_found',
          message: `Payment method ${request.paymentMethodId} not found`
        }
      };
    }

    // Simulate payment processing
    // In a real app, this would call a payment gateway API
    const now = new Date();
    const payment: Payment = {
      id: `pay_${Date.now()}`,
      orderId: request.orderId,
      userId: request.userId || paymentMethod.userId,
      amount: request.amount,
      status: 'completed', // Assume success for mock
      method: paymentMethod.type,
      methodId: paymentMethod.id,
      transactionId: `tx_${Date.now()}`,
      createdAt: now.toISOString(),
      updatedAt: now.toISOString(),
      metadata: request.metadata
    };

    mockPayments.push(payment);

    // Update order payment status
    try {
      await supabaseClient
        .from('orders')
        .update({ payment_status: 'paid' })
        .eq('id', request.orderId);
    } catch (error) {
      console.error(`Error updating order payment status for order ${request.orderId}:`, error);
    }

    return {
      success: true,
      payment
    };
  } catch (error: any) {
    console.error(`Error processing payment for order ${request.orderId}:`, error);
    return {
      success: false,
      error: {
        code: 'payment_processing_error',
        message: error.message || 'An error occurred while processing the payment',
        details: error
      }
    };
  }
};

/**
 * Get payment by order ID
 * @param orderId Order ID
 * @returns Promise with payment
 */
export const getPaymentByOrderId = async (orderId: string): Promise<Payment | null> => {
  try {
    // In a real app, this would fetch from a database

    // For now, return mock data
    return mockPayments.find(payment => payment.orderId === orderId) || null;
  } catch (error) {
    console.error(`Error fetching payment for order ${orderId}:`, error);
    return null;
  }
};
