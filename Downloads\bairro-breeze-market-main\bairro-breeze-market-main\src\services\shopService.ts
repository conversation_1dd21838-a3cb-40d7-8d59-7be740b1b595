import { ApiService } from './api';
import { Tables, Insertable, Updatable } from '@/types/database';
import { supabaseClient } from '@/lib/supabase';

// Create a typed service for shops
class ShopService extends ApiService<'shops'> {
  constructor() {
    super('shops');
  }

  /**
   * Get shops by category
   * @param category Category name
   * @returns Promise with shops
   */
  async getByCategory(category: string): Promise<Tables<'shops'>[]> {
    try {
      const { data, error } = await supabaseClient
        .from('shops')
        .select('*')
        .eq('category', category)
        .eq('active', true)
        .order('rating', { ascending: false });

      if (error) {
        console.error('Error fetching shops by category:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in getByCategory:', error);
      return [];
    }
  }

  /**
   * Get featured shops
   * @param limit Number of shops to return
   * @returns Promise with featured shops
   */
  async getFeatured(limit = 10): Promise<Tables<'shops'>[]> {
    try {
      const { data, error } = await supabaseClient
        .from('shops')
        .select('*')
        .eq('featured', true)
        .eq('active', true)
        .order('rating', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching featured shops:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in getFeatured:', error);
      return [];
    }
  }

  /**
   * Get shop by user ID (merchant)
   * @param userId User ID
   * @returns Promise with shop
   */
  async getByUserId(userId: string): Promise<Tables<'shops'> | null> {
    try {
      const { data, error } = await supabaseClient
        .from('shops')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        console.error('Error fetching shop by user ID:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in getByUserId:', error);
      return null;
    }
  }

  /**
   * Search shops by name or description
   * @param query Search query
   * @returns Promise with matching shops
   */
  async search(query: string): Promise<Tables<'shops'>[]> {
    try {
      const { data, error } = await supabaseClient
        .from('shops')
        .select('*')
        .or(`name.ilike.%${query}%,description.ilike.%${query}%`)
        .eq('active', true)
        .order('rating', { ascending: false });

      if (error) {
        console.error('Error searching shops:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in search:', error);
      return [];
    }
  }
}

export const shopService = new ShopService();
