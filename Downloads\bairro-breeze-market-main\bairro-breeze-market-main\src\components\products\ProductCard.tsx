import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ShoppingCart, Heart, Star } from "lucide-react";
import { useCart } from "@/hooks/useCart";
import { motion } from "framer-motion";
import { toast } from "@/hooks/use-toast";
import { Link } from "react-router-dom";

interface ProductCardProps {
  id: string;
  name: string;
  description?: string;
  price: number;
  originalPrice?: number;
  image: string;
  rating?: number;
  shopName?: string;
  category?: string;
  isPromo?: boolean;
}

export function ProductCard({
  id,
  name,
  description,
  price,
  originalPrice,
  image,
  rating = 0,
  shopName,
  category,
  isPromo = false
}: ProductCardProps) {
  const [isFavorite, setIsFavorite] = useState(false);
  const { addItem } = useCart();

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const handleAddToCart = () => {
    addItem({
      id,
      name,
      price,
      image,
      quantity: 1
    });

    toast({
      title: "Produto adicionado",
      description: `${name} foi adicionado ao carrinho.`,
    });
  };

  const toggleFavorite = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsFavorite(!isFavorite);
    
    toast({
      title: isFavorite ? "Removido dos favoritos" : "Adicionado aos favoritos",
      description: `${name} foi ${isFavorite ? "removido dos" : "adicionado aos"} favoritos.`,
    });
  };

  const discountPercentage = originalPrice ? Math.round((1 - price / originalPrice) * 100) : 0;

  return (
    <motion.div
      whileHover={{ y: -5, boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)" }}
      className="bg-white rounded-lg overflow-hidden border"
    >
      <Link to={`/product/${id}`} className="block">
        <div className="relative">
          <img
            src={image}
            alt={name}
            className="w-full h-40 object-cover"
          />
          
          {isPromo && (
            <Badge className="absolute top-2 left-2 bg-cta text-white">
              {discountPercentage}% OFF
            </Badge>
          )}
          
          <Button
            variant="ghost"
            size="icon"
            className={`absolute top-2 right-2 rounded-full bg-white/80 hover:bg-white ${
              isFavorite ? "text-red-500" : "text-gray-500"
            }`}
            onClick={toggleFavorite}
          >
            <Heart className={`h-5 w-5 ${isFavorite ? "fill-current" : ""}`} />
          </Button>
        </div>
        
        <div className="p-3">
          <div className="flex justify-between items-start mb-1">
            <h3 className="font-medium text-sm line-clamp-2">{name}</h3>
          </div>
          
          {shopName && (
            <p className="text-xs text-muted-foreground mb-1">{shopName}</p>
          )}
          
          {rating > 0 && (
            <div className="flex items-center mb-2">
              <Star className="h-3 w-3 text-yellow-400 fill-yellow-400 mr-1" />
              <span className="text-xs font-medium">{rating.toFixed(1)}</span>
            </div>
          )}
          
          <div className="flex justify-between items-center mt-2">
            <div>
              <p className="font-bold text-sm">{formatCurrency(price)}</p>
              {originalPrice && (
                <p className="text-xs text-muted-foreground line-through">
                  {formatCurrency(originalPrice)}
                </p>
              )}
            </div>
            
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 rounded-full bg-eco-light text-eco hover:bg-eco hover:text-white"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleAddToCart();
              }}
            >
              <ShoppingCart className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </Link>
    </motion.div>
  );
}
