import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  Card<PERSON>ooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  ShoppingCart, 
  Trash2, 
  Plus, 
  Minus, 
  ArrowRight, 
  Store, 
  ShoppingBag 
} from "lucide-react";
import { useCart } from "@/hooks/useCart";
import { toast } from "@/hooks/use-toast";
import { AnimatedPage } from "@/components/animations";
import { motion } from "framer-motion";

export default function Cart() {
  const { 
    items, 
    removeItem, 
    updateQuantity, 
    subtotal, 
    discount, 
    deliveryFee, 
    total, 
    shopName 
  } = useCart();
  const navigate = useNavigate();
  
  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };
  
  // Handle quantity change
  const handleIncreaseQuantity = (id: string, currentQuantity: number, maxQuantity?: number) => {
    if (maxQuantity !== undefined && currentQuantity >= maxQuantity) {
      toast({
        title: "Quantidade máxima atingida",
        description: `Apenas ${maxQuantity} unidades disponíveis.`,
        variant: "destructive",
      });
      return;
    }
    
    updateQuantity(id, currentQuantity + 1);
  };
  
  const handleDecreaseQuantity = (id: string, currentQuantity: number) => {
    if (currentQuantity > 1) {
      updateQuantity(id, currentQuantity - 1);
    } else {
      removeItem(id);
    }
  };
  
  // Handle remove item
  const handleRemoveItem = (id: string, name: string) => {
    removeItem(id);
    
    toast({
      title: "Item removido",
      description: `${name} foi removido do carrinho.`,
    });
  };
  
  // Handle checkout
  const handleCheckout = () => {
    if (items.length === 0) {
      toast({
        title: "Carrinho vazio",
        description: "Adicione produtos ao carrinho para finalizar a compra.",
        variant: "destructive",
      });
      return;
    }
    
    navigate("/checkout");
  };
  
  // Item animation variants
  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.1,
        duration: 0.3,
      },
    }),
    removed: { opacity: 0, x: -100 },
  };
  
  return (
    <AnimatedPage>
      <div className="container px-4 py-6 max-w-6xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">Meu Carrinho</h1>
        
        {items.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12">
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.5 }}
            >
              <ShoppingCart className="h-16 w-16 text-muted-foreground mb-4" />
            </motion.div>
            <motion.h2
              className="text-xl font-medium mb-2"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.1 }}
            >
              Seu carrinho está vazio
            </motion.h2>
            <motion.p
              className="text-muted-foreground mb-6"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.2 }}
            >
              Adicione produtos ao carrinho para continuar
            </motion.p>
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              <Button 
                className="bg-cta hover:bg-cta-dark"
                onClick={() => navigate("/")}
              >
                Explorar Produtos
              </Button>
            </motion.div>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              {/* Shop info */}
              {shopName && (
                <div className="flex items-center mb-4 p-4 bg-muted rounded-lg">
                  <Store className="h-5 w-5 text-muted-foreground mr-2" />
                  <p className="text-sm">
                    Produtos de <span className="font-medium">{shopName}</span>
                  </p>
                </div>
              )}
              
              {/* Cart items */}
              <Card>
                <CardHeader>
                  <CardTitle>Itens do Carrinho</CardTitle>
                  <CardDescription>
                    {items.length} {items.length === 1 ? "item" : "itens"} no carrinho
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {items.map((item, index) => (
                    <motion.div
                      key={item.id}
                      custom={index}
                      initial="hidden"
                      animate="visible"
                      exit="removed"
                      variants={itemVariants}
                      className="flex items-center border-b pb-4 last:border-0 last:pb-0"
                    >
                      <div className="h-20 w-20 rounded-md overflow-hidden mr-4">
                        <img
                          src={item.image}
                          alt={item.name}
                          className="h-full w-full object-cover"
                        />
                      </div>
                      
                      <div className="flex-1">
                        <h3 className="font-medium">{item.name}</h3>
                        <p className="text-sm text-muted-foreground">{item.shopName}</p>
                        <div className="flex items-center justify-between mt-2">
                          <div className="flex items-center">
                            <Button
                              variant="outline"
                              size="icon"
                              className="h-8 w-8 rounded-full"
                              onClick={() => handleDecreaseQuantity(item.id, item.quantity)}
                            >
                              <Minus className="h-3 w-3" />
                            </Button>
                            <span className="mx-2 w-8 text-center">{item.quantity}</span>
                            <Button
                              variant="outline"
                              size="icon"
                              className="h-8 w-8 rounded-full"
                              onClick={() => handleIncreaseQuantity(item.id, item.quantity, item.maxQuantity)}
                            >
                              <Plus className="h-3 w-3" />
                            </Button>
                          </div>
                          
                          <div className="flex items-center">
                            <div className="text-right mr-4">
                              <p className="font-medium">{formatCurrency(item.price * item.quantity)}</p>
                              {item.originalPrice && (
                                <p className="text-xs text-muted-foreground line-through">
                                  {formatCurrency(item.originalPrice * item.quantity)}
                                </p>
                              )}
                            </div>
                            
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 text-destructive"
                              onClick={() => handleRemoveItem(item.id, item.name)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </CardContent>
              </Card>
            </div>
            
            {/* Order summary */}
            <div>
              <Card className="sticky top-20">
                <CardHeader>
                  <CardTitle>Resumo do Pedido</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Subtotal</span>
                    <span>{formatCurrency(subtotal)}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Taxa de entrega</span>
                    <span>{formatCurrency(deliveryFee)}</span>
                  </div>
                  
                  {discount > 0 && (
                    <div className="flex justify-between text-green-600">
                      <span>Desconto</span>
                      <span>-{formatCurrency(discount)}</span>
                    </div>
                  )}
                  
                  <div className="border-t pt-4 flex justify-between font-medium text-lg">
                    <span>Total</span>
                    <span>{formatCurrency(total)}</span>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button 
                    className="w-full bg-cta hover:bg-cta-dark"
                    onClick={handleCheckout}
                  >
                    Finalizar Compra
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </div>
        )}
      </div>
    </AnimatedPage>
  );
}
