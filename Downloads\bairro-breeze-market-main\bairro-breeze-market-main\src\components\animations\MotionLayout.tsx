import React, { ReactNode } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useLocation } from 'react-router-dom';

interface MotionLayoutProps {
  children: ReactNode;
  className?: string;
  transitionType?: 'fade' | 'slide' | 'zoom' | 'none';
  duration?: number;
}

/**
 * Componente de layout com animações de transição entre páginas
 */
export function MotionLayout({
  children,
  className = '',
  transitionType = 'fade',
  duration = 0.3,
}: MotionLayoutProps) {
  const location = useLocation();

  // Variantes de animação baseadas no tipo de transição
  const getVariants = () => {
    switch (transitionType) {
      case 'fade':
        return {
          initial: { opacity: 0 },
          animate: { opacity: 1 },
          exit: { opacity: 0 },
        };
      case 'slide':
        return {
          initial: { opacity: 0, x: -20 },
          animate: { opacity: 1, x: 0 },
          exit: { opacity: 0, x: 20 },
        };
      case 'zoom':
        return {
          initial: { opacity: 0, scale: 0.95 },
          animate: { opacity: 1, scale: 1 },
          exit: { opacity: 0, scale: 1.05 },
        };
      case 'none':
      default:
        return {
          initial: { opacity: 1 },
          animate: { opacity: 1 },
          exit: { opacity: 1 },
        };
    }
  };

  const variants = getVariants();

  return (
    <div className={className}>
      <AnimatePresence mode="wait">
        <motion.div
          key={location.pathname}
          initial="initial"
          animate="animate"
          exit="exit"
          variants={variants}
          transition={{
            duration,
            ease: [0.25, 0.1, 0.25, 1.0], // Curva de bezier para easing suave
          }}
          className="w-full h-full"
        >
          {children}
        </motion.div>
      </AnimatePresence>
    </div>
  );
}

/**
 * Componente de transição para elementos com animação de entrada e saída
 */
export function MotionTransition({
  children,
  className = '',
  isVisible = true,
  transitionType = 'fade',
  duration = 0.3,
  delay = 0,
}: MotionLayoutProps & {
  isVisible?: boolean;
  delay?: number;
}) {
  // Variantes de animação baseadas no tipo de transição
  const getVariants = () => {
    switch (transitionType) {
      case 'fade':
        return {
          initial: { opacity: 0 },
          animate: { opacity: 1 },
          exit: { opacity: 0 },
        };
      case 'slide':
        return {
          initial: { opacity: 0, y: 20 },
          animate: { opacity: 1, y: 0 },
          exit: { opacity: 0, y: -20 },
        };
      case 'zoom':
        return {
          initial: { opacity: 0, scale: 0.95 },
          animate: { opacity: 1, scale: 1 },
          exit: { opacity: 0, scale: 0.95 },
        };
      case 'none':
      default:
        return {
          initial: { opacity: 1 },
          animate: { opacity: 1 },
          exit: { opacity: 1 },
        };
    }
  };

  const variants = getVariants();

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          className={className}
          initial="initial"
          animate="animate"
          exit="exit"
          variants={variants}
          transition={{
            duration,
            delay,
            ease: [0.25, 0.1, 0.25, 1.0],
          }}
        >
          {children}
        </motion.div>
      )}
    </AnimatePresence>
  );
}
