import { Order, OrderItem } from "@/types/order";
import { CartItem } from "@/types/cart";
import { Tables } from "@/types/database";

// Mock services for development
const orderService = {
  getOrderItems: async (orderId: string) => [],
  getAll: async ({ orderBy }: any) => [],
  getByUserId: async (userId: string) => [],
  getByShopId: async (shopId: string) => [],
  getByDelivererId: async (delivererId: string) => [],
  getByStatus: async (status: string) => [],
  getPendingOrders: async () => [],
  createWithItems: async (order: any, items: any) => null,
  updateStatus: async (orderId: string, status: string) => null,
  assignDeliverer: async (orderId: string, delivererId: string) => null
};

const shopService = {
  getById: async (shopId: string) => null
};

// Mock AuthUser type
interface AuthUser {
  id: string;
  email?: string;
  profile?: {
    name?: string;
    role?: string;
  };
}

// Convert database order to frontend order model
const mapDatabaseOrderToOrder = async (dbOrder: Tables<'orders'>): Promise<Order> => {
  // Fetch order items
  const orderItems = await orderService.getOrderItems(dbOrder.id);

  // Fetch shop details
  let shopName = "Unknown Shop";
  try {
    const shop = await shopService.getById(dbOrder.shop_id);
    if (shop) {
      shopName = shop.name;
    }
  } catch (error) {
    console.error(`Error fetching shop details for order ${dbOrder.id}:`, error);
  }

  // Map order items
  const items: OrderItem[] = orderItems.map(item => ({
    productId: item.product_id,
    quantity: item.quantity,
    price: item.price,
    name: item.name
  }));

  return {
    id: dbOrder.id,
    status: dbOrder.status,
    items,
    total: dbOrder.total,
    createdAt: dbOrder.created_at,
    shopName,
    deliveryAddress: dbOrder.delivery_address
  };
};

// Mock orders for development mode
const mockOrders: Order[] = [
  {
    id: "1",
    status: "delivered",
    items: [
      { productId: "1", quantity: 2, price: 15.9, name: "Caixa de Morangos Orgânicos 500g" }
    ],
    total: 31.8,
    createdAt: "2024-04-18T10:00:00Z",
    shopName: "Feira da Terra",
    deliveryAddress: "Rua das Flores, 123"
  },
  {
    id: "2",
    status: "in_progress",
    items: [
      { productId: "2", quantity: 1, price: 69.9, name: "Filé Mignon Premium kg" }
    ],
    total: 69.9,
    createdAt: "2024-04-18T11:30:00Z",
    shopName: "Açougue Premium",
    deliveryAddress: "Av. Principal, 456"
  },
  {
    id: "3",
    status: "pending",
    items: [
      { productId: "3", quantity: 2, price: 24.9, name: "Café Especial Torrado 250g" },
      { productId: "4", quantity: 1, price: 12.5, name: "Pão Artesanal Integral" }
    ],
    total: 62.3,
    createdAt: "2024-04-18T14:45:00Z",
    shopName: "Padaria São José",
    deliveryAddress: "Rua dos Pinheiros, 789"
  },
  {
    id: "4",
    status: "delivered",
    items: [
      { productId: "4", quantity: 3, price: 12.5, name: "Pão Artesanal Integral" }
    ],
    total: 37.5,
    createdAt: "2024-04-17T09:15:00Z",
    shopName: "Padaria São José",
    deliveryAddress: "Alameda Santos, 234"
  },
  {
    id: "5",
    status: "cancelled",
    items: [
      { productId: "2", quantity: 2, price: 69.9, name: "Filé Mignon Premium kg" }
    ],
    total: 139.8,
    createdAt: "2024-04-16T16:20:00Z",
    shopName: "Açougue Premium",
    deliveryAddress: "Rua Augusta, 567"
  }
];

// Fetch all orders
export const fetchOrders = async (): Promise<Order[]> => {
  try {
    const dbOrders = await orderService.getAll({
      orderBy: { column: 'created_at', ascending: false }
    });

    // Map database orders to frontend orders
    const orders = await Promise.all(dbOrders.map(mapDatabaseOrderToOrder));
    return orders;
  } catch (error) {
    console.error('Error fetching orders:', error);
    return mockOrders;
  }
};

// Fetch orders by user ID
export const fetchOrdersByUserId = async (userId: string): Promise<Order[]> => {
  try {
    const dbOrders = await orderService.getByUserId(userId);
    const orders = await Promise.all(dbOrders.map(mapDatabaseOrderToOrder));
    return orders;
  } catch (error) {
    console.error(`Error fetching orders for user ${userId}:`, error);
    return mockOrders;
  }
};

// Fetch orders by shop ID
export const fetchOrdersByShopId = async (shopId: string): Promise<Order[]> => {
  try {
    const dbOrders = await orderService.getByShopId(shopId);
    const orders = await Promise.all(dbOrders.map(mapDatabaseOrderToOrder));
    return orders;
  } catch (error) {
    console.error(`Error fetching orders for shop ${shopId}:`, error);
    return mockOrders.filter(order => order.shopName === shopId);
  }
};

// Fetch orders by deliverer ID
export const fetchOrdersByDelivererId = async (delivererId: string): Promise<Order[]> => {
  try {
    const dbOrders = await orderService.getByDelivererId(delivererId);
    const orders = await Promise.all(dbOrders.map(mapDatabaseOrderToOrder));
    return orders;
  } catch (error) {
    console.error(`Error fetching orders for deliverer ${delivererId}:`, error);
    return mockOrders.filter(order => order.status === 'in_progress');
  }
};

// Fetch orders by status
export const fetchOrdersByStatus = async (status: Order['status']): Promise<Order[]> => {
  try {
    const dbOrders = await orderService.getByStatus(status);
    const orders = await Promise.all(dbOrders.map(mapDatabaseOrderToOrder));
    return orders;
  } catch (error) {
    console.error(`Error fetching orders with status ${status}:`, error);
    return mockOrders.filter(order => order.status === status);
  }
};

// Fetch pending orders (available for deliverers)
export const fetchPendingOrders = async (): Promise<Order[]> => {
  try {
    const dbOrders = await orderService.getPendingOrders();
    const orders = await Promise.all(dbOrders.map(mapDatabaseOrderToOrder));
    return orders;
  } catch (error) {
    console.error('Error fetching pending orders:', error);
    return mockOrders.filter(order => order.status === 'pending');
  }
};

export interface CreateOrderData {
  items: CartItem[];
  total: number;
  deliveryAddress: string;
  paymentMethod: string;
  user: AuthUser;
}

// Create a new order
export const createOrder = async (orderData: CreateOrderData): Promise<Order> => {
  try {
    // Get shop ID from first item (assumes all items from same shop)
    const shopId = orderData.items[0].shopName; // In a real app, this would be the actual shop ID

    // Create order in database
    const dbOrder = await orderService.createWithItems(
      {
        user_id: orderData.user.id,
        shop_id: shopId,
        total: orderData.total,
        delivery_address: orderData.deliveryAddress,
        payment_method: orderData.paymentMethod,
        payment_status: 'pending',
        status: 'pending',
        created_at: new Date().toISOString()
      },
      orderData.items
    );

    if (!dbOrder) {
      throw new Error('Failed to create order');
    }

    // Convert to frontend order model
    return await mapDatabaseOrderToOrder(dbOrder);
  } catch (error) {
    console.error('Error creating order:', error);

    // Create mock order in development mode
    const mockOrder: Order = {
      id: `${mockOrders.length + 1}`,
      status: 'pending',
      items: orderData.items.map(item => ({
        productId: item.id,
        quantity: item.quantity,
        price: item.price,
        name: item.name
      })),
      total: orderData.total,
      createdAt: new Date().toISOString(),
      shopName: orderData.items[0].shopName,
      deliveryAddress: orderData.deliveryAddress
    };

    mockOrders.unshift(mockOrder);
    return mockOrder;
  }
};

// Update order status
export const updateOrderStatus = async (
  orderId: string,
  status: Order['status']
): Promise<Order | null> => {
  try {
    const dbOrder = await orderService.updateStatus(orderId, status);
    if (!dbOrder) {
      throw new Error(`Failed to update order ${orderId}`);
    }

    return await mapDatabaseOrderToOrder(dbOrder);
  } catch (error) {
    console.error(`Error updating order ${orderId} status to ${status}:`, error);

    // Update mock order in development mode
    const orderIndex = mockOrders.findIndex(order => order.id === orderId);
    if (orderIndex >= 0) {
      mockOrders[orderIndex] = {
        ...mockOrders[orderIndex],
        status: status as any
      };
      return mockOrders[orderIndex];
    }

    return null;
  }
};

// Assign deliverer to order
export const assignDeliverer = async (
  orderId: string,
  delivererId: string
): Promise<Order | null> => {
  try {
    const dbOrder = await orderService.assignDeliverer(orderId, delivererId);
    if (!dbOrder) {
      throw new Error(`Failed to assign deliverer ${delivererId} to order ${orderId}`);
    }

    return await mapDatabaseOrderToOrder(dbOrder);
  } catch (error) {
    console.error(`Error assigning deliverer ${delivererId} to order ${orderId}:`, error);

    // Update mock order in development mode
    const orderIndex = mockOrders.findIndex(order => order.id === orderId);
    if (orderIndex >= 0) {
      mockOrders[orderIndex] = {
        ...mockOrders[orderIndex],
        status: 'in_progress'
      };
      return mockOrders[orderIndex];
    }

    return null;
  }
};
