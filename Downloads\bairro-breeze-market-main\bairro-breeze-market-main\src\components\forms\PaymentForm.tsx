import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { 
  CreditCard, 
  Loader2, 
  Check, 
  Plus, 
  Trash2, 
  Star, 
  QrCode, 
  Banknote 
} from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";
import { motion } from "framer-motion";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { 
  getPaymentMethods, 
  addPaymentMethod, 
  removePaymentMethod, 
  setDefaultPaymentMethod,
  PaymentMethod
} from "@/services/payment";
import { 
  creditCardSchema, 
  CreditCardFormValues,
  formatCreditCardNumber
} from "@/lib/validations";

interface PaymentFormProps {
  onPaymentMethodSelect: (methodId: string) => void;
  onComplete: () => void;
}

export function PaymentForm({ onPaymentMethodSelect, onComplete }: PaymentFormProps) {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [selectedMethodId, setSelectedMethodId] = useState<string | null>(null);
  const [selectedMethodType, setSelectedMethodType] = useState<'credit_card' | 'pix' | 'cash'>('credit_card');
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMethods, setIsLoadingMethods] = useState(false);
  const [isAddCardDialogOpen, setIsAddCardDialogOpen] = useState(false);
  const { user } = useAuth();

  // React Hook Form com validação Zod
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors, isSubmitting, isValid }
  } = useForm<CreditCardFormValues>({
    resolver: zodResolver(creditCardSchema),
    mode: "onChange"
  });

  // Observar o campo de número do cartão para formatação
  const cardNumber = watch("cardNumber");

  // Efeito para formatar o número do cartão
  useEffect(() => {
    if (cardNumber) {
      const formattedCardNumber = formatCreditCardNumber(cardNumber);
      if (formattedCardNumber !== cardNumber) {
        setValue("cardNumber", formattedCardNumber);
      }
    }
  }, [cardNumber, setValue]);

  // Carregar métodos de pagamento salvos
  useEffect(() => {
    const loadPaymentMethods = async () => {
      if (!user) return;
      
      setIsLoadingMethods(true);
      try {
        const methods = await getPaymentMethods(user.id);
        setPaymentMethods(methods);
        
        // Selecionar o método padrão se existir
        const defaultMethod = methods.find(method => method.isDefault);
        if (defaultMethod) {
          setSelectedMethodId(defaultMethod.id);
          onPaymentMethodSelect(defaultMethod.id);
        }
      } catch (error) {
        console.error("Error loading payment methods:", error);
        toast({
          title: "Erro ao carregar métodos de pagamento",
          description: "Não foi possível carregar seus métodos de pagamento salvos.",
          variant: "destructive"
        });
      } finally {
        setIsLoadingMethods(false);
      }
    };
    
    loadPaymentMethods();
  }, [user, onPaymentMethodSelect]);

  // Selecionar método de pagamento
  const handleMethodSelect = (methodId: string) => {
    setSelectedMethodId(methodId);
    onPaymentMethodSelect(methodId);
    
    // Definir como padrão se não for
    const selectedMethod = paymentMethods.find(method => method.id === methodId);
    if (selectedMethod && !selectedMethod.isDefault) {
      setDefaultPaymentMethod(user?.id || "", methodId).catch(error => {
        console.error("Error setting default payment method:", error);
      });
    }
  };

  // Selecionar tipo de pagamento (para novos métodos)
  const handlePaymentTypeSelect = (type: 'credit_card' | 'pix' | 'cash') => {
    setSelectedMethodType(type);
    
    // Para PIX e dinheiro, criar um método temporário se não existir
    if (type !== 'credit_card') {
      const existingMethod = paymentMethods.find(method => method.type === type);
      
      if (existingMethod) {
        setSelectedMethodId(existingMethod.id);
        onPaymentMethodSelect(existingMethod.id);
      } else {
        // Criar método temporário
        addPaymentMethod(user?.id || "", { type }).then(newMethod => {
          if (newMethod) {
            setPaymentMethods(prev => [...prev, newMethod]);
            setSelectedMethodId(newMethod.id);
            onPaymentMethodSelect(newMethod.id);
          }
        }).catch(error => {
          console.error(`Error creating ${type} payment method:`, error);
        });
      }
    }
  };

  // Remover método de pagamento
  const handleRemoveMethod = async (methodId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    
    if (!user) return;
    
    try {
      const success = await removePaymentMethod(user.id, methodId);
      
      if (success) {
        // Atualizar lista de métodos
        setPaymentMethods(prev => prev.filter(method => method.id !== methodId));
        
        // Se o método removido era o selecionado, limpar seleção
        if (selectedMethodId === methodId) {
          setSelectedMethodId(null);
        }
        
        toast({
          title: "Método de pagamento removido",
          description: "Seu método de pagamento foi removido com sucesso.",
        });
      }
    } catch (error) {
      console.error("Error removing payment method:", error);
      toast({
        title: "Erro ao remover método de pagamento",
        description: "Ocorreu um erro ao remover o método de pagamento. Tente novamente.",
        variant: "destructive"
      });
    }
  };

  // Adicionar novo cartão
  const onSubmitCard = async (data: CreditCardFormValues) => {
    if (!user) return;
    
    setIsLoading(true);
    
    try {
      // Validar cartão
      // Em uma aplicação real, isso seria feito com uma API de gateway de pagamento
      
      // Extrair últimos 4 dígitos
      const last4 = data.cardNumber.replace(/\s/g, "").slice(-4);
      
      // Identificar bandeira do cartão
      const cardBrand = getCardBrand(data.cardNumber);
      
      // Adicionar cartão
      const newCard = await addPaymentMethod(user.id, {
        type: "credit_card",
        last4,
        brand: cardBrand,
        expiryMonth: parseInt(data.expiryMonth),
        expiryYear: parseInt(data.expiryYear),
        holderName: data.cardName
      });
      
      if (newCard) {
        // Atualizar lista de métodos
        setPaymentMethods(prev => [...prev, newCard]);
        
        // Selecionar o novo cartão
        setSelectedMethodId(newCard.id);
        onPaymentMethodSelect(newCard.id);
        
        // Fechar diálogo
        setIsAddCardDialogOpen(false);
        
        // Limpar formulário
        reset();
        
        toast({
          title: "Cartão adicionado",
          description: "Seu cartão foi adicionado com sucesso.",
        });
      }
    } catch (error) {
      console.error("Error adding card:", error);
      toast({
        title: "Erro ao adicionar cartão",
        description: "Ocorreu um erro ao adicionar o cartão. Verifique os dados e tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Identificar bandeira do cartão
  const getCardBrand = (cardNumber: string): string => {
    const number = cardNumber.replace(/\s/g, "");
    
    // Visa
    if (/^4/.test(number)) return "Visa";
    
    // Mastercard
    if (/^5[1-5]/.test(number)) return "Mastercard";
    
    // Amex
    if (/^3[47]/.test(number)) return "Amex";
    
    // Elo
    if (/^(636368|438935|504175|451416|636297|5067|4576|4011)/.test(number)) return "Elo";
    
    // Hipercard
    if (/^(606282|3841)/.test(number)) return "Hipercard";
    
    return "Desconhecida";
  };

  // Continuar para próxima etapa
  const handleContinue = () => {
    if (selectedMethodId) {
      onComplete();
    } else {
      toast({
        title: "Selecione um método de pagamento",
        description: "Por favor, selecione um método de pagamento para continuar.",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Tipos de pagamento */}
      <div className="grid grid-cols-3 gap-3">
        <Button
          type="button"
          variant={selectedMethodType === "credit_card" ? "default" : "outline"}
          className={selectedMethodType === "credit_card" ? "bg-eco hover:bg-eco-dark" : ""}
          onClick={() => handlePaymentTypeSelect("credit_card")}
        >
          <CreditCard className="mr-2 h-4 w-4" />
          Cartão
        </Button>
        
        <Button
          type="button"
          variant={selectedMethodType === "pix" ? "default" : "outline"}
          className={selectedMethodType === "pix" ? "bg-eco hover:bg-eco-dark" : ""}
          onClick={() => handlePaymentTypeSelect("pix")}
        >
          <QrCode className="mr-2 h-4 w-4" />
          PIX
        </Button>
        
        <Button
          type="button"
          variant={selectedMethodType === "cash" ? "default" : "outline"}
          className={selectedMethodType === "cash" ? "bg-eco hover:bg-eco-dark" : ""}
          onClick={() => handlePaymentTypeSelect("cash")}
        >
          <Banknote className="mr-2 h-4 w-4" />
          Dinheiro
        </Button>
      </div>

      {/* Métodos de pagamento */}
      {selectedMethodType === "credit_card" && (
        <RadioGroup value={selectedMethodId || ""} onValueChange={handleMethodSelect}>
          <div className="space-y-3">
            {/* Cartões salvos */}
            {paymentMethods.filter(method => method.type === "credit_card").map((method) => (
              <motion.div
                key={method.id}
                className="flex items-start space-x-3 border rounded-md p-3"
                whileHover={{ backgroundColor: "#f9fafb" }}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <RadioGroupItem value={method.id} id={`method-${method.id}`} className="mt-1" />
                <div className="flex-1">
                  <div className="flex justify-between">
                    <Label htmlFor={`method-${method.id}`} className="font-medium cursor-pointer flex items-center">
                      <CreditCard className="mr-2 h-4 w-4" />
                      {method.brand} •••• {method.last4}
                      {method.isDefault && <span className="text-xs text-eco ml-2">(Padrão)</span>}
                    </Label>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={(e) => handleRemoveMethod(method.id, e)}
                      className="h-8 w-8 p-0 text-muted-foreground"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">
                    {method.holderName} • Expira em {method.expiryMonth}/{method.expiryYear}
                  </p>
                </div>
              </motion.div>
            ))}

            {/* Adicionar novo cartão */}
            <Dialog open={isAddCardDialogOpen} onOpenChange={setIsAddCardDialogOpen}>
              <DialogTrigger asChild>
                <Button
                  type="button"
                  variant="outline"
                  className="w-full justify-start"
                >
                  <Plus className="mr-2 h-4 w-4" />
                  Adicionar novo cartão
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                  <DialogTitle>Adicionar novo cartão</DialogTitle>
                  <DialogDescription>
                    Preencha os dados do seu cartão de crédito.
                  </DialogDescription>
                </DialogHeader>
                
                <form onSubmit={handleSubmit(onSubmitCard)} className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="cardNumber">Número do cartão</Label>
                    <Input
                      id="cardNumber"
                      placeholder="0000 0000 0000 0000"
                      {...register("cardNumber")}
                      className={errors.cardNumber ? "border-red-500" : ""}
                    />
                    {errors.cardNumber && (
                      <p className="text-red-500 text-xs">{errors.cardNumber.message}</p>
                    )}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="cardName">Nome no cartão</Label>
                    <Input
                      id="cardName"
                      placeholder="Nome como está no cartão"
                      {...register("cardName")}
                      className={errors.cardName ? "border-red-500" : ""}
                    />
                    {errors.cardName && (
                      <p className="text-red-500 text-xs">{errors.cardName.message}</p>
                    )}
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="expiryMonth">Mês</Label>
                      <Input
                        id="expiryMonth"
                        placeholder="MM"
                        maxLength={2}
                        {...register("expiryMonth")}
                        className={errors.expiryMonth ? "border-red-500" : ""}
                      />
                      {errors.expiryMonth && (
                        <p className="text-red-500 text-xs">{errors.expiryMonth.message}</p>
                      )}
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="expiryYear">Ano</Label>
                      <Input
                        id="expiryYear"
                        placeholder="AA"
                        maxLength={2}
                        {...register("expiryYear")}
                        className={errors.expiryYear ? "border-red-500" : ""}
                      />
                      {errors.expiryYear && (
                        <p className="text-red-500 text-xs">{errors.expiryYear.message}</p>
                      )}
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="cvv">CVV</Label>
                      <Input
                        id="cvv"
                        placeholder="123"
                        maxLength={4}
                        {...register("cvv")}
                        className={errors.cvv ? "border-red-500" : ""}
                      />
                      {errors.cvv && (
                        <p className="text-red-500 text-xs">{errors.cvv.message}</p>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 pt-2">
                    <Checkbox id="saveCard" defaultChecked {...register("saveCard")} />
                    <Label htmlFor="saveCard" className="text-sm">
                      Salvar cartão para compras futuras
                    </Label>
                  </div>
                  
                  <DialogFooter className="pt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsAddCardDialogOpen(false)}
                    >
                      Cancelar
                    </Button>
                    <Button
                      type="submit"
                      disabled={isSubmitting || !isValid}
                      className="bg-eco hover:bg-eco-dark"
                    >
                      {isLoading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Processando...
                        </>
                      ) : (
                        <>
                          <Check className="mr-2 h-4 w-4" />
                          Adicionar cartão
                        </>
                      )}
                    </Button>
                  </DialogFooter>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </RadioGroup>
      )}

      {/* PIX */}
      {selectedMethodType === "pix" && (
        <div className="border rounded-md p-4 space-y-4">
          <div className="flex items-center justify-center">
            <QrCode className="h-32 w-32 text-eco" />
          </div>
          <div className="text-center">
            <p className="font-medium">Pagamento via PIX</p>
            <p className="text-sm text-muted-foreground mt-1">
              Você receberá um QR Code para pagamento após finalizar o pedido.
            </p>
          </div>
        </div>
      )}

      {/* Dinheiro */}
      {selectedMethodType === "cash" && (
        <div className="border rounded-md p-4 space-y-4">
          <div className="flex items-center justify-center">
            <Banknote className="h-32 w-32 text-eco" />
          </div>
          <div className="text-center">
            <p className="font-medium">Pagamento em Dinheiro</p>
            <p className="text-sm text-muted-foreground mt-1">
              Você pagará em dinheiro diretamente ao entregador.
            </p>
          </div>
        </div>
      )}

      {/* Botão de continuar */}
      <div className="flex justify-end pt-4">
        <Button
          type="button"
          onClick={handleContinue}
          disabled={!selectedMethodId}
          className="bg-eco hover:bg-eco-dark"
        >
          Continuar
        </Button>
      </div>
    </div>
  );
}
