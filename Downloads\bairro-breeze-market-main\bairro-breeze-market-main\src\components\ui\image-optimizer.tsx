import React, { useState, useEffect } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

interface ImageOptimizerProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string;
  alt: string;
  fallbackSrc?: string;
  width?: number | string;
  height?: number | string;
  aspectRatio?: string;
  className?: string;
  containerClassName?: string;
  priority?: boolean;
  quality?: number;
  onLoad?: () => void;
  onError?: () => void;
}

/**
 * Componente de imagem otimizada com lazy loading, placeholders e fallbacks
 */
export function ImageOptimizer({
  src,
  alt,
  fallbackSrc,
  width,
  height,
  aspectRatio,
  className,
  containerClassName,
  priority = false,
  quality = 80,
  onLoad,
  onError,
  ...props
}: ImageOptimizerProps) {
  const [isLoading, setIsLoading] = useState(!priority);
  const [hasError, setHasError] = useState(false);
  const [imageSrc, setImageSrc] = useState(priority ? src : '');
  
  // Gerar URL otimizada (em uma implementação real, isso poderia usar um CDN de imagens)
  const getOptimizedSrc = (url: string) => {
    // Simulação de otimização - em produção, usaria um serviço como Cloudinary, Imgix, etc.
    if (url.startsWith('http')) {
      // Adicionar parâmetro de qualidade se for URL externa
      const separator = url.includes('?') ? '&' : '?';
      return `${url}${separator}q=${quality}`;
    }
    return url;
  };

  useEffect(() => {
    // Reset states when src changes
    if (src !== imageSrc && !priority) {
      setIsLoading(true);
      setHasError(false);
    }

    // Se priority for true, já definimos o src
    if (priority) return;

    // Usar IntersectionObserver para lazy loading
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          setImageSrc(getOptimizedSrc(src));
          observer.disconnect();
        }
      });
    }, {
      rootMargin: '200px', // Carregar imagens 200px antes de entrarem na viewport
    });

    const element = document.getElementById(`img-${props.id || src.replace(/[^a-zA-Z0-9]/g, '')}`);
    if (element) {
      observer.observe(element);
    }

    return () => {
      observer.disconnect();
    };
  }, [src, imageSrc, priority, props.id, quality]);

  const handleLoad = () => {
    setIsLoading(false);
    setHasError(false);
    onLoad?.();
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
    
    // Tentar fallback se disponível
    if (fallbackSrc && imageSrc !== fallbackSrc) {
      setImageSrc(fallbackSrc);
    } else {
      onError?.();
    }
  };

  const containerStyle: React.CSSProperties = {
    position: 'relative',
    width: width || '100%',
    height: height || 'auto',
    aspectRatio: aspectRatio || 'auto',
    overflow: 'hidden',
  };

  return (
    <div 
      id={`img-${props.id || src.replace(/[^a-zA-Z0-9]/g, '')}`}
      className={cn('relative overflow-hidden', containerClassName)}
      style={containerStyle}
    >
      {isLoading && (
        <Skeleton className="absolute inset-0 z-0" />
      )}
      
      {imageSrc && (
        <img
          src={imageSrc}
          alt={alt}
          className={cn(
            'w-full h-full object-cover transition-opacity duration-300',
            isLoading ? 'opacity-0' : 'opacity-100',
            className
          )}
          onLoad={handleLoad}
          onError={handleError}
          loading={priority ? 'eager' : 'lazy'}
          width={typeof width === 'number' ? width : undefined}
          height={typeof height === 'number' ? height : undefined}
          {...props}
        />
      )}
      
      {hasError && !fallbackSrc && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 text-gray-400">
          <span className="text-sm">Imagem indisponível</span>
        </div>
      )}
    </div>
  );
}

/**
 * Componente de imagem de produto otimizada
 */
export function ProductImage({
  src,
  alt,
  className,
  ...props
}: Omit<ImageOptimizerProps, 'aspectRatio'>) {
  return (
    <ImageOptimizer
      src={src}
      alt={alt}
      aspectRatio="1/1"
      className={cn('rounded-md', className)}
      fallbackSrc="/images/product-placeholder.jpg"
      {...props}
    />
  );
}

/**
 * Componente de imagem de avatar otimizada
 */
export function AvatarImage({
  src,
  alt,
  size = 'md',
  className,
  ...props
}: Omit<ImageOptimizerProps, 'aspectRatio'> & {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
}) {
  const sizeMap = {
    xs: { width: 24, height: 24 },
    sm: { width: 32, height: 32 },
    md: { width: 40, height: 40 },
    lg: { width: 48, height: 48 },
    xl: { width: 64, height: 64 },
  };
  
  const { width, height } = sizeMap[size];
  
  return (
    <ImageOptimizer
      src={src}
      alt={alt}
      width={width}
      height={height}
      aspectRatio="1/1"
      className={cn('rounded-full', className)}
      fallbackSrc="/images/avatar-placeholder.jpg"
      {...props}
    />
  );
}
