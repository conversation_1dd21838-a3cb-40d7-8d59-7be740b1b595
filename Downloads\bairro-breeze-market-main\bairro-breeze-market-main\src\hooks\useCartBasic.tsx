import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { CartItem, CartState, CartAction } from '@/types/cart';
import { formatCurrency } from '@/lib/utils';

// Contexto do carrinho
const CartContext = createContext<{
  items: CartItem[];
  subtotal: number;
  shopId: string | null;
  shopName: string | null;
  addItem: (item: CartItem) => void;
  removeItem: (id: string) => void;
  updateQuantity: (id: string, quantity: number) => void;
  clearCart: () => void;
} | undefined>(undefined);

// Estado inicial
const initialState: CartState = {
  items: [],
  subtotal: 0,
  shopId: null,
  shopName: null,
};

// Reducer para gerenciar o estado do carrinho
function cartReducer(state: CartState, action: CartAction): CartState {
  switch (action.type) {
    case 'ADD_ITEM': {
      const newItem = action.payload;
      
      // Verificar se o carrinho está vazio ou se o item é da mesma loja
      if (state.items.length === 0 || state.shopId === newItem.shopId) {
        // Verificar se o item já existe no carrinho
        const existingItemIndex = state.items.findIndex(item => item.id === newItem.id);
        
        if (existingItemIndex >= 0) {
          // Atualizar quantidade se o item já existe
          const updatedItems = [...state.items];
          updatedItems[existingItemIndex] = {
            ...updatedItems[existingItemIndex],
            quantity: updatedItems[existingItemIndex].quantity + newItem.quantity
          };
          
          // Calcular novo subtotal
          const subtotal = updatedItems.reduce((total, item) => total + (item.price * item.quantity), 0);
          
          return {
            ...state,
            items: updatedItems,
            subtotal,
            shopId: newItem.shopId,
            shopName: newItem.shopName
          };
        } else {
          // Adicionar novo item
          const updatedItems = [...state.items, newItem];
          
          // Calcular novo subtotal
          const subtotal = updatedItems.reduce((total, item) => total + (item.price * item.quantity), 0);
          
          return {
            ...state,
            items: updatedItems,
            subtotal,
            shopId: newItem.shopId,
            shopName: newItem.shopName
          };
        }
      } else {
        // Não permitir adicionar itens de lojas diferentes
        console.warn('Não é possível adicionar itens de lojas diferentes ao carrinho');
        return state;
      }
    }
    
    case 'REMOVE_ITEM': {
      const updatedItems = state.items.filter(item => item.id !== action.payload.id);
      
      // Calcular novo subtotal
      const subtotal = updatedItems.reduce((total, item) => total + (item.price * item.quantity), 0);
      
      // Resetar shopId e shopName se o carrinho ficar vazio
      const shopId = updatedItems.length > 0 ? state.shopId : null;
      const shopName = updatedItems.length > 0 ? state.shopName : null;
      
      return {
        ...state,
        items: updatedItems,
        subtotal,
        shopId,
        shopName
      };
    }
    
    case 'UPDATE_QUANTITY': {
      const { id, quantity } = action.payload;
      
      // Não permitir quantidade menor que 1
      if (quantity < 1) {
        return state;
      }
      
      const updatedItems = state.items.map(item => 
        item.id === id ? { ...item, quantity } : item
      );
      
      // Calcular novo subtotal
      const subtotal = updatedItems.reduce((total, item) => total + (item.price * item.quantity), 0);
      
      return {
        ...state,
        items: updatedItems,
        subtotal
      };
    }
    
    case 'CLEAR_CART':
      return initialState;
    
    default:
      return state;
  }
}

// Provider do carrinho
export function CartProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(cartReducer, initialState);
  
  // Carregar carrinho do localStorage ao iniciar
  useEffect(() => {
    try {
      const savedCart = localStorage.getItem('cart');
      if (savedCart) {
        const parsedCart = JSON.parse(savedCart);
        
        // Verificar se o formato do carrinho salvo é válido
        if (parsedCart && Array.isArray(parsedCart.items)) {
          // Recalcular o subtotal para garantir consistência
          const subtotal = parsedCart.items.reduce(
            (total: number, item: CartItem) => total + (item.price * item.quantity), 
            0
          );
          
          // Atualizar o estado com os dados carregados
          dispatch({
            type: 'ADD_ITEM',
            payload: {
              ...parsedCart.items[0],
              quantity: 0 // Quantidade zero para não duplicar
            }
          });
          
          // Adicionar cada item individualmente
          parsedCart.items.forEach((item: CartItem) => {
            dispatch({
              type: 'ADD_ITEM',
              payload: item
            });
          });
        }
      }
    } catch (error) {
      console.error('Erro ao carregar carrinho do localStorage:', error);
    }
  }, []);
  
  // Salvar carrinho no localStorage quando mudar
  useEffect(() => {
    try {
      localStorage.setItem('cart', JSON.stringify(state));
    } catch (error) {
      console.error('Erro ao salvar carrinho no localStorage:', error);
    }
  }, [state]);
  
  // Funções para manipular o carrinho
  const addItem = (item: CartItem) => {
    if (state.shopId && state.shopId !== item.shopId) {
      if (window.confirm(
        `Seu carrinho contém itens de ${state.shopName}. Deseja limpar o carrinho e adicionar este item de ${item.shopName}?`
      )) {
        dispatch({ type: 'CLEAR_CART' });
        setTimeout(() => {
          dispatch({ type: 'ADD_ITEM', payload: item });
        }, 0);
      }
    } else {
      dispatch({ type: 'ADD_ITEM', payload: item });
    }
  };
  
  const removeItem = (id: string) => {
    dispatch({ type: 'REMOVE_ITEM', payload: { id } });
  };
  
  const updateQuantity = (id: string, quantity: number) => {
    dispatch({ type: 'UPDATE_QUANTITY', payload: { id, quantity } });
  };
  
  const clearCart = () => {
    dispatch({ type: 'CLEAR_CART' });
  };
  
  return (
    <CartContext.Provider
      value={{
        items: state.items,
        subtotal: state.subtotal,
        shopId: state.shopId,
        shopName: state.shopName,
        addItem,
        removeItem,
        updateQuantity,
        clearCart
      }}
    >
      {children}
    </CartContext.Provider>
  );
}

// Hook para usar o carrinho
export function useCart() {
  const context = useContext(CartContext);
  
  if (context === undefined) {
    throw new Error('useCart deve ser usado dentro de um CartProvider');
  }
  
  return context;
}
