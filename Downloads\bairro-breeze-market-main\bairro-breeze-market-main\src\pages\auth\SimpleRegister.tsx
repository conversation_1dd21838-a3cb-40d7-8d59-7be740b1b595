import React, { useState, useEffect } from 'react';
import { useNavigate, Link, useSearchParams } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, Loader2 } from 'lucide-react';

export default function SimpleRegister() {
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [role, setRole] = useState('customer');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  
  // Get role from URL if provided
  useEffect(() => {
    const roleParam = searchParams.get('role');
    if (roleParam && ['customer', 'merchant', 'deliverer'].includes(roleParam)) {
      setRole(roleParam);
    }
  }, [searchParams]);

  const handleRegister = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!name || !email || !password || !confirmPassword) {
      setError('Por favor, preencha todos os campos.');
      return;
    }
    
    if (password !== confirmPassword) {
      setError('As senhas não coincidem.');
      return;
    }
    
    try {
      setIsLoading(true);
      setError(null);
      
      // Simular registro bem-sucedido
      const mockUser = {
        id: 'user' + Date.now(),
        email,
        profile: {
          name,
          role,
          address: '',
          phone: ''
        }
      };
      
      // Simular atraso de rede
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Salvar usuário no localStorage
      localStorage.setItem('mockUser', JSON.stringify(mockUser));
      
      // Redirecionar com base no perfil
      if (role === 'merchant') {
        navigate('/merchant');
      } else if (role === 'deliverer') {
        navigate('/deliverer');
      } else {
        navigate('/');
      }
    } catch (err: any) {
      setError(err.message || 'Ocorreu um erro ao criar sua conta. Tente novamente.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container max-w-md mx-auto px-4 py-8">
      <div className="text-center mb-6">
        <Link to="/" className="inline-block">
          <h1 className="text-2xl font-bold text-primary">Já Comprei</h1>
        </Link>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Criar Conta</CardTitle>
          <CardDescription>
            Cadastre-se para começar a usar o Já Comprei
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={role} onValueChange={setRole}>
            <TabsList className="grid grid-cols-3 mb-6">
              <TabsTrigger value="customer">Cliente</TabsTrigger>
              <TabsTrigger value="merchant">Comerciante</TabsTrigger>
              <TabsTrigger value="deliverer">Entregador</TabsTrigger>
            </TabsList>
            
            <TabsContent value="customer">
              <p className="text-sm text-muted-foreground mb-6">
                Cadastre-se como cliente para comprar produtos de lojas locais.
              </p>
            </TabsContent>
            
            <TabsContent value="merchant">
              <p className="text-sm text-muted-foreground mb-6">
                Cadastre-se como comerciante para vender seus produtos.
              </p>
            </TabsContent>
            
            <TabsContent value="deliverer">
              <p className="text-sm text-muted-foreground mb-6">
                Cadastre-se como entregador para realizar entregas e ganhar dinheiro.
              </p>
            </TabsContent>
          </Tabs>
          
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          <form onSubmit={handleRegister} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nome completo</Label>
              <Input
                id="name"
                placeholder="Seu nome completo"
                value={name}
                onChange={(e) => setName(e.target.value)}
                disabled={isLoading}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={isLoading}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="password">Senha</Label>
              <Input
                id="password"
                type="password"
                placeholder="••••••••"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                disabled={isLoading}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirmar senha</Label>
              <Input
                id="confirmPassword"
                type="password"
                placeholder="••••••••"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                disabled={isLoading}
              />
            </div>
            
            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Criando conta...
                </>
              ) : (
                'Criar conta'
              )}
            </Button>
          </form>
        </CardContent>
        <CardFooter className="flex flex-col space-y-4">
          <div className="text-sm text-center">
            Já tem uma conta?{' '}
            <Link to="/login" className="text-primary hover:underline">
              Faça login
            </Link>
          </div>
          
          <div className="text-xs text-center text-muted-foreground">
            Ao criar uma conta, você concorda com nossos{' '}
            <Link to="/terms" className="hover:underline">
              Termos de Serviço
            </Link>{' '}
            e{' '}
            <Link to="/privacy" className="hover:underline">
              Política de Privacidade
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
