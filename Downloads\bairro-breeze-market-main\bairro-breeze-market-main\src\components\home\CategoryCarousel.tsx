import { useRef } from "react";
import { motion } from "framer-motion";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface Category {
  id: string;
  name: string;
  icon: string;
  color: string;
}

interface CategoryCarouselProps {
  categories: Category[];
  className?: string;
}

export function CategoryCarousel({ categories, className }: CategoryCarouselProps) {
  const carouselRef = useRef<HTMLDivElement>(null);

  const scroll = (direction: "left" | "right") => {
    if (carouselRef.current) {
      const { current } = carouselRef;
      const scrollAmount = direction === "left" ? -200 : 200;
      
      current.scrollBy({
        left: scrollAmount,
        behavior: "smooth",
      });
    }
  };

  return (
    <div className={cn("relative", className)}>
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-bold">Categorias</h2>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8 rounded-full"
            onClick={() => scroll("left")}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8 rounded-full"
            onClick={() => scroll("right")}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div
        ref={carouselRef}
        className="flex overflow-x-auto scrollbar-hide gap-4 pb-4 -mx-4 px-4"
        style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
      >
        {categories.map((category) => (
          <motion.div
            key={category.id}
            whileHover={{ y: -5 }}
            whileTap={{ scale: 0.95 }}
            className="flex-shrink-0"
          >
            <div
              className={cn(
                "flex flex-col items-center justify-center w-20 h-20 rounded-full mb-2",
                `bg-${category.color}-light`
              )}
            >
              <img
                src={category.icon}
                alt={category.name}
                className="w-10 h-10 object-contain"
              />
            </div>
            <p className="text-center text-sm font-medium">{category.name}</p>
          </motion.div>
        ))}
      </div>
    </div>
  );
}
