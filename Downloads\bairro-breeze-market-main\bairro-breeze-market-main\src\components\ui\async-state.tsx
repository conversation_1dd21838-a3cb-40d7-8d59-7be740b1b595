import React, { ReactNode } from 'react';
import { Loader2, Alert<PERSON>ircle, CheckCircle } from 'lucide-react';
import { <PERSON>ert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';

interface AsyncStateProps<T> {
  loading: boolean;
  error: Error | null;
  data: T | null;
  children: (data: T) => ReactNode;
  loadingComponent?: ReactNode;
  errorComponent?: ReactNode;
  emptyComponent?: ReactNode;
  onRetry?: () => void;
  isEmpty?: (data: T | null) => boolean;
}

/**
 * Componente para renderização condicional baseada no estado de uma operação assíncrona
 */
export function AsyncState<T>({
  loading,
  error,
  data,
  children,
  loadingComponent,
  errorComponent,
  emptyComponent,
  onRetry,
  isEmpty = (data) => data === null || (Array.isArray(data) && data.length === 0),
}: AsyncStateProps<T>) {
  // Estado de carregamento
  if (loading) {
    if (loadingComponent) {
      return <>{loadingComponent}</>;
    }
    
    return (
      <div className="flex flex-col items-center justify-center p-6 space-y-4">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        <p className="text-sm text-muted-foreground">Carregando...</p>
      </div>
    );
  }
  
  // Estado de erro
  if (error) {
    if (errorComponent) {
      return <>{errorComponent}</>;
    }
    
    return (
      <div className="flex flex-col items-center justify-center p-6 space-y-4">
        <Alert variant="destructive" className="max-w-md">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Erro</AlertTitle>
          <AlertDescription>
            {error.message || 'Ocorreu um erro inesperado.'}
          </AlertDescription>
        </Alert>
        
        {onRetry && (
          <Button 
            variant="outline" 
            onClick={onRetry}
            className="mt-2"
          >
            Tentar novamente
          </Button>
        )}
      </div>
    );
  }
  
  // Estado vazio
  if (isEmpty(data)) {
    if (emptyComponent) {
      return <>{emptyComponent}</>;
    }
    
    return (
      <div className="flex flex-col items-center justify-center p-6 space-y-2">
        <p className="text-muted-foreground">Nenhum dado encontrado.</p>
      </div>
    );
  }
  
  // Estado com dados
  return <>{children(data as T)}</>;
}

/**
 * Componente para exibir um estado de sucesso
 */
export function SuccessState({
  title,
  description,
  children,
}: {
  title: string;
  description?: string;
  children?: ReactNode;
}) {
  return (
    <div className="flex flex-col items-center justify-center p-6 space-y-4">
      <div className="bg-green-100 p-3 rounded-full">
        <CheckCircle className="h-8 w-8 text-green-600" />
      </div>
      <div className="text-center">
        <h3 className="text-lg font-medium">{title}</h3>
        {description && <p className="text-sm text-muted-foreground mt-1">{description}</p>}
      </div>
      {children}
    </div>
  );
}

/**
 * Componente para exibir um estado de carregamento
 */
export function LoadingState({
  message = 'Carregando...',
  size = 'default',
}: {
  message?: string;
  size?: 'small' | 'default' | 'large';
}) {
  const sizeClasses = {
    small: 'h-4 w-4',
    default: 'h-8 w-8',
    large: 'h-12 w-12',
  };
  
  return (
    <div className="flex flex-col items-center justify-center p-6 space-y-4">
      <Loader2 className={`${sizeClasses[size]} animate-spin text-muted-foreground`} />
      <p className="text-sm text-muted-foreground">{message}</p>
    </div>
  );
}

/**
 * Componente para exibir um estado de erro
 */
export function ErrorState({
  error,
  onRetry,
  title = 'Erro',
}: {
  error: Error | string;
  onRetry?: () => void;
  title?: string;
}) {
  const errorMessage = typeof error === 'string' ? error : error.message || 'Ocorreu um erro inesperado.';
  
  return (
    <div className="flex flex-col items-center justify-center p-6 space-y-4">
      <Alert variant="destructive" className="max-w-md">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>{title}</AlertTitle>
        <AlertDescription>{errorMessage}</AlertDescription>
      </Alert>
      
      {onRetry && (
        <Button 
          variant="outline" 
          onClick={onRetry}
          className="mt-2"
        >
          Tentar novamente
        </Button>
      )}
    </div>
  );
}

/**
 * Componente para exibir um estado vazio
 */
export function EmptyState({
  title,
  description,
  action,
  icon,
}: {
  title: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  icon?: ReactNode;
}) {
  return (
    <div className="flex flex-col items-center justify-center p-6 space-y-4">
      {icon && <div className="text-muted-foreground">{icon}</div>}
      <div className="text-center">
        <h3 className="text-lg font-medium">{title}</h3>
        {description && <p className="text-sm text-muted-foreground mt-1">{description}</p>}
      </div>
      {action && (
        <Button 
          variant="outline" 
          onClick={action.onClick}
          className="mt-2"
        >
          {action.label}
        </Button>
      )}
    </div>
  );
}
