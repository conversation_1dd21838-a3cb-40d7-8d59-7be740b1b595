import React, { createContext, useContext, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

// Tipo para o usuário autenticado
export interface AuthUser {
  id: string;
  email: string;
  profile?: {
    name?: string;
    role?: 'customer' | 'merchant' | 'deliverer';
    phone?: string;
    address?: string;
    avatar_url?: string;
  };
}

// Tipo para o contexto de autenticação
interface AuthContextType {
  user: AuthUser | null;
  isLoading: boolean;
  signIn: (email: string, password: string, role?: string) => Promise<{ success: boolean; error?: string }>;
  signUp: (email: string, password: string, userData: any) => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<void>;
  updateProfile: (data: Partial<AuthUser['profile']>) => Promise<{ success: boolean; error?: string }>;
}

// Criar o contexto
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider do contexto
export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  // Verificar se o usuário está autenticado ao carregar
  useEffect(() => {
    const checkUser = async () => {
      try {
        setIsLoading(true);
        
        // Verificar se há um usuário no localStorage
        const storedUser = localStorage.getItem('mockUser');
        
        if (storedUser) {
          setUser(JSON.parse(storedUser));
        }
      } catch (error) {
        console.error('Erro ao verificar autenticação:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    checkUser();
  }, []);

  // Função para fazer login
  const signIn = async (email: string, password: string, role?: string): Promise<{ success: boolean; error?: string }> => {
    try {
      setIsLoading(true);
      
      // Simular login bem-sucedido
      const mockUser: AuthUser = {
        id: 'user1',
        email,
        profile: {
          name: 'Usuário Teste',
          role: (role as 'customer' | 'merchant' | 'deliverer') || 'customer',
          address: 'Rua Teste, 123',
          phone: '(11) 99999-9999'
        }
      };
      
      // Salvar usuário no localStorage
      localStorage.setItem('mockUser', JSON.stringify(mockUser));
      
      // Atualizar estado
      setUser(mockUser);
      
      return { success: true };
    } catch (error: any) {
      console.error('Erro ao fazer login:', error);
      return { 
        success: false, 
        error: error.message || 'Ocorreu um erro ao fazer login. Tente novamente.' 
      };
    } finally {
      setIsLoading(false);
    }
  };

  // Função para criar conta
  const signUp = async (email: string, password: string, userData: any): Promise<{ success: boolean; error?: string }> => {
    try {
      setIsLoading(true);
      
      // Simular criação de conta bem-sucedida
      const mockUser: AuthUser = {
        id: 'user' + Date.now(),
        email,
        profile: {
          name: userData.name || 'Novo Usuário',
          role: (userData.role as 'customer' | 'merchant' | 'deliverer') || 'customer',
          address: userData.address,
          phone: userData.phone
        }
      };
      
      // Salvar usuário no localStorage
      localStorage.setItem('mockUser', JSON.stringify(mockUser));
      
      // Atualizar estado
      setUser(mockUser);
      
      return { success: true };
    } catch (error: any) {
      console.error('Erro ao criar conta:', error);
      return { 
        success: false, 
        error: error.message || 'Ocorreu um erro ao criar sua conta. Tente novamente.' 
      };
    } finally {
      setIsLoading(false);
    }
  };

  // Função para fazer logout
  const signOut = async (): Promise<void> => {
    try {
      setIsLoading(true);
      
      // Remover usuário do localStorage
      localStorage.removeItem('mockUser');
      
      // Limpar estado
      setUser(null);
      
      // Redirecionar para a página inicial
      navigate('/');
    } catch (error) {
      console.error('Erro ao fazer logout:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Função para atualizar perfil
  const updateProfile = async (data: Partial<AuthUser['profile']>): Promise<{ success: boolean; error?: string }> => {
    try {
      setIsLoading(true);
      
      if (!user) {
        throw new Error('Usuário não autenticado');
      }
      
      // Atualizar perfil do usuário
      const updatedUser: AuthUser = {
        ...user,
        profile: {
          ...user.profile,
          ...data
        }
      };
      
      // Salvar usuário atualizado no localStorage
      localStorage.setItem('mockUser', JSON.stringify(updatedUser));
      
      // Atualizar estado
      setUser(updatedUser);
      
      return { success: true };
    } catch (error: any) {
      console.error('Erro ao atualizar perfil:', error);
      return { 
        success: false, 
        error: error.message || 'Ocorreu um erro ao atualizar seu perfil. Tente novamente.' 
      };
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        signIn,
        signUp,
        signOut,
        updateProfile
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

// Hook para usar a autenticação
export function useAuth() {
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    throw new Error('useAuth deve ser usado dentro de um AuthProvider');
  }
  
  return context;
}
