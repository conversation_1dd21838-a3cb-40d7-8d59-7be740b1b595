import { ApiService } from './api';
import { Tables, Insertable, Updatable } from '@/types/database';
import { supabaseClient } from '@/lib/supabase';

// Create a typed service for categories
class CategoryService extends ApiService<'categories'> {
  constructor() {
    super('categories');
  }

  /**
   * Get all categories
   * @returns Promise with categories
   */
  async getAllCategories(): Promise<Tables<'categories'>[]> {
    try {
      const { data, error } = await supabaseClient
        .from('categories')
        .select('*')
        .order('name', { ascending: true });

      if (error) {
        console.error('Error fetching categories:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in getAllCategories:', error);
      
      // Return mock data in development mode
      return [
        { id: '1', name: '<PERSON>rca<PERSON>', icon: 'ShoppingBag', created_at: new Date().toISOString() },
        { id: '2', name: '<PERSON><PERSON>', icon: 'Utensils', created_at: new Date().toISOString() },
        { id: '3', name: '<PERSON><PERSON><PERSON>', icon: 'Pill', created_at: new Date().toISOString() },
        { id: '4', name: '<PERSON><PERSON>ia', icon: 'Croissant', created_at: new Date().toISOString() },
        { id: '5', name: 'Açougue', icon: 'Beef', created_at: new Date().toISOString() },
        { id: '6', name: 'Hortifruti', icon: 'Apple', created_at: new Date().toISOString() },
        { id: '7', name: 'Bebidas', icon: 'Wine', created_at: new Date().toISOString() },
        { id: '8', name: 'Pet Shop', icon: 'Paw', created_at: new Date().toISOString() },
      ];
    }
  }
}

export const categoryService = new CategoryService();
