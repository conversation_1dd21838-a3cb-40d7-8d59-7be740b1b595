import { supabaseClient } from '@/lib/supabase';
import { fetchProducts, searchProducts } from './products';
import { searchShops } from './shops';
import { Product } from '@/types/product';
import { Shop } from './shops';

// Search result interface
export interface SearchResult {
  products: Product[];
  shops: Shop[];
}

/**
 * Search for products and shops
 * @param query Search query
 * @returns Promise with search results
 */
export const search = async (query: string): Promise<SearchResult> => {
  try {
    // Search for products and shops in parallel
    const [products, shops] = await Promise.all([
      searchProducts(query),
      searchShops(query)
    ]);
    
    return { products, shops };
  } catch (error) {
    console.error(`Error searching for "${query}":`, error);
    return { products: [], shops: [] };
  }
};

/**
 * Get popular search terms
 * @param limit Number of terms to return
 * @returns Promise with popular search terms
 */
export const getPopularSearchTerms = async (limit = 10): Promise<string[]> => {
  try {
    // In a real app, this would fetch from a database
    
    // For now, return mock data
    return [
      'pizza',
      'mercado',
      'farmácia',
      'frutas',
      'pão',
      'café',
      'carne',
      'vegetais',
      'bebidas',
      'doces'
    ].slice(0, limit);
  } catch (error) {
    console.error('Error fetching popular search terms:', error);
    return [];
  }
};

/**
 * Get recent search terms for a user
 * @param userId User ID
 * @param limit Number of terms to return
 * @returns Promise with recent search terms
 */
export const getRecentSearchTerms = async (
  userId: string,
  limit = 5
): Promise<string[]> => {
  try {
    // In a real app, this would fetch from a database
    
    // For now, return mock data
    return [
      'pizza',
      'café',
      'pão',
      'frutas',
      'mercado'
    ].slice(0, limit);
  } catch (error) {
    console.error(`Error fetching recent search terms for user ${userId}:`, error);
    return [];
  }
};

/**
 * Save search term for a user
 * @param userId User ID
 * @param term Search term
 * @returns Promise with success status
 */
export const saveSearchTerm = async (
  userId: string,
  term: string
): Promise<boolean> => {
  try {
    // In a real app, this would insert into the database
    
    // For now, just log it
    console.info(`[DEV] Saved search term "${term}" for user ${userId}`);
    return true;
  } catch (error) {
    console.error(`Error saving search term "${term}" for user ${userId}:`, error);
    return false;
  }
};

/**
 * Clear recent search terms for a user
 * @param userId User ID
 * @returns Promise with success status
 */
export const clearRecentSearchTerms = async (userId: string): Promise<boolean> => {
  try {
    // In a real app, this would delete from the database
    
    // For now, just log it
    console.info(`[DEV] Cleared recent search terms for user ${userId}`);
    return true;
  } catch (error) {
    console.error(`Error clearing recent search terms for user ${userId}:`, error);
    return false;
  }
};
