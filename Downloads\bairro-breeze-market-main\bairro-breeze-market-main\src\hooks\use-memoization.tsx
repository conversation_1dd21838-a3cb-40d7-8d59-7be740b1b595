import { useRef, useCallback, useMemo, useEffect, useState, DependencyList } from 'react';

/**
 * Hook para memoizar um valor com verificação profunda de igualdade
 * @param value Valor a ser memoizado
 * @param deps Lista de dependências
 * @returns Valor memoizado
 */
export function useDeepMemo<T>(value: T, deps: DependencyList): T {
  const ref = useRef<T>(value);
  
  // Verificar se as dependências mudaram
  const depsChanged = useMemo(() => {
    if (!deps.length) return true;
    
    return deps.some((dep, i) => {
      if (typeof dep === 'object' && dep !== null) {
        return JSON.stringify(dep) !== JSON.stringify(ref.current);
      }
      return dep !== (ref.current as any)[i];
    });
  }, deps);
  
  // Atualizar o valor memoizado se as dependências mudaram
  if (depsChanged) {
    ref.current = value;
  }
  
  return ref.current;
}

/**
 * Hook para memoizar uma função com verificação profunda de igualdade
 * @param callback Função a ser memoizada
 * @param deps Lista de dependências
 * @returns Função memoizada
 */
export function useDeepCallback<T extends (...args: any[]) => any>(
  callback: T,
  deps: DependencyList
): T {
  const callbackRef = useRef<T>(callback);
  
  // Verificar se as dependências mudaram
  const depsChanged = useMemo(() => {
    if (!deps.length) return true;
    
    return deps.some((dep, i) => {
      if (typeof dep === 'object' && dep !== null) {
        return JSON.stringify(dep) !== JSON.stringify(callbackRef.current);
      }
      return dep !== (callbackRef.current as any)[i];
    });
  }, deps);
  
  // Atualizar a função memoizada se as dependências mudaram
  if (depsChanged) {
    callbackRef.current = callback;
  }
  
  return useCallback((...args: Parameters<T>) => {
    return callbackRef.current(...args);
  }, []) as T;
}

/**
 * Hook para debounce de funções
 * @param fn Função a ser debounced
 * @param delay Tempo de delay em ms
 * @param deps Lista de dependências
 * @returns Função com debounce
 */
export function useDebounce<T extends (...args: any[]) => any>(
  fn: T,
  delay: number,
  deps: DependencyList = []
): [T, boolean] {
  const [isDebouncing, setIsDebouncing] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  
  // Limpar timer quando o componente for desmontado
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);
  
  // Criar função com debounce
  const debouncedFn = useCallback((...args: Parameters<T>) => {
    setIsDebouncing(true);
    
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }
    
    timerRef.current = setTimeout(() => {
      fn(...args);
      setIsDebouncing(false);
      timerRef.current = null;
    }, delay);
  }, [fn, delay, ...deps]) as T;
  
  return [debouncedFn, isDebouncing];
}

/**
 * Hook para throttle de funções
 * @param fn Função a ser throttled
 * @param limit Tempo limite em ms
 * @param deps Lista de dependências
 * @returns Função com throttle
 */
export function useThrottle<T extends (...args: any[]) => any>(
  fn: T,
  limit: number,
  deps: DependencyList = []
): [T, boolean] {
  const [isThrottling, setIsThrottling] = useState(false);
  const lastRunRef = useRef<number>(0);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  
  // Limpar timer quando o componente for desmontado
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);
  
  // Criar função com throttle
  const throttledFn = useCallback((...args: Parameters<T>) => {
    const now = Date.now();
    const timeSinceLastRun = now - lastRunRef.current;
    
    if (timeSinceLastRun >= limit) {
      // Executar imediatamente se o tempo limite foi atingido
      lastRunRef.current = now;
      fn(...args);
      setIsThrottling(false);
    } else {
      // Agendar para executar após o tempo limite
      setIsThrottling(true);
      
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
      
      timerRef.current = setTimeout(() => {
        lastRunRef.current = Date.now();
        fn(...args);
        setIsThrottling(false);
        timerRef.current = null;
      }, limit - timeSinceLastRun);
    }
  }, [fn, limit, ...deps]) as T;
  
  return [throttledFn, isThrottling];
}
