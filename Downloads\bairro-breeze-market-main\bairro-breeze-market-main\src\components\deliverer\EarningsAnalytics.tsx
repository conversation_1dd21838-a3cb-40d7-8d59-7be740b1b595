import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  ArrowDown, 
  ArrowUp, 
  Calendar, 
  DollarSign, 
  TrendingUp, 
  Truck, 
  Clock,
  CheckCircle
} from "lucide-react";
import { Order } from "@/types/order";
import { useQuery } from "@tanstack/react-query";
import { fetchOrders } from "@/services/orders";
import { format, subDays, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from "date-fns";
import { ptBR } from "date-fns/locale";

interface EarningsSummary {
  totalEarnings: number;
  totalDeliveries: number;
  completedDeliveries: number;
  averageDeliveryTime: number;
  percentChange: number;
}

export function EarningsAnalytics() {
  const [timeRange, setTimeRange] = useState<"today" | "week" | "month" | "all">("week");
  
  // Fetch orders
  const { data: orders = [], isLoading } = useQuery({
    queryKey: ["orders"],
    queryFn: fetchOrders,
  });

  // Filter orders by time range and only include delivered orders
  const getFilteredOrders = () => {
    const now = new Date();
    const deliveredOrders = orders.filter(order => order.status === "delivered");
    
    switch (timeRange) {
      case "today":
        return deliveredOrders.filter(order => {
          const orderDate = new Date(order.createdAt);
          return orderDate.toDateString() === now.toDateString();
        });
      case "week":
        const weekStart = startOfWeek(now, { weekStartsOn: 1 });
        const weekEnd = endOfWeek(now, { weekStartsOn: 1 });
        return deliveredOrders.filter(order => {
          const orderDate = new Date(order.createdAt);
          return orderDate >= weekStart && orderDate <= weekEnd;
        });
      case "month":
        const monthStart = startOfMonth(now);
        const monthEnd = endOfMonth(now);
        return deliveredOrders.filter(order => {
          const orderDate = new Date(order.createdAt);
          return orderDate >= monthStart && orderDate <= monthEnd;
        });
      case "all":
      default:
        return deliveredOrders;
    }
  };

  // Calculate earnings summary
  const calculateEarningsSummary = (): EarningsSummary => {
    const filteredOrders = getFilteredOrders();
    const totalEarnings = filteredOrders.reduce((sum, order) => sum + (order.total * 0.1), 0); // Assuming 10% commission
    
    // Calculate previous period for comparison
    const now = new Date();
    let previousPeriodStart, previousPeriodEnd, currentPeriodStart;
    
    switch (timeRange) {
      case "today":
        previousPeriodStart = subDays(now, 1);
        previousPeriodEnd = previousPeriodStart;
        currentPeriodStart = now;
        break;
      case "week":
        currentPeriodStart = startOfWeek(now, { weekStartsOn: 1 });
        previousPeriodStart = subDays(currentPeriodStart, 7);
        previousPeriodEnd = subDays(currentPeriodStart, 1);
        break;
      case "month":
        currentPeriodStart = startOfMonth(now);
        previousPeriodStart = startOfMonth(subDays(currentPeriodStart, 1));
        previousPeriodEnd = subDays(currentPeriodStart, 1);
        break;
      case "all":
      default:
        // For "all", we'll just use a fixed percentage change
        return {
          totalEarnings,
          totalDeliveries: filteredOrders.length,
          completedDeliveries: filteredOrders.length,
          averageDeliveryTime: 25, // Mock average delivery time in minutes
          percentChange: 0
        };
    }
    
    const previousPeriodOrders = orders.filter(order => {
      const orderDate = new Date(order.createdAt);
      return order.status === "delivered" && 
             orderDate >= previousPeriodStart && 
             orderDate <= previousPeriodEnd;
    });
    
    const previousEarnings = previousPeriodOrders.reduce((sum, order) => sum + (order.total * 0.1), 0);
    
    // Calculate percent change
    let percentChange = 0;
    if (previousEarnings > 0) {
      percentChange = ((totalEarnings - previousEarnings) / previousEarnings) * 100;
    } else if (totalEarnings > 0) {
      percentChange = 100; // If previous was 0 and current is positive, that's a 100% increase
    }
    
    return {
      totalEarnings,
      totalDeliveries: filteredOrders.length,
      completedDeliveries: filteredOrders.length,
      averageDeliveryTime: 25, // Mock average delivery time in minutes
      percentChange
    };
  };

  const summary = calculateEarningsSummary();

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const getTimeRangeLabel = () => {
    const now = new Date();
    
    switch (timeRange) {
      case "today":
        return format(now, "dd 'de' MMMM", { locale: ptBR });
      case "week":
        const weekStart = startOfWeek(now, { weekStartsOn: 1 });
        const weekEnd = endOfWeek(now, { weekStartsOn: 1 });
        return `${format(weekStart, "dd/MM", { locale: ptBR })} - ${format(weekEnd, "dd/MM", { locale: ptBR })}`;
      case "month":
        return format(now, "MMMM 'de' yyyy", { locale: ptBR });
      case "all":
      default:
        return "Todo o período";
    }
  };

  // Mock data for the chart
  const dailyEarnings = [
    { day: "Seg", amount: 45 },
    { day: "Ter", amount: 38 },
    { day: "Qua", amount: 52 },
    { day: "Qui", amount: 65 },
    { day: "Sex", amount: 78 },
    { day: "Sáb", amount: 85 },
    { day: "Dom", amount: 40 },
  ];

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h2 className="text-xl font-semibold">Análise de Ganhos</h2>
        <div className="flex gap-2 w-full sm:w-auto">
          <Select
            value={timeRange}
            onValueChange={(value: "today" | "week" | "month" | "all") => setTimeRange(value)}
          >
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="Período" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="today">Hoje</SelectItem>
              <SelectItem value="week">Esta semana</SelectItem>
              <SelectItem value="month">Este mês</SelectItem>
              <SelectItem value="all">Todo o período</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Ganhos Totais</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(summary.totalEarnings)}</div>
            <div className="flex items-center pt-1 text-xs text-muted-foreground">
              {summary.percentChange > 0 ? (
                <>
                  <ArrowUp className="h-3 w-3 text-green-500 mr-1" />
                  <span className="text-green-500">{summary.percentChange.toFixed(1)}%</span>
                </>
              ) : summary.percentChange < 0 ? (
                <>
                  <ArrowDown className="h-3 w-3 text-red-500 mr-1" />
                  <span className="text-red-500">{Math.abs(summary.percentChange).toFixed(1)}%</span>
                </>
              ) : (
                <span>Sem alteração</span>
              )}
              <span className="ml-1">vs. período anterior</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Total de Entregas</CardTitle>
            <Truck className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary.totalDeliveries}</div>
            <div className="pt-1 text-xs text-muted-foreground">
              {getTimeRangeLabel()}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Taxa de Conclusão</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {summary.totalDeliveries > 0 
                ? `${((summary.completedDeliveries / summary.totalDeliveries) * 100).toFixed(0)}%` 
                : "0%"}
            </div>
            <div className="pt-1 text-xs text-muted-foreground">
              {summary.completedDeliveries} de {summary.totalDeliveries} entregas
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between pb-2">
            <CardTitle className="text-sm font-medium">Tempo Médio</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary.averageDeliveryTime} min</div>
            <div className="pt-1 text-xs text-muted-foreground">
              Tempo médio por entrega
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Ganhos Diários</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[200px]">
            <div className="flex h-full items-end gap-2">
              {dailyEarnings.map((day, i) => (
                <div key={i} className="relative flex h-full w-full flex-col items-center">
                  <div 
                    className="absolute bottom-0 w-full rounded-md bg-trust" 
                    style={{ height: `${(day.amount / 85) * 100}%` }}
                  />
                  <div className="mt-auto pt-3 text-xs">{day.day}</div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Histórico de Entregas</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {getFilteredOrders().slice(0, 5).map((order) => (
              <div key={order.id} className="flex items-center justify-between border-b pb-4">
                <div>
                  <div className="font-medium">Pedido #{order.id}</div>
                  <div className="text-sm text-muted-foreground">{format(new Date(order.createdAt), "dd/MM/yyyy 'às' HH:mm", { locale: ptBR })}</div>
                </div>
                <div className="text-right">
                  <div className="font-medium">{formatCurrency(order.total * 0.1)}</div>
                  <div className="text-xs text-muted-foreground">{order.shopName}</div>
                </div>
              </div>
            ))}
            {getFilteredOrders().length === 0 && (
              <div className="text-center py-4 text-muted-foreground">
                Nenhuma entrega encontrada neste período
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
