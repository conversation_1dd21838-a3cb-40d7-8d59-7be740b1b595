import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useMediaQuery } from '@/hooks/use-breakpoint';
import { VirtualGrid } from '@/components/ui/virtual-list';
import { ProductCard } from '@/components/ui/product-card';
import { SkeletonGrid } from '@/components/ui/skeleton-loaders';
import { Product } from '@/types/product';
import { useInView } from 'framer-motion';
import { Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface OptimizedProductGridProps {
  products: Product[];
  isLoading?: boolean;
  isError?: boolean;
  onLoadMore?: () => void;
  hasMore?: boolean;
  className?: string;
  itemClassName?: string;
  emptyMessage?: string;
  errorMessage?: string;
  animateEntrance?: boolean;
  virtualized?: boolean;
  gridLayout?: 'fixed' | 'responsive';
  minItemWidth?: number;
}

/**
 * Componente otimizado para exibição de grade de produtos
 * Suporta virtualização, carregamento infinito e animações fluidas
 */
export function OptimizedProductGrid({
  products,
  isLoading = false,
  isError = false,
  onLoadMore,
  hasMore = false,
  className,
  itemClassName,
  emptyMessage = 'Nenhum produto encontrado',
  errorMessage = 'Erro ao carregar produtos',
  animateEntrance = true,
  virtualized = false,
  gridLayout = 'responsive',
  minItemWidth = 200,
}: OptimizedProductGridProps) {
  const loadMoreRef = useRef<HTMLDivElement>(null);
  const isLoadMoreVisible = useInView(loadMoreRef, { once: false, amount: 0.5 });
  const isMobile = !useMediaQuery('sm');
  const isTablet = useMediaQuery('sm') && !useMediaQuery('lg');
  const isDesktop = useMediaQuery('lg');
  
  // Determinar número de colunas com base no tamanho da tela
  const getColumnCount = () => {
    if (isMobile) return 2;
    if (isTablet) return 3;
    if (isDesktop) return 4;
    return 4;
  };
  
  const columnCount = getColumnCount();
  
  // Carregar mais produtos quando o elemento de carregamento estiver visível
  useEffect(() => {
    if (isLoadMoreVisible && hasMore && !isLoading && onLoadMore) {
      onLoadMore();
    }
  }, [isLoadMoreVisible, hasMore, isLoading, onLoadMore]);
  
  // Renderizar item de produto com animações
  const renderProductItem = (product: Product, index: number) => {
    const delay = Math.min(index * 0.05, 0.5); // Limitar delay máximo a 0.5s
    
    return (
      <motion.div
        initial={animateEntrance ? { opacity: 0, y: 20 } : false}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.95 }}
        transition={{ duration: 0.3, delay }}
        className={cn('h-full', itemClassName)}
        layout
      >
        <ProductCard
          id={product.id}
          name={product.name}
          image={product.image}
          price={product.price}
          originalPrice={product.originalPrice}
          shopName={product.shopName}
          isPromo={product.isPromo}
        />
      </motion.div>
    );
  };
  
  // Renderizar grade de produtos virtualizada
  if (virtualized && products.length > 0) {
    return (
      <div className={cn('relative min-h-[200px]', className)}>
        <VirtualGrid
          items={products}
          renderItem={renderProductItem}
          itemHeight={300} // Altura aproximada do item
          columns={columnCount}
          gap={16}
          className="w-full"
          onEndReached={hasMore ? onLoadMore : undefined}
          endReachedThreshold={300}
          loadingComponent={
            <div className="flex justify-center py-4">
              <Loader2 className="h-6 w-6 animate-spin text-primary" />
            </div>
          }
          emptyComponent={
            <div className="flex flex-col items-center justify-center py-12">
              <p className="text-muted-foreground">{emptyMessage}</p>
            </div>
          }
          isLoading={isLoading}
        />
      </div>
    );
  }
  
  // Renderizar grade de produtos responsiva (não virtualizada)
  return (
    <div className={cn('relative min-h-[200px]', className)}>
      {isLoading && products.length === 0 ? (
        <SkeletonGrid
          columns={columnCount}
          count={columnCount * 2}
          gap={16}
          itemHeight={200}
        />
      ) : isError ? (
        <div className="flex flex-col items-center justify-center py-12">
          <p className="text-destructive">{errorMessage}</p>
        </div>
      ) : products.length === 0 ? (
        <div className="flex flex-col items-center justify-center py-12">
          <p className="text-muted-foreground">{emptyMessage}</p>
        </div>
      ) : (
        <>
          <div 
            className={cn(
              gridLayout === 'responsive' 
                ? `grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4` 
                : `grid gap-4`,
              gridLayout === 'fixed' && {
                'grid-template-columns': `repeat(auto-fill, minmax(${minItemWidth}px, 1fr))`
              }
            )}
          >
            <AnimatePresence mode="popLayout">
              {products.map((product, index) => (
                <React.Fragment key={product.id}>
                  {renderProductItem(product, index)}
                </React.Fragment>
              ))}
            </AnimatePresence>
          </div>
          
          {/* Elemento para detecção de carregamento infinito */}
          {hasMore && (
            <div 
              ref={loadMoreRef} 
              className="w-full h-20 flex items-center justify-center mt-4"
            >
              {isLoading && (
                <Loader2 className="h-6 w-6 animate-spin text-primary" />
              )}
            </div>
          )}
        </>
      )}
    </div>
  );
}
