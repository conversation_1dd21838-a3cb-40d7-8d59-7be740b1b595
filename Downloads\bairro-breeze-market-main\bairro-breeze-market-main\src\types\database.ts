export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      products: {
        Row: {
          id: string
          created_at: string
          name: string
          description: string | null
          price: number
          original_price: number | null
          image: string
          category: string
          shop_id: string
          is_promo: boolean
          stock: number
          active: boolean
        }
        Insert: {
          id?: string
          created_at?: string
          name: string
          description?: string | null
          price: number
          original_price?: number | null
          image: string
          category: string
          shop_id: string
          is_promo?: boolean
          stock?: number
          active?: boolean
        }
        Update: {
          id?: string
          created_at?: string
          name?: string
          description?: string | null
          price?: number
          original_price?: number | null
          image?: string
          category?: string
          shop_id?: string
          is_promo?: boolean
          stock?: number
          active?: boolean
        }
      }
      shops: {
        Row: {
          id: string
          created_at: string
          name: string
          description: string | null
          image: string
          cover_image: string | null
          category: string
          delivery_time: string
          delivery_fee: number
          rating: number
          address: string
          phone: string
          opening_hours: string
          featured: boolean
          user_id: string
          active: boolean
        }
        Insert: {
          id?: string
          created_at?: string
          name: string
          description?: string | null
          image: string
          cover_image?: string | null
          category: string
          delivery_time: string
          delivery_fee: number
          rating?: number
          address: string
          phone: string
          opening_hours: string
          featured?: boolean
          user_id: string
          active?: boolean
        }
        Update: {
          id?: string
          created_at?: string
          name?: string
          description?: string | null
          image?: string
          cover_image?: string | null
          category?: string
          delivery_time?: string
          delivery_fee?: number
          rating?: number
          address?: string
          phone?: string
          opening_hours?: string
          featured?: boolean
          user_id?: string
          active?: boolean
        }
      }
      orders: {
        Row: {
          id: string
          created_at: string
          status: 'pending' | 'in_progress' | 'delivered' | 'cancelled'
          total: number
          delivery_address: string
          user_id: string
          shop_id: string
          deliverer_id: string | null
          payment_method: string
          payment_status: 'pending' | 'paid' | 'failed'
          delivery_instructions: string | null
        }
        Insert: {
          id?: string
          created_at?: string
          status?: 'pending' | 'in_progress' | 'delivered' | 'cancelled'
          total: number
          delivery_address: string
          user_id: string
          shop_id: string
          deliverer_id?: string | null
          payment_method: string
          payment_status?: 'pending' | 'paid' | 'failed'
          delivery_instructions?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          status?: 'pending' | 'in_progress' | 'delivered' | 'cancelled'
          total?: number
          delivery_address?: string
          user_id?: string
          shop_id?: string
          deliverer_id?: string | null
          payment_method?: string
          payment_status?: 'pending' | 'paid' | 'failed'
          delivery_instructions?: string | null
        }
      }
      order_items: {
        Row: {
          id: string
          created_at: string
          order_id: string
          product_id: string
          quantity: number
          price: number
          name: string
        }
        Insert: {
          id?: string
          created_at?: string
          order_id: string
          product_id: string
          quantity: number
          price: number
          name: string
        }
        Update: {
          id?: string
          created_at?: string
          order_id?: string
          product_id?: string
          quantity?: number
          price?: number
          name?: string
        }
      }
      profiles: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          name: string
          role: 'customer' | 'merchant' | 'deliverer'
          phone: string | null
          address: string | null
          avatar_url: string | null
        }
        Insert: {
          id: string
          created_at?: string
          updated_at?: string
          name: string
          role: 'customer' | 'merchant' | 'deliverer'
          phone?: string | null
          address?: string | null
          avatar_url?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          name?: string
          role?: 'customer' | 'merchant' | 'deliverer'
          phone?: string | null
          address?: string | null
          avatar_url?: string | null
        }
      }
      deliveries: {
        Row: {
          id: string
          created_at: string
          order_id: string
          deliverer_id: string
          status: 'pending' | 'in_progress' | 'completed' | 'cancelled'
          pickup_time: string | null
          delivery_time: string | null
          earnings: number
        }
        Insert: {
          id?: string
          created_at?: string
          order_id: string
          deliverer_id: string
          status?: 'pending' | 'in_progress' | 'completed' | 'cancelled'
          pickup_time?: string | null
          delivery_time?: string | null
          earnings: number
        }
        Update: {
          id?: string
          created_at?: string
          order_id?: string
          deliverer_id?: string
          status?: 'pending' | 'in_progress' | 'completed' | 'cancelled'
          pickup_time?: string | null
          delivery_time?: string | null
          earnings?: number
        }
      }
      categories: {
        Row: {
          id: string
          created_at: string
          name: string
          icon: string
        }
        Insert: {
          id?: string
          created_at?: string
          name: string
          icon: string
        }
        Update: {
          id?: string
          created_at?: string
          name?: string
          icon?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}

export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row']
export type Insertable<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert']
export type Updatable<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update']
