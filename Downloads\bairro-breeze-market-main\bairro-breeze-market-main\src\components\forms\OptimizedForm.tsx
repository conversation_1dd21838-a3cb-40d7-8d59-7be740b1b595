import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useForm, Controller, FieldValues, SubmitHandler, FieldErrors } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { cn } from '@/lib/utils';
import { Loader2, CheckCircle, AlertCircle, Info } from 'lucide-react';
import { useDebounce } from '@/hooks/use-memoization';

// Tipos de campos de formulário
type FieldType = 
  | 'text' 
  | 'email' 
  | 'password' 
  | 'number' 
  | 'tel' 
  | 'textarea' 
  | 'select' 
  | 'radio' 
  | 'checkbox'
  | 'date';

// Interface para definição de campo
interface FieldDefinition {
  name: string;
  label: string;
  type: FieldType;
  placeholder?: string;
  required?: boolean;
  options?: { value: string; label: string }[];
  defaultValue?: any;
  helperText?: string;
  validation?: z.ZodTypeAny;
  disabled?: boolean;
  className?: string;
  autoComplete?: string;
  mask?: (value: string) => string;
  icon?: React.ReactNode;
  min?: number;
  max?: number;
  step?: number;
  rows?: number;
}

// Interface para o componente de formulário
interface OptimizedFormProps {
  fields: FieldDefinition[];
  onSubmit: SubmitHandler<FieldValues>;
  schema?: z.ZodTypeAny;
  submitText?: string;
  cancelText?: string;
  onCancel?: () => void;
  isLoading?: boolean;
  successMessage?: string;
  errorMessage?: string;
  className?: string;
  formClassName?: string;
  showSuccessAnimation?: boolean;
  autoFocusFirstField?: boolean;
  validateOnBlur?: boolean;
  validateOnChange?: boolean;
  resetOnSubmit?: boolean;
  submitButtonClassName?: string;
  cancelButtonClassName?: string;
  layout?: 'vertical' | 'horizontal';
  fieldAnimation?: boolean;
}

/**
 * Componente de formulário otimizado com validação, animações e máscaras
 */
export function OptimizedForm({
  fields,
  onSubmit,
  schema,
  submitText = 'Enviar',
  cancelText = 'Cancelar',
  onCancel,
  isLoading = false,
  successMessage,
  errorMessage,
  className,
  formClassName,
  showSuccessAnimation = true,
  autoFocusFirstField = true,
  validateOnBlur = true,
  validateOnChange = false,
  resetOnSubmit = false,
  submitButtonClassName,
  cancelButtonClassName,
  layout = 'vertical',
  fieldAnimation = true,
}: OptimizedFormProps) {
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [isError, setIsError] = useState(false);
  const [errorMsg, setErrorMsg] = useState<string | null>(null);
  const firstFieldRef = useRef<HTMLInputElement>(null);
  
  // Criar schema Zod a partir das definições de campo se não for fornecido
  const formSchema = schema || createSchemaFromFields(fields);
  
  // Configurar React Hook Form
  const {
    control,
    handleSubmit,
    formState: { errors, isSubmitting, isDirty, isValid },
    reset,
    trigger,
    watch,
    setValue,
  } = useForm<FieldValues>({
    resolver: zodResolver(formSchema),
    mode: validateOnChange ? 'onChange' : validateOnBlur ? 'onBlur' : 'onSubmit',
    defaultValues: createDefaultValues(fields),
  });
  
  // Focar no primeiro campo ao montar o componente
  useEffect(() => {
    if (autoFocusFirstField && firstFieldRef.current) {
      firstFieldRef.current.focus();
    }
  }, [autoFocusFirstField]);
  
  // Função para lidar com o envio do formulário
  const handleFormSubmit: SubmitHandler<FieldValues> = async (data) => {
    setIsSubmitted(true);
    setIsSuccess(false);
    setIsError(false);
    setErrorMsg(null);
    
    try {
      await onSubmit(data);
      
      if (resetOnSubmit) {
        reset();
      }
      
      setIsSuccess(true);
      
      // Resetar estado de sucesso após 3 segundos
      if (showSuccessAnimation) {
        setTimeout(() => {
          setIsSuccess(false);
        }, 3000);
      }
    } catch (error) {
      setIsError(true);
      setErrorMsg(error instanceof Error ? error.message : errorMessage || 'Ocorreu um erro ao processar o formulário.');
    } finally {
      setIsSubmitted(false);
    }
  };
  
  // Variantes de animação para campos
  const fieldVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.05,
        duration: 0.3,
      },
    }),
  };
  
  // Renderizar campo com base no tipo
  const renderField = (field: FieldDefinition, index: number) => {
    const { name, label, type, placeholder, required, options, helperText, disabled, className, icon, mask, ...rest } = field;
    const error = errors[name];
    const isFieldInvalid = !!error;
    
    const fieldContent = (
      <div 
        className={cn(
          layout === 'vertical' ? 'space-y-2' : 'flex items-start',
          className
        )}
      >
        {type !== 'checkbox' && (
          <Label 
            htmlFor={name}
            className={cn(
              layout === 'horizontal' && 'w-1/3 pt-2',
              isFieldInvalid && 'text-destructive'
            )}
          >
            {label}
            {required && <span className="text-destructive ml-1">*</span>}
          </Label>
        )}
        
        <div className={cn(layout === 'horizontal' && 'w-2/3')}>
          <Controller
            name={name}
            control={control}
            render={({ field: { onChange, onBlur, value, ref } }) => {
              // Aplicar máscara se fornecida
              const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | string) => {
                let newValue;
                
                if (typeof e === 'string') {
                  newValue = e;
                } else {
                  newValue = e.target.value;
                }
                
                if (mask && typeof newValue === 'string') {
                  newValue = mask(newValue);
                }
                
                onChange(newValue);
              };
              
              switch (type) {
                case 'textarea':
                  return (
                    <div className="relative">
                      <Textarea
                        id={name}
                        placeholder={placeholder}
                        className={cn(
                          isFieldInvalid && 'border-destructive focus-visible:ring-destructive',
                          icon && 'pl-10'
                        )}
                        value={value || ''}
                        onChange={handleChange}
                        onBlur={onBlur}
                        disabled={disabled || isLoading}
                        rows={field.rows || 3}
                        ref={index === 0 ? firstFieldRef as any : ref}
                        {...rest}
                      />
                      {icon && (
                        <div className="absolute left-3 top-3 text-muted-foreground">
                          {icon}
                        </div>
                      )}
                    </div>
                  );
                
                case 'select':
                  return (
                    <Select
                      value={value || ''}
                      onValueChange={onChange}
                      disabled={disabled || isLoading}
                    >
                      <SelectTrigger 
                        className={cn(
                          isFieldInvalid && 'border-destructive focus-visible:ring-destructive',
                          icon && 'pl-10'
                        )}
                      >
                        {icon && (
                          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
                            {icon}
                          </div>
                        )}
                        <SelectValue placeholder={placeholder} />
                      </SelectTrigger>
                      <SelectContent>
                        {options?.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  );
                
                case 'radio':
                  return (
                    <RadioGroup
                      value={value || ''}
                      onValueChange={onChange}
                      disabled={disabled || isLoading}
                      className="space-y-2"
                    >
                      {options?.map((option) => (
                        <div key={option.value} className="flex items-center space-x-2">
                          <RadioGroupItem value={option.value} id={`${name}-${option.value}`} />
                          <Label htmlFor={`${name}-${option.value}`}>{option.label}</Label>
                        </div>
                      ))}
                    </RadioGroup>
                  );
                
                case 'checkbox':
                  return (
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id={name}
                        checked={value || false}
                        onCheckedChange={onChange}
                        disabled={disabled || isLoading}
                      />
                      <Label 
                        htmlFor={name}
                        className={isFieldInvalid ? 'text-destructive' : ''}
                      >
                        {label}
                        {required && <span className="text-destructive ml-1">*</span>}
                      </Label>
                    </div>
                  );
                
                default:
                  return (
                    <div className="relative">
                      <Input
                        id={name}
                        type={type}
                        placeholder={placeholder}
                        className={cn(
                          isFieldInvalid && 'border-destructive focus-visible:ring-destructive',
                          icon && 'pl-10'
                        )}
                        value={value || ''}
                        onChange={handleChange}
                        onBlur={onBlur}
                        disabled={disabled || isLoading}
                        ref={index === 0 ? firstFieldRef : ref}
                        {...rest}
                      />
                      {icon && (
                        <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
                          {icon}
                        </div>
                      )}
                    </div>
                  );
              }
            }}
          />
          
          {/* Mensagem de erro ou ajuda */}
          <AnimatePresence mode="wait">
            {isFieldInvalid ? (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.2 }}
                className="text-destructive text-sm mt-1 flex items-start"
              >
                <AlertCircle className="h-4 w-4 mr-1 mt-0.5 flex-shrink-0" />
                <span>{error?.message as string}</span>
              </motion.div>
            ) : helperText ? (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.2 }}
                className="text-muted-foreground text-sm mt-1 flex items-start"
              >
                <Info className="h-4 w-4 mr-1 mt-0.5 flex-shrink-0" />
                <span>{helperText}</span>
              </motion.div>
            ) : null}
          </AnimatePresence>
        </div>
      </div>
    );
    
    // Aplicar animação se habilitada
    if (fieldAnimation) {
      return (
        <motion.div
          key={name}
          custom={index}
          variants={fieldVariants}
          initial="hidden"
          animate="visible"
          className="mb-4"
        >
          {fieldContent}
        </motion.div>
      );
    }
    
    return <div key={name} className="mb-4">{fieldContent}</div>;
  };
  
  return (
    <div className={className}>
      <form 
        onSubmit={handleSubmit(handleFormSubmit)} 
        className={cn('space-y-6', formClassName)}
        noValidate
      >
        <div className="space-y-4">
          {fields.map((field, index) => renderField(field, index))}
        </div>
        
        {/* Mensagens de sucesso/erro */}
        <AnimatePresence mode="wait">
          {isSuccess && successMessage && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="bg-green-50 text-green-700 p-3 rounded-md flex items-center"
            >
              <CheckCircle className="h-5 w-5 mr-2 text-green-500" />
              {successMessage}
            </motion.div>
          )}
          
          {isError && (errorMsg || errorMessage) && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="bg-destructive/10 text-destructive p-3 rounded-md flex items-center"
            >
              <AlertCircle className="h-5 w-5 mr-2" />
              {errorMsg || errorMessage}
            </motion.div>
          )}
        </AnimatePresence>
        
        {/* Botões de ação */}
        <div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 space-y-2 space-y-reverse sm:space-y-0">
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading || isSubmitting}
              className={cancelButtonClassName}
            >
              {cancelText}
            </Button>
          )}
          
          <Button
            type="submit"
            disabled={isLoading || isSubmitting || (!isDirty && !isValid)}
            className={cn('bg-cta hover:bg-cta-dark', submitButtonClassName)}
          >
            {isLoading || isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processando...
              </>
            ) : isSuccess ? (
              <>
                <CheckCircle className="mr-2 h-4 w-4" />
                Enviado
              </>
            ) : (
              submitText
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}

// Função auxiliar para criar schema Zod a partir das definições de campo
function createSchemaFromFields(fields: FieldDefinition[]): z.ZodTypeAny {
  const schemaMap: Record<string, z.ZodTypeAny> = {};
  
  fields.forEach((field) => {
    if (field.validation) {
      schemaMap[field.name] = field.validation;
    } else {
      let baseSchema: z.ZodTypeAny;
      
      switch (field.type) {
        case 'email':
          baseSchema = z.string().email('Email inválido');
          break;
        case 'number':
          baseSchema = z.coerce.number();
          if (field.min !== undefined) baseSchema = baseSchema.min(field.min, `Deve ser maior ou igual a ${field.min}`);
          if (field.max !== undefined) baseSchema = baseSchema.max(field.max, `Deve ser menor ou igual a ${field.max}`);
          break;
        case 'checkbox':
          baseSchema = z.boolean();
          break;
        default:
          baseSchema = z.string();
          break;
      }
      
      if (field.required) {
        schemaMap[field.name] = baseSchema.min(1, 'Campo obrigatório');
      } else {
        schemaMap[field.name] = baseSchema.optional();
      }
    }
  });
  
  return z.object(schemaMap);
}

// Função auxiliar para criar valores padrão a partir das definições de campo
function createDefaultValues(fields: FieldDefinition[]): Record<string, any> {
  const defaultValues: Record<string, any> = {};
  
  fields.forEach((field) => {
    defaultValues[field.name] = field.defaultValue !== undefined ? field.defaultValue : '';
  });
  
  return defaultValues;
}
