import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Store, Package, TrendingUp, Users, Clock, DollarSign, ShoppingBag, LogOut, Settings, CreditCard } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { AnimatedPage } from "@/components/animations";
import { LoadingState } from "@/components/ui/async-state";
import { useAuth } from "@/hooks/useAuth";
import { MerchantProfileForm } from "@/components/merchant/MerchantProfileForm";

const MerchantProfile = () => {
  const { user, signOut } = useAuth();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("profile");
  const [loading, setLoading] = useState(false);

  const handleSignOut = async () => {
    try {
      setLoading(true);
      await signOut();
      navigate("/login");
    } catch (error) {
      console.error("Error signing out:", error);
      toast({
        title: "Erro ao sair",
        description: "Não foi possível sair da sua conta. Tente novamente mais tarde.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingState message="Carregando..." />
      </div>
    );
  }

  return (
    <AnimatedPage className="min-h-screen bg-gray-50">
      <header className="bg-eco text-white sticky top-0 z-10">
        <div className="container px-4 h-16 flex items-center justify-between">
          <div className="flex items-center">
            <Store className="h-6 w-6 mr-2" />
            <h1 className="text-xl font-medium">Perfil da Loja</h1>
          </div>
          <Button variant="ghost" onClick={handleSignOut} className="text-white">
            <LogOut className="h-5 w-5 mr-2" />
            Sair
          </Button>
        </div>
      </header>

      <main className="container px-4 py-6">
        <div className="flex flex-col md:flex-row gap-6">
          {/* Sidebar */}
          <div className="w-full md:w-1/4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Store className="mr-2 h-5 w-5" />
                  Minha Loja
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <Tabs
                  value={activeTab}
                  onValueChange={setActiveTab}
                  orientation="vertical"
                  className="w-full"
                >
                  <TabsList className="flex flex-col items-start h-auto p-0 bg-transparent">
                    <TabsTrigger
                      value="profile"
                      className="w-full justify-start px-4 py-2 data-[state=active]:bg-muted"
                    >
                      <Store className="mr-2 h-4 w-4" />
                      Informações da Loja
                    </TabsTrigger>
                    <TabsTrigger
                      value="payment"
                      className="w-full justify-start px-4 py-2 data-[state=active]:bg-muted"
                    >
                      <CreditCard className="mr-2 h-4 w-4" />
                      Métodos de Pagamento
                    </TabsTrigger>
                    <TabsTrigger
                      value="settings"
                      className="w-full justify-start px-4 py-2 data-[state=active]:bg-muted"
                    >
                      <Settings className="mr-2 h-4 w-4" />
                      Configurações
                    </TabsTrigger>
                  </TabsList>
                </Tabs>
              </CardContent>
              <CardFooter className="border-t p-4">
                <Button
                  variant="outline"
                  className="w-full"
                  onClick={() => navigate("/merchant/dashboard")}
                >
                  <TrendingUp className="mr-2 h-4 w-4" />
                  Voltar ao Dashboard
                </Button>
              </CardFooter>
            </Card>
          </div>

          {/* Main Content */}
          <div className="w-full md:w-3/4">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsContent value="profile" className="mt-0">
                <MerchantProfileForm />
              </TabsContent>

              <TabsContent value="payment" className="mt-0">
                <Card>
                  <CardHeader>
                    <CardTitle>Métodos de Pagamento</CardTitle>
                    <CardDescription>
                      Configure os métodos de pagamento aceitos pela sua loja
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between border-b pb-4">
                        <div className="flex items-center">
                          <CreditCard className="h-5 w-5 mr-3 text-muted-foreground" />
                          <div>
                            <p className="font-medium">Cartão de Crédito</p>
                            <p className="text-sm text-muted-foreground">Aceitar pagamentos com cartão de crédito</p>
                          </div>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="credit-card"
                            className="h-4 w-4 rounded border-gray-300 text-cta focus:ring-cta"
                            defaultChecked
                          />
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between border-b pb-4">
                        <div className="flex items-center">
                          <CreditCard className="h-5 w-5 mr-3 text-muted-foreground" />
                          <div>
                            <p className="font-medium">Cartão de Débito</p>
                            <p className="text-sm text-muted-foreground">Aceitar pagamentos com cartão de débito</p>
                          </div>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="debit-card"
                            className="h-4 w-4 rounded border-gray-300 text-cta focus:ring-cta"
                            defaultChecked
                          />
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between border-b pb-4">
                        <div className="flex items-center">
                          <DollarSign className="h-5 w-5 mr-3 text-muted-foreground" />
                          <div>
                            <p className="font-medium">Dinheiro</p>
                            <p className="text-sm text-muted-foreground">Aceitar pagamentos em dinheiro</p>
                          </div>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="cash"
                            className="h-4 w-4 rounded border-gray-300 text-cta focus:ring-cta"
                            defaultChecked
                          />
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <CreditCard className="h-5 w-5 mr-3 text-muted-foreground" />
                          <div>
                            <p className="font-medium">PIX</p>
                            <p className="text-sm text-muted-foreground">Aceitar pagamentos via PIX</p>
                          </div>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="pix"
                            className="h-4 w-4 rounded border-gray-300 text-cta focus:ring-cta"
                            defaultChecked
                          />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button className="w-full bg-cta hover:bg-cta-dark">
                      Salvar Configurações
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>

              <TabsContent value="settings" className="mt-0">
                <Card>
                  <CardHeader>
                    <CardTitle>Configurações da Conta</CardTitle>
                    <CardDescription>
                      Gerencie as configurações da sua conta
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between border-b pb-4">
                        <div>
                          <p className="font-medium">Notificações por Email</p>
                          <p className="text-sm text-muted-foreground">Receber notificações por email</p>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="email-notifications"
                            className="h-4 w-4 rounded border-gray-300 text-cta focus:ring-cta"
                            defaultChecked
                          />
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between border-b pb-4">
                        <div>
                          <p className="font-medium">Notificações Push</p>
                          <p className="text-sm text-muted-foreground">Receber notificações push</p>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="push-notifications"
                            className="h-4 w-4 rounded border-gray-300 text-cta focus:ring-cta"
                            defaultChecked
                          />
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">Modo de Férias</p>
                          <p className="text-sm text-muted-foreground">Desativar temporariamente sua loja</p>
                        </div>
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id="vacation-mode"
                            className="h-4 w-4 rounded border-gray-300 text-cta focus:ring-cta"
                          />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="flex flex-col space-y-2">
                    <Button className="w-full bg-cta hover:bg-cta-dark">
                      Salvar Configurações
                    </Button>
                    <Button variant="destructive" className="w-full">
                      Desativar Conta
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </main>
    </AnimatedPage>
  );
};

export default MerchantProfile;
