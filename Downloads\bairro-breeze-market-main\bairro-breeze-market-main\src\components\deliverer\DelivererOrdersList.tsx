import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  Package, 
  Clock, 
  ChevronRight, 
  Loader2, 
  ShoppingBag,
  CheckCircle,
  Truck,
  AlertCircle,
  Search,
  Filter,
  X,
  MapPin,
  Navigation,
  DollarSign
} from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";
import { Order } from "@/types/order";
import { fetchPendingOrders, fetchOrdersByDelivererId } from "@/services/deliveries";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog";
import { DeliveryMap } from "@/components/map/DeliveryMap";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { AnimatedList } from "@/components/animations";

export function DelivererOrdersList() {
  const { user } = useAuth();
  
  const [isLoading, setIsLoading] = useState(true);
  const [availableOrders, setAvailableOrders] = useState<Order[]>([]);
  const [myOrders, setMyOrders] = useState<Order[]>([]);
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([]);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [isMapOpen, setIsMapOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [activeTab, setActiveTab] = useState<"available" | "my">("available");
  
  // Filters
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState("");
  
  // Load orders
  useEffect(() => {
    const loadOrders = async () => {
      if (!user) return;
      
      setIsLoading(true);
      
      try {
        // Load available orders
        const pendingOrders = await fetchPendingOrders();
        setAvailableOrders(pendingOrders);
        
        // Load my orders
        const delivererOrders = await fetchOrdersByDelivererId(user.id);
        setMyOrders(delivererOrders);
        
        // Set filtered orders based on active tab
        setFilteredOrders(activeTab === "available" ? pendingOrders : delivererOrders);
      } catch (error) {
        console.error("Error loading orders:", error);
        toast({
          title: "Erro ao carregar pedidos",
          description: "Não foi possível carregar os pedidos. Tente novamente mais tarde.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    loadOrders();
  }, [user, activeTab]);
  
  // Apply filters
  useEffect(() => {
    let result = activeTab === "available" ? [...availableOrders] : [...myOrders];
    
    // Apply status filter
    if (statusFilter !== "all") {
      result = result.filter(order => order.status === statusFilter);
    }
    
    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      result = result.filter(order => 
        order.id.toLowerCase().includes(query) ||
        order.shopName.toLowerCase().includes(query) ||
        order.deliveryAddress.toLowerCase().includes(query)
      );
    }
    
    setFilteredOrders(result);
  }, [availableOrders, myOrders, activeTab, statusFilter, searchQuery]);
  
  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return format(date, "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });
  };
  
  // Get status badge
  const getStatusBadge = (status: Order["status"]) => {
    switch (status) {
      case "pending":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
            Pendente
          </Badge>
        );
      case "in_progress":
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800 hover:bg-blue-100">
            Em andamento
          </Badge>
        );
      case "delivered":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-100">
            Entregue
          </Badge>
        );
      case "cancelled":
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800 hover:bg-red-100">
            Cancelado
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-gray-100 text-gray-800 hover:bg-gray-100">
            Desconhecido
          </Badge>
        );
    }
  };
  
  // Get status icon
  const getStatusIcon = (status: Order["status"]) => {
    switch (status) {
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case "in_progress":
        return <Truck className="h-4 w-4 text-blue-500" />;
      case "delivered":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "cancelled":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Package className="h-4 w-4 text-gray-500" />;
    }
  };
  
  // Handle view order details
  const handleViewOrderDetails = (order: Order) => {
    setSelectedOrder(order);
    setIsDetailsOpen(true);
  };
  
  // Handle accept order
  const handleAcceptOrder = async (orderId: string) => {
    setIsProcessing(true);
    
    try {
      // In a real app, this would call an API to accept the order
      // For now, simulate API call with a delay
      setTimeout(() => {
        // Find the order
        const order = availableOrders.find(o => o.id === orderId);
        
        if (!order) {
          throw new Error("Pedido não encontrado");
        }
        
        // Update order status
        const updatedOrder = {
          ...order,
          status: "in_progress" as const,
          delivererId: user?.id,
          delivererName: user?.user_metadata?.name || "Entregador"
        };
        
        // Update available orders
        setAvailableOrders(prevOrders => 
          prevOrders.filter(o => o.id !== orderId)
        );
        
        // Update my orders
        setMyOrders(prevOrders => [...prevOrders, updatedOrder]);
        
        // Update filtered orders if on my orders tab
        if (activeTab === "my") {
          setFilteredOrders(prevOrders => [...prevOrders, updatedOrder]);
        } else {
          setFilteredOrders(prevOrders => 
            prevOrders.filter(o => o.id !== orderId)
          );
        }
        
        // Close dialog if open
        setIsDetailsOpen(false);
        
        toast({
          title: "Pedido aceito",
          description: "Você aceitou o pedido com sucesso.",
        });
        
        setIsProcessing(false);
      }, 1500);
    } catch (error) {
      console.error("Error accepting order:", error);
      toast({
        title: "Erro ao aceitar pedido",
        description: "Não foi possível aceitar o pedido. Tente novamente mais tarde.",
        variant: "destructive",
      });
      setIsProcessing(false);
    }
  };
  
  // Handle complete order
  const handleCompleteOrder = async (orderId: string) => {
    setIsProcessing(true);
    
    try {
      // In a real app, this would call an API to complete the order
      // For now, simulate API call with a delay
      setTimeout(() => {
        // Find the order
        const order = myOrders.find(o => o.id === orderId);
        
        if (!order) {
          throw new Error("Pedido não encontrado");
        }
        
        // Update order status
        const updatedOrder = {
          ...order,
          status: "delivered" as const
        };
        
        // Update my orders
        setMyOrders(prevOrders => 
          prevOrders.map(o => o.id === orderId ? updatedOrder : o)
        );
        
        // Update filtered orders if on my orders tab
        if (activeTab === "my") {
          setFilteredOrders(prevOrders => 
            prevOrders.map(o => o.id === orderId ? updatedOrder : o)
          );
        }
        
        // Close dialogs if open
        setIsDetailsOpen(false);
        setIsMapOpen(false);
        
        toast({
          title: "Pedido entregue",
          description: "Você marcou o pedido como entregue com sucesso.",
        });
        
        setIsProcessing(false);
      }, 1500);
    } catch (error) {
      console.error("Error completing order:", error);
      toast({
        title: "Erro ao completar pedido",
        description: "Não foi possível marcar o pedido como entregue. Tente novamente mais tarde.",
        variant: "destructive",
      });
      setIsProcessing(false);
    }
  };
  
  // Clear filters
  const clearFilters = () => {
    setStatusFilter("all");
    setSearchQuery("");
  };
  
  // Calculate earnings
  const calculateEarnings = (total: number) => {
    // In a real app, this would be based on a more complex formula
    // For now, use a simple percentage
    return total * 0.1; // 10% of order total
  };
  
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <CardTitle>Entregas</CardTitle>
              <CardDescription>
                Gerencie suas entregas
              </CardDescription>
            </div>
            <div className="flex flex-col sm:flex-row gap-2">
              <div className="flex gap-2">
                <Button
                  variant={activeTab === "available" ? "default" : "outline"}
                  onClick={() => setActiveTab("available")}
                  className={activeTab === "available" ? "bg-trust hover:bg-trust-dark" : ""}
                >
                  <Package className="mr-2 h-4 w-4" />
                  Disponíveis
                </Button>
                <Button
                  variant={activeTab === "my" ? "default" : "outline"}
                  onClick={() => setActiveTab("my")}
                  className={activeTab === "my" ? "bg-trust hover:bg-trust-dark" : ""}
                >
                  <Truck className="mr-2 h-4 w-4" />
                  Minhas Entregas
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-2 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Buscar entregas..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-8"
              />
              {searchQuery && (
                <button
                  className="absolute right-2 top-1/2 transform -translate-y-1/2"
                  onClick={() => setSearchQuery("")}
                >
                  <X className="h-4 w-4 text-muted-foreground" />
                </button>
              )}
            </div>
            <Select
              value={statusFilter}
              onValueChange={setStatusFilter}
            >
              <SelectTrigger className="w-full sm:w-[180px]">
                <div className="flex items-center">
                  <Filter className="mr-2 h-4 w-4" />
                  <SelectValue placeholder="Filtrar por status" />
                </div>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos os status</SelectItem>
                <SelectItem value="pending">Pendentes</SelectItem>
                <SelectItem value="in_progress">Em andamento</SelectItem>
                <SelectItem value="delivered">Entregues</SelectItem>
                <SelectItem value="cancelled">Cancelados</SelectItem>
              </SelectContent>
            </Select>
            {(statusFilter !== "all" || searchQuery) && (
              <Button
                variant="outline"
                size="icon"
                onClick={clearFilters}
                className="h-10 w-10"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
          
          {filteredOrders.length === 0 ? (
            <div className="text-center py-8">
              <ShoppingBag className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-1">Nenhuma entrega encontrada</h3>
              <p className="text-muted-foreground mb-4">
                {searchQuery || statusFilter !== "all" 
                  ? "Tente ajustar os filtros para ver mais entregas" 
                  : activeTab === "available" 
                    ? "Não há entregas disponíveis no momento" 
                    : "Você ainda não aceitou nenhuma entrega"}
              </p>
              {(searchQuery || statusFilter !== "all") && (
                <Button 
                  variant="outline"
                  onClick={clearFilters}
                >
                  Limpar filtros
                </Button>
              )}
            </div>
          ) : (
            <AnimatedList className="space-y-4" delay={0.1} staggerDelay={0.05}>
              {filteredOrders.map((order) => (
                <div 
                  key={order.id} 
                  className="border rounded-lg p-4 hover:border-trust transition-colors"
                >
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div>
                      <div className="flex items-center">
                        <p className="font-medium">Pedido #{order.id.substring(0, 8)}</p>
                        <div className="ml-2">
                          {getStatusBadge(order.status)}
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground flex items-center mt-1">
                        <Clock className="mr-1 h-3 w-3" />
                        {formatDate(order.createdAt)}
                      </p>
                      <div className="mt-2">
                        <p className="text-sm font-medium">{order.shopName}</p>
                        <p className="text-sm text-muted-foreground truncate max-w-[300px]">
                          {order.deliveryAddress}
                        </p>
                      </div>
                      <div className="mt-2 flex items-center text-sm">
                        {getStatusIcon(order.status)}
                        <span className="ml-1">
                          {order.status === "pending" && "Aguardando aceitação"}
                          {order.status === "in_progress" && "Em andamento"}
                          {order.status === "delivered" && "Entregue"}
                          {order.status === "cancelled" && "Cancelado"}
                        </span>
                      </div>
                    </div>
                    <div className="flex flex-col sm:items-end gap-2">
                      <div className="text-right">
                        <p className="text-xs text-muted-foreground">Ganho estimado</p>
                        <p className="font-bold">{formatCurrency(calculateEarnings(order.total))}</p>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewOrderDetails(order)}
                        >
                          Detalhes
                          <ChevronRight className="ml-1 h-4 w-4" />
                        </Button>
                        {activeTab === "available" && order.status === "pending" && (
                          <Button
                            size="sm"
                            className="bg-trust hover:bg-trust-dark"
                            onClick={() => handleAcceptOrder(order.id)}
                            disabled={isProcessing}
                          >
                            {isProcessing ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <>
                                <Truck className="mr-1 h-4 w-4" />
                                Aceitar
                              </>
                            )}
                          </Button>
                        )}
                        {activeTab === "my" && order.status === "in_progress" && (
                          <Button
                            size="sm"
                            className="bg-trust hover:bg-trust-dark"
                            onClick={() => setIsMapOpen(true)}
                          >
                            <Navigation className="mr-1 h-4 w-4" />
                            Navegar
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </AnimatedList>
          )}
        </CardContent>
      </Card>
      
      {/* Order Details Dialog */}
      <Dialog open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Detalhes da Entrega</DialogTitle>
            <DialogDescription>
              {selectedOrder && `Pedido #${selectedOrder.id.substring(0, 8)}`}
            </DialogDescription>
          </DialogHeader>
          
          {selectedOrder && (
            <div className="space-y-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm text-muted-foreground">Data do Pedido</p>
                  <p className="font-medium">{formatDate(selectedOrder.createdAt)}</p>
                </div>
                <div>
                  {getStatusBadge(selectedOrder.status)}
                </div>
              </div>
              
              <div>
                <p className="text-sm text-muted-foreground mb-1">Estabelecimento</p>
                <div className="flex items-center">
                  <MapPin className="h-4 w-4 mr-2 text-muted-foreground" />
                  <p className="font-medium">{selectedOrder.shopName}</p>
                </div>
              </div>
              
              <div>
                <p className="text-sm text-muted-foreground mb-1">Endereço de Entrega</p>
                <div className="flex items-start">
                  <MapPin className="h-4 w-4 mr-2 text-muted-foreground flex-shrink-0 mt-0.5" />
                  <p className="font-medium">{selectedOrder.deliveryAddress}</p>
                </div>
              </div>
              
              {selectedOrder.notes && (
                <div>
                  <p className="text-sm text-muted-foreground mb-1">Observações</p>
                  <p className="text-sm">{selectedOrder.notes}</p>
                </div>
              )}
              
              <div>
                <p className="text-sm text-muted-foreground mb-2">Itens do Pedido</p>
                <div className="space-y-2">
                  {selectedOrder.items.map((item, index) => (
                    <div key={index} className="flex justify-between items-center py-2 border-b">
                      <div className="flex items-center">
                        <div className="mr-3 font-medium">{item.quantity}x</div>
                        <div>{item.name}</div>
                      </div>
                      <div className="font-medium">{formatCurrency(item.price * item.quantity)}</div>
                    </div>
                  ))}
                  <div className="flex justify-between items-center pt-2">
                    <div className="font-medium">Total</div>
                    <div className="font-bold">{formatCurrency(selectedOrder.total)}</div>
                  </div>
                </div>
              </div>
              
              <div>
                <p className="text-sm text-muted-foreground mb-1">Ganho estimado</p>
                <div className="flex items-center">
                  <DollarSign className="h-4 w-4 mr-2 text-trust" />
                  <p className="font-bold">{formatCurrency(calculateEarnings(selectedOrder.total))}</p>
                </div>
              </div>
            </div>
          )}
          
          <DialogFooter className="flex flex-col sm:flex-row gap-2">
            <Button
              variant="outline"
              onClick={() => setIsDetailsOpen(false)}
              className="sm:order-1"
            >
              Fechar
            </Button>
            
            {selectedOrder && activeTab === "available" && selectedOrder.status === "pending" && (
              <Button
                className="bg-trust hover:bg-trust-dark sm:order-2"
                onClick={() => handleAcceptOrder(selectedOrder.id)}
                disabled={isProcessing}
              >
                {isProcessing ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Truck className="h-4 w-4 mr-2" />
                )}
                Aceitar Entrega
              </Button>
            )}
            
            {selectedOrder && activeTab === "my" && selectedOrder.status === "in_progress" && (
              <>
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsDetailsOpen(false);
                    setIsMapOpen(true);
                  }}
                  className="sm:order-2"
                >
                  <Navigation className="h-4 w-4 mr-2" />
                  Ver Rota
                </Button>
                
                <Button
                  className="bg-trust hover:bg-trust-dark sm:order-3"
                  onClick={() => handleCompleteOrder(selectedOrder.id)}
                  disabled={isProcessing}
                >
                  {isProcessing ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <CheckCircle className="h-4 w-4 mr-2" />
                  )}
                  Concluir Entrega
                </Button>
              </>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Map Dialog */}
      <Dialog open={isMapOpen} onOpenChange={setIsMapOpen}>
        <DialogContent className="sm:max-w-[800px] sm:h-[600px]">
          <DialogHeader>
            <DialogTitle>Rota de Entrega</DialogTitle>
            <DialogDescription>
              {selectedOrder && `Pedido #${selectedOrder.id.substring(0, 8)}`}
            </DialogDescription>
          </DialogHeader>
          
          {selectedOrder && (
            <>
              <DeliveryMap 
                order={selectedOrder}
                delivererName={user?.user_metadata?.name || "Entregador"}
                estimatedTime={25}
              />
              
              <div className="mt-4 space-y-4">
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center">
                    <div className="h-10 w-10 rounded-full bg-trust-light flex items-center justify-center mr-3">
                      <MapPin className="h-5 w-5 text-trust" />
                    </div>
                    <div>
                      <p className="font-medium">{selectedOrder.deliveryAddress}</p>
                      <p className="text-sm text-muted-foreground">Endereço de entrega</p>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm text-muted-foreground">Ganho estimado</p>
                    <p className="font-bold text-lg">{formatCurrency(calculateEarnings(selectedOrder.total))}</p>
                  </div>
                </div>
              </div>
              
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setIsMapOpen(false)}
                >
                  Fechar
                </Button>
                
                {selectedOrder.status === "in_progress" && (
                  <Button
                    className="bg-trust hover:bg-trust-dark"
                    onClick={() => handleCompleteOrder(selectedOrder.id)}
                    disabled={isProcessing}
                  >
                    {isProcessing ? (
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    ) : (
                      <CheckCircle className="h-4 w-4 mr-2" />
                    )}
                    Concluir Entrega
                  </Button>
                )}
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
