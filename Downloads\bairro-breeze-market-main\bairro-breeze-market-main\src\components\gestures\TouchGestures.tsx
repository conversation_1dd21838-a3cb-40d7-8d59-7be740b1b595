import React, { useState, useRef, useEffect, ReactNode } from 'react';
import { motion, useMotionValue, useTransform, PanInfo, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { cn } from '@/lib/utils';

interface SwipeableProps {
  children: ReactNode;
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  swipeThreshold?: number;
  className?: string;
  disableHorizontal?: boolean;
  disableVertical?: boolean;
  enableBackNavigation?: boolean;
}

/**
 * Componente para detecção de gestos de swipe
 */
export function Swipeable({
  children,
  onSwipeLeft,
  onSwipeRight,
  onSwipeUp,
  onSwipeDown,
  swipeThreshold = 100,
  className,
  disableHorizontal = false,
  disableVertical = true,
  enableBackNavigation = false,
}: SwipeableProps) {
  const x = useMotionValue(0);
  const y = useMotionValue(0);
  const navigate = useNavigate();
  const [isSwipeActive, setIsSwipeActive] = useState(false);
  
  // Manipulador de gesto de arrastar
  const handleDragEnd = (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    const { offset } = info;
    
    if (!disableHorizontal) {
      if (offset.x < -swipeThreshold && onSwipeLeft) {
        onSwipeLeft();
      } else if (offset.x > swipeThreshold && onSwipeRight) {
        if (enableBackNavigation && !onSwipeRight) {
          navigate(-1);
        } else {
          onSwipeRight();
        }
      }
    }
    
    if (!disableVertical) {
      if (offset.y < -swipeThreshold && onSwipeUp) {
        onSwipeUp();
      } else if (offset.y > swipeThreshold && onSwipeDown) {
        onSwipeDown();
      }
    }
    
    // Resetar posição
    x.set(0);
    y.set(0);
    setIsSwipeActive(false);
  };
  
  return (
    <motion.div
      className={cn('touch-pan-y', className)}
      drag={!disableHorizontal || !disableVertical}
      dragDirectionLock
      dragConstraints={{ left: 0, right: 0, top: 0, bottom: 0 }}
      dragElastic={0.1}
      onDragStart={() => setIsSwipeActive(true)}
      onDragEnd={handleDragEnd}
      style={{ x, y }}
      dragTransition={{ bounceStiffness: 600, bounceDamping: 30 }}
    >
      {children}
    </motion.div>
  );
}

interface SwipeableCardProps {
  children: ReactNode;
  onDismiss?: () => void;
  dismissDirection?: 'left' | 'right' | 'up' | 'down';
  className?: string;
  dismissThreshold?: number;
  allowDrag?: boolean;
}

/**
 * Componente de card com gesto de swipe para dispensar
 */
export function SwipeableCard({
  children,
  onDismiss,
  dismissDirection = 'right',
  className,
  dismissThreshold = 0.5,
  allowDrag = true,
}: SwipeableCardProps) {
  const [isDismissed, setIsDismissed] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);
  
  // Valores de movimento
  const x = useMotionValue(0);
  const y = useMotionValue(0);
  
  // Transformações baseadas na direção
  const xRotation = useTransform(y, [-200, 200], [15, -15]);
  const yRotation = useTransform(x, [-200, 200], [-15, 15]);
  const scale = useTransform(
    dismissDirection === 'left' || dismissDirection === 'right' ? x : y,
    [-300, 0, 300],
    [0.8, 1, 0.8]
  );
  
  // Calcular opacidade baseada na direção
  const opacity = useTransform(
    dismissDirection === 'left' || dismissDirection === 'right' ? x : y,
    [-300, 0, 300],
    [0.5, 1, 0.5]
  );
  
  // Manipulador de gesto de arrastar
  const handleDragEnd = (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    const { offset, velocity } = info;
    
    // Verificar se o card deve ser dispensado
    const shouldDismiss = () => {
      if (!cardRef.current) return false;
      
      const cardWidth = cardRef.current.offsetWidth;
      const cardHeight = cardRef.current.offsetHeight;
      
      switch (dismissDirection) {
        case 'left':
          return offset.x < -cardWidth * dismissThreshold || velocity.x < -500;
        case 'right':
          return offset.x > cardWidth * dismissThreshold || velocity.x > 500;
        case 'up':
          return offset.y < -cardHeight * dismissThreshold || velocity.y < -500;
        case 'down':
          return offset.y > cardHeight * dismissThreshold || velocity.y > 500;
      }
    };
    
    if (shouldDismiss()) {
      // Animar para fora da tela
      const target = {
        x: dismissDirection === 'left' ? -1000 : dismissDirection === 'right' ? 1000 : 0,
        y: dismissDirection === 'up' ? -1000 : dismissDirection === 'down' ? 1000 : 0,
      };
      
      // Definir como dispensado
      setIsDismissed(true);
      
      // Chamar callback após animação
      setTimeout(() => {
        if (onDismiss) onDismiss();
      }, 300);
    } else {
      // Resetar posição
      x.set(0);
      y.set(0);
    }
  };
  
  return (
    <AnimatePresence>
      {!isDismissed && (
        <motion.div
          ref={cardRef}
          className={cn('touch-pan-y', className)}
          style={{
            x,
            y,
            rotateX: xRotation,
            rotateY: yRotation,
            scale,
            opacity,
          }}
          drag={allowDrag}
          dragConstraints={{ left: 0, right: 0, top: 0, bottom: 0 }}
          dragElastic={0.7}
          onDragEnd={handleDragEnd}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{
            opacity: 0,
            scale: 0.8,
            x: dismissDirection === 'left' ? -300 : dismissDirection === 'right' ? 300 : 0,
            y: dismissDirection === 'up' ? -300 : dismissDirection === 'down' ? 300 : 0,
            transition: { duration: 0.3 },
          }}
          transition={{
            type: 'spring',
            stiffness: 300,
            damping: 20,
          }}
        >
          {children}
        </motion.div>
      )}
    </AnimatePresence>
  );
}

interface PullToRefreshProps {
  children: ReactNode;
  onRefresh: () => Promise<void>;
  className?: string;
  pullDistance?: number;
  refreshingContent?: ReactNode;
  pullingContent?: ReactNode;
}

/**
 * Componente de pull-to-refresh
 */
export function PullToRefresh({
  children,
  onRefresh,
  className,
  pullDistance = 100,
  refreshingContent,
  pullingContent,
}: PullToRefreshProps) {
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isPulling, setIsPulling] = useState(false);
  const y = useMotionValue(0);
  const pullProgress = useTransform(y, [0, pullDistance], [0, 1]);
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Manipulador de gesto de arrastar
  const handleDragEnd = async (event: MouseEvent | TouchEvent | PointerEvent, info: PanInfo) => {
    const { offset } = info;
    
    if (offset.y >= pullDistance && !isRefreshing) {
      setIsRefreshing(true);
      
      try {
        await onRefresh();
      } catch (error) {
        console.error('Error refreshing:', error);
      } finally {
        setIsRefreshing(false);
      }
    }
    
    // Resetar posição
    y.set(0);
    setIsPulling(false);
  };
  
  // Verificar se está no topo da página
  const checkIfAtTop = () => {
    if (containerRef.current) {
      return containerRef.current.scrollTop <= 0;
    }
    return false;
  };
  
  // Manipulador de início de arrasto
  const handleDragStart = () => {
    if (checkIfAtTop()) {
      setIsPulling(true);
    }
  };
  
  return (
    <div
      ref={containerRef}
      className={cn('overflow-auto relative', className)}
    >
      <motion.div
        drag="y"
        dragConstraints={{ top: 0, bottom: 0 }}
        dragElastic={0.1}
        dragDirectionLock
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        style={{ y }}
      >
        {/* Indicador de pull-to-refresh */}
        <AnimatePresence>
          {(isPulling || isRefreshing) && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="flex items-center justify-center py-4 bg-background"
            >
              {isRefreshing ? (
                refreshingContent || (
                  <div className="flex items-center">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                      className="h-6 w-6 border-2 border-primary border-t-transparent rounded-full mr-2"
                    />
                    <span>Atualizando...</span>
                  </div>
                )
              ) : (
                pullingContent || (
                  <div className="flex items-center">
                    <motion.div
                      style={{ rotate: pullProgress.get() * 180 }}
                      className="h-6 w-6 mr-2"
                    >
                      ↓
                    </motion.div>
                    <span>Puxe para atualizar</span>
                  </div>
                )
              )}
            </motion.div>
          )}
        </AnimatePresence>
        
        {/* Conteúdo principal */}
        {children}
      </motion.div>
    </div>
  );
}
