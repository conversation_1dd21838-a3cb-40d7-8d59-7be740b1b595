import { Link, useLocation } from "react-router-dom";
import { useAuth } from "@/hooks/useAuth";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { 
  Store, 
  Package, 
  TrendingUp, 
  Settings, 
  LogOut, 
  User, 
  Bell, 
  Menu 
} from "lucide-react";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { motion } from "framer-motion";

interface MerchantNavProps {
  activeTab?: string;
}

export function MerchantNav({ activeTab }: MerchantNavProps) {
  const { user, signOut } = useAuth();
  const location = useLocation();
  const pathname = location.pathname;

  // Obter iniciais do nome do usuário para o avatar
  const getInitials = () => {
    if (!user?.user_metadata?.name) return "M";
    
    const nameParts = user.user_metadata.name.split(" ");
    if (nameParts.length === 1) return nameParts[0].charAt(0).toUpperCase();
    
    return (
      nameParts[0].charAt(0).toUpperCase() + 
      nameParts[nameParts.length - 1].charAt(0).toUpperCase()
    );
  };

  // Links de navegação
  const navLinks = [
    {
      href: "/merchant",
      label: "Dashboard",
      icon: <Store className="h-5 w-5" />,
      isActive: pathname === "/merchant"
    },
    {
      href: "/merchant/orders",
      label: "Pedidos",
      icon: <Package className="h-5 w-5" />,
      isActive: pathname === "/merchant/orders" || activeTab === "orders"
    },
    {
      href: "/merchant/products",
      label: "Produtos",
      icon: <Package className="h-5 w-5" />,
      isActive: pathname === "/merchant/products" || activeTab === "products"
    },
    {
      href: "/merchant/sales",
      label: "Análises",
      icon: <TrendingUp className="h-5 w-5" />,
      isActive: pathname === "/merchant/sales" || activeTab === "analytics"
    }
  ];

  return (
    <header className="bg-eco text-white sticky top-0 z-10">
      <div className="container px-4 h-16 flex items-center justify-between">
        {/* Logo e título */}
        <div className="flex items-center">
          <Link to="/merchant" className="flex items-center">
            <Store className="h-6 w-6 mr-2" />
            <h1 className="text-xl font-medium">Painel do Lojista</h1>
          </Link>
        </div>

        {/* Navegação para desktop */}
        <nav className="hidden md:flex items-center space-x-6">
          {navLinks.map((link) => (
            <Link
              key={link.href}
              to={link.href}
              className={`flex items-center text-sm font-medium transition-colors hover:text-white/80 ${
                link.isActive ? "text-white" : "text-white/70"
              }`}
            >
              {link.icon}
              <span className="ml-2">{link.label}</span>
              {link.isActive && (
                <motion.div
                  className="absolute bottom-0 left-0 right-0 h-0.5 bg-white"
                  layoutId="navIndicator"
                />
              )}
            </Link>
          ))}
        </nav>

        {/* Ações e perfil */}
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="icon" className="text-white">
            <Bell className="h-5 w-5" />
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={user?.user_metadata?.avatar_url} alt={user?.user_metadata?.name || "Lojista"} />
                  <AvatarFallback>{getInitials()}</AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end" forceMount>
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">{user?.user_metadata?.name || "Lojista"}</p>
                  <p className="text-xs leading-none text-muted-foreground">{user?.email}</p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <User className="mr-2 h-4 w-4" />
                <span>Perfil</span>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <Settings className="mr-2 h-4 w-4" />
                <span>Configurações</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => signOut()}>
                <LogOut className="mr-2 h-4 w-4" />
                <span>Sair</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Menu mobile */}
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="md:hidden text-white">
                <Menu className="h-5 w-5" />
              </Button>
            </SheetTrigger>
            <SheetContent side="right">
              <div className="grid gap-4 py-4">
                <div className="flex items-center space-x-4">
                  <Avatar>
                    <AvatarImage src={user?.user_metadata?.avatar_url} />
                    <AvatarFallback>{getInitials()}</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="text-sm font-medium">{user?.user_metadata?.name || "Lojista"}</p>
                    <p className="text-xs text-muted-foreground">{user?.email}</p>
                  </div>
                </div>
                <div className="grid gap-2">
                  {navLinks.map((link) => (
                    <Link
                      key={link.href}
                      to={link.href}
                      className={`flex items-center p-2 rounded-md ${
                        link.isActive
                          ? "bg-eco text-white"
                          : "text-foreground hover:bg-muted"
                      }`}
                    >
                      {link.icon}
                      <span className="ml-2">{link.label}</span>
                    </Link>
                  ))}
                  <Button variant="ghost" className="justify-start" onClick={() => signOut()}>
                    <LogOut className="mr-2 h-4 w-4" />
                    Sair
                  </Button>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
}
