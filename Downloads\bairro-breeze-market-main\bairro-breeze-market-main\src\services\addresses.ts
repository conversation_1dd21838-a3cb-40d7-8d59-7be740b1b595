import { supabaseClient } from '@/lib/supabase';
import { getLocationByAddress } from './map';

// Address interface
export interface Address {
  id: string;
  userId: string;
  name: string;
  street: string;
  number: string;
  complement?: string;
  neighborhood: string;
  city: string;
  state: string;
  zipCode: string;
  isDefault: boolean;
  latitude?: number;
  longitude?: number;
}

// Mock addresses for development mode
let mockAddresses: Address[] = [
  {
    id: '1',
    userId: 'user1',
    name: 'Casa',
    street: 'Rua das Flores',
    number: '123',
    complement: 'Apto 101',
    neighborhood: 'Centro',
    city: 'São Paulo',
    state: 'SP',
    zipCode: '01234-567',
    isDefault: true,
    latitude: -23.5505,
    longitude: -46.6333
  },
  {
    id: '2',
    userId: 'user1',
    name: 'Trabalho',
    street: 'Av. Paulista',
    number: '1000',
    neighborhood: 'Bela Vista',
    city: 'São Paulo',
    state: 'SP',
    zipCode: '01310-100',
    isDefault: false,
    latitude: -23.5632,
    longitude: -46.6542
  }
];

/**
 * Get addresses for a user
 * @param userId User ID
 * @returns Promise with addresses
 */
export const getAddresses = async (userId: string): Promise<Address[]> => {
  try {
    // In a real app, this would fetch from a database
    
    // For now, use mock data
    return mockAddresses
      .filter(address => address.userId === userId)
      .sort((a, b) => (a.isDefault ? -1 : 1) - (b.isDefault ? -1 : 1));
  } catch (error) {
    console.error(`Error fetching addresses for user ${userId}:`, error);
    return [];
  }
};

/**
 * Get address by ID
 * @param addressId Address ID
 * @returns Promise with address
 */
export const getAddressById = async (addressId: string): Promise<Address | null> => {
  try {
    // In a real app, this would fetch from a database
    
    // For now, use mock data
    return mockAddresses.find(address => address.id === addressId) || null;
  } catch (error) {
    console.error(`Error fetching address ${addressId}:`, error);
    return null;
  }
};

/**
 * Get default address for a user
 * @param userId User ID
 * @returns Promise with default address
 */
export const getDefaultAddress = async (userId: string): Promise<Address | null> => {
  try {
    // In a real app, this would fetch from a database
    
    // For now, use mock data
    return mockAddresses.find(address => address.userId === userId && address.isDefault) || null;
  } catch (error) {
    console.error(`Error fetching default address for user ${userId}:`, error);
    return null;
  }
};

/**
 * Create an address
 * @param address Address data
 * @returns Promise with created address
 */
export const createAddress = async (
  address: Omit<Address, 'id'>
): Promise<Address | null> => {
  try {
    // In a real app, this would insert into the database
    
    // Get coordinates for the address if not provided
    let latitude = address.latitude;
    let longitude = address.longitude;
    
    if (!latitude || !longitude) {
      const fullAddress = `${address.street}, ${address.number}, ${address.neighborhood}, ${address.city}, ${address.state}, ${address.zipCode}`;
      const location = await getLocationByAddress(fullAddress);
      
      if (location) {
        latitude = location.latitude;
        longitude = location.longitude;
      }
    }
    
    // If this is the first address or marked as default, make sure it's the only default
    if (address.isDefault) {
      mockAddresses.forEach(addr => {
        if (addr.userId === address.userId) {
          addr.isDefault = false;
        }
      });
    }
    
    // If this is the first address, make it default
    const userAddresses = mockAddresses.filter(addr => addr.userId === address.userId);
    const isFirstAddress = userAddresses.length === 0;
    
    // Create new address
    const newAddress: Address = {
      id: `${mockAddresses.length + 1}`,
      ...address,
      isDefault: isFirstAddress || address.isDefault,
      latitude,
      longitude
    };
    
    mockAddresses.push(newAddress);
    
    return newAddress;
  } catch (error) {
    console.error('Error creating address:', error);
    return null;
  }
};

/**
 * Update an address
 * @param addressId Address ID
 * @param data Updated address data
 * @returns Promise with updated address
 */
export const updateAddress = async (
  addressId: string,
  data: Partial<Omit<Address, 'id' | 'userId'>>
): Promise<Address | null> => {
  try {
    // In a real app, this would update the database
    
    // Find the address
    const index = mockAddresses.findIndex(address => address.id === addressId);
    
    if (index < 0) {
      return null;
    }
    
    const address = mockAddresses[index];
    
    // Get coordinates for the address if street, number, city, or state changed
    let latitude = address.latitude;
    let longitude = address.longitude;
    
    if (
      data.street !== undefined ||
      data.number !== undefined ||
      data.city !== undefined ||
      data.state !== undefined
    ) {
      const fullAddress = `${data.street || address.street}, ${data.number || address.number}, ${data.neighborhood || address.neighborhood}, ${data.city || address.city}, ${data.state || address.state}, ${data.zipCode || address.zipCode}`;
      const location = await getLocationByAddress(fullAddress);
      
      if (location) {
        latitude = location.latitude;
        longitude = location.longitude;
      }
    }
    
    // If setting as default, make sure it's the only default
    if (data.isDefault) {
      mockAddresses.forEach(addr => {
        if (addr.userId === address.userId) {
          addr.isDefault = false;
        }
      });
    }
    
    // Update the address
    mockAddresses[index] = {
      ...address,
      ...data,
      latitude,
      longitude
    };
    
    return mockAddresses[index];
  } catch (error) {
    console.error(`Error updating address ${addressId}:`, error);
    return null;
  }
};

/**
 * Delete an address
 * @param addressId Address ID
 * @returns Promise with success status
 */
export const deleteAddress = async (addressId: string): Promise<boolean> => {
  try {
    // In a real app, this would delete from the database
    
    // Find the address
    const index = mockAddresses.findIndex(address => address.id === addressId);
    
    if (index < 0) {
      return false;
    }
    
    const address = mockAddresses[index];
    
    // Remove the address
    mockAddresses.splice(index, 1);
    
    // If it was the default address, set a new default
    if (address.isDefault) {
      const userAddresses = mockAddresses.filter(addr => addr.userId === address.userId);
      
      if (userAddresses.length > 0) {
        userAddresses[0].isDefault = true;
      }
    }
    
    return true;
  } catch (error) {
    console.error(`Error deleting address ${addressId}:`, error);
    return false;
  }
};

/**
 * Set default address
 * @param addressId Address ID
 * @returns Promise with success status
 */
export const setDefaultAddress = async (addressId: string): Promise<boolean> => {
  try {
    // In a real app, this would update the database
    
    // Find the address
    const index = mockAddresses.findIndex(address => address.id === addressId);
    
    if (index < 0) {
      return false;
    }
    
    const address = mockAddresses[index];
    
    // Set as default and unset others
    mockAddresses.forEach(addr => {
      if (addr.userId === address.userId) {
        addr.isDefault = addr.id === addressId;
      }
    });
    
    return true;
  } catch (error) {
    console.error(`Error setting default address ${addressId}:`, error);
    return false;
  }
};
