/**
 * Serviço de logging para capturar e registrar erros e eventos
 * Em uma aplicação real, isso seria integrado com um serviço como Sentry, LogRocket, etc.
 */

// Níveis de log
export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'fatal';

// Interface para um evento de log
export interface LogEvent {
  level: LogLevel;
  message: string;
  timestamp: string;
  context?: Record<string, any>;
  tags?: string[];
  user?: {
    id?: string;
    email?: string;
    name?: string;
  };
}

// Configuração do logger
interface LoggerConfig {
  minLevel: LogLevel;
  enableConsole: boolean;
  captureErrors: boolean;
  captureUnhandledRejections: boolean;
  environment: 'development' | 'production' | 'test';
}

// Configuração padrão
const defaultConfig: LoggerConfig = {
  minLevel: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  enableConsole: process.env.NODE_ENV !== 'production',
  captureErrors: true,
  captureUnhandledRejections: true,
  environment: (process.env.NODE_ENV as any) || 'development',
};

// Mapeamento de níveis de log para prioridade numérica
const LOG_LEVEL_PRIORITY: Record<LogLevel, number> = {
  debug: 0,
  info: 1,
  warn: 2,
  error: 3,
  fatal: 4,
};

// Classe principal do logger
class Logger {
  private config: LoggerConfig;
  private events: LogEvent[] = [];
  private maxEvents = 100;
  private currentUser?: LogEvent['user'];

  constructor(config: Partial<LoggerConfig> = {}) {
    this.config = { ...defaultConfig, ...config };
    
    // Configurar captura de erros não tratados
    if (this.config.captureErrors && typeof window !== 'undefined') {
      window.addEventListener('error', this.handleGlobalError);
    }
    
    // Configurar captura de rejeições não tratadas
    if (this.config.captureUnhandledRejections && typeof window !== 'undefined') {
      window.addEventListener('unhandledrejection', this.handleUnhandledRejection);
    }
  }

  // Definir usuário atual
  setUser(user: LogEvent['user']) {
    this.currentUser = user;
  }

  // Limpar usuário atual
  clearUser() {
    this.currentUser = undefined;
  }

  // Métodos de log para diferentes níveis
  debug(message: string, context?: Record<string, any>, tags?: string[]) {
    this.log('debug', message, context, tags);
  }

  info(message: string, context?: Record<string, any>, tags?: string[]) {
    this.log('info', message, context, tags);
  }

  warn(message: string, context?: Record<string, any>, tags?: string[]) {
    this.log('warn', message, context, tags);
  }

  error(message: string, error?: Error, context?: Record<string, any>, tags?: string[]) {
    const errorContext = error ? {
      ...context,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      }
    } : context;
    
    this.log('error', message, errorContext, tags);
  }

  fatal(message: string, error?: Error, context?: Record<string, any>, tags?: string[]) {
    const errorContext = error ? {
      ...context,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      }
    } : context;
    
    this.log('fatal', message, errorContext, tags);
  }

  // Método principal de log
  private log(level: LogLevel, message: string, context?: Record<string, any>, tags?: string[]) {
    // Verificar nível mínimo de log
    if (LOG_LEVEL_PRIORITY[level] < LOG_LEVEL_PRIORITY[this.config.minLevel]) {
      return;
    }
    
    const event: LogEvent = {
      level,
      message,
      timestamp: new Date().toISOString(),
      context,
      tags,
      user: this.currentUser,
    };
    
    // Adicionar evento à lista
    this.events.push(event);
    
    // Limitar o número de eventos armazenados
    if (this.events.length > this.maxEvents) {
      this.events.shift();
    }
    
    // Registrar no console se habilitado
    if (this.config.enableConsole) {
      this.logToConsole(event);
    }
    
    // Em uma aplicação real, aqui enviaríamos o evento para um serviço de logging
    this.sendToRemoteService(event);
  }

  // Registrar no console
  private logToConsole(event: LogEvent) {
    const timestamp = new Date(event.timestamp).toLocaleTimeString();
    const prefix = `[${timestamp}] [${event.level.toUpperCase()}]`;
    
    switch (event.level) {
      case 'debug':
        console.debug(prefix, event.message, event.context || '');
        break;
      case 'info':
        console.info(prefix, event.message, event.context || '');
        break;
      case 'warn':
        console.warn(prefix, event.message, event.context || '');
        break;
      case 'error':
      case 'fatal':
        console.error(prefix, event.message, event.context || '');
        break;
    }
  }

  // Enviar para serviço remoto (mock)
  private sendToRemoteService(event: LogEvent) {
    // Em uma aplicação real, aqui enviaríamos o evento para um serviço como Sentry
    if (this.config.environment === 'production' && (event.level === 'error' || event.level === 'fatal')) {
      // Simular envio para serviço remoto
      console.debug('[REMOTE LOGGING] Would send to remote service:', event);
    }
  }

  // Obter todos os eventos registrados
  getEvents() {
    return [...this.events];
  }

  // Limpar todos os eventos
  clearEvents() {
    this.events = [];
  }

  // Manipulador de erros globais
  private handleGlobalError = (event: ErrorEvent) => {
    this.error(
      `Unhandled error: ${event.message}`,
      event.error,
      {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
      },
      ['unhandled', 'global-error']
    );
  };

  // Manipulador de rejeições não tratadas
  private handleUnhandledRejection = (event: PromiseRejectionEvent) => {
    const error = event.reason instanceof Error ? event.reason : new Error(String(event.reason));
    
    this.error(
      `Unhandled promise rejection: ${error.message}`,
      error,
      {},
      ['unhandled', 'promise-rejection']
    );
  };
}

// Exportar instância singleton
export const logger = new Logger();

// Função de utilidade para registrar erros de API
export function logApiError(endpoint: string, error: any, context?: Record<string, any>) {
  const errorObj = error instanceof Error ? error : new Error(String(error));
  
  logger.error(
    `API Error: ${endpoint}`,
    errorObj,
    {
      ...context,
      endpoint,
    },
    ['api', 'api-error']
  );
}

// Função de utilidade para registrar erros de componente
export function logComponentError(componentName: string, error: any, props?: Record<string, any>) {
  const errorObj = error instanceof Error ? error : new Error(String(error));
  
  logger.error(
    `Component Error: ${componentName}`,
    errorObj,
    {
      component: componentName,
      props,
    },
    ['component', 'render-error']
  );
}

// Função de utilidade para registrar erros de formulário
export function logFormError(formName: string, error: any, formData?: Record<string, any>) {
  const errorObj = error instanceof Error ? error : new Error(String(error));
  
  logger.error(
    `Form Error: ${formName}`,
    errorObj,
    {
      form: formName,
      formData,
    },
    ['form', 'validation-error']
  );
}

// Exportar funções de utilidade
export default {
  logger,
  logApiError,
  logComponentError,
  logFormError,
};
