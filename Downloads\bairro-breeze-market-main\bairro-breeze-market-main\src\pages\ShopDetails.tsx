import { useState } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { Navbar } from "@/components/ui/navbar";
import { BottomNav } from "@/components/ui/bottom-nav";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ChevronLeft, Heart, Info, MapPin, Phone, Search, Star, Clock, Filter } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { AnimatedPage, AnimatedList, FadeIn } from "@/components/animations";
import { ProductCard } from "@/components/ui/product-card";
import { Badge } from "@/components/ui/badge";
import { fetchProducts } from "@/services/products";
import { useQuery } from "@tanstack/react-query";

// Mock shop data
const shopData = {
  "1": {
    id: "1",
    name: "Mercado do Bairro",
    image: "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9",
    coverImage: "https://images.unsplash.com/photo-1542838132-92c53300491e",
    category: "Mercado",
    deliveryTime: "15-30 min",
    rating: 4.8,
    deliveryFee: 5.0,
    address: "Rua das Flores, 123 - Centro",
    phone: "(11) 99999-9999",
    openingHours: "08:00 - 22:00",
    description: "Mercado de bairro com produtos frescos e de qualidade. Atendemos a região com entregas rápidas e preços justos.",
    featured: true,
  },
  "2": {
    id: "2",
    name: "Padaria São José",
    image: "https://images.unsplash.com/photo-1608198093002-ad4e005484ec",
    coverImage: "https://images.unsplash.com/photo-1517433670267-08bbd4be890f",
    category: "Padaria",
    deliveryTime: "10-20 min",
    rating: 4.5,
    deliveryFee: 3.5,
    address: "Av. São João, 456 - Vila Nova",
    phone: "(11) 98888-8888",
    openingHours: "06:00 - 20:00",
    description: "Padaria tradicional com pães artesanais, bolos, doces e salgados. Café da manhã completo e lanches a qualquer hora.",
    featured: false,
  },
  "3": {
    id: "3",
    name: "Açougue Premium",
    image: "https://images.unsplash.com/photo-1582562124811-c09040d0a901",
    coverImage: "https://images.unsplash.com/photo-1607623814075-e51df1bdc82f",
    category: "Açougue",
    deliveryTime: "20-35 min",
    rating: 4.7,
    deliveryFee: 6.0,
    address: "Rua dos Açougueiros, 789 - Jardim Paulista",
    phone: "(11) 97777-7777",
    openingHours: "08:00 - 18:00",
    description: "Carnes selecionadas de alta qualidade. Cortes especiais e atendimento personalizado.",
    featured: false,
  },
  "4": {
    id: "4",
    name: "Feira da Terra",
    image: "https://images.unsplash.com/photo-1506484381205-f7945653044d",
    coverImage: "https://images.unsplash.com/photo-1488459716781-31db52582fe9",
    category: "Feira",
    deliveryTime: "25-40 min",
    rating: 4.6,
    deliveryFee: 4.5,
    address: "Rua das Hortaliças, 321 - Jardim Botânico",
    phone: "(11) 96666-6666",
    openingHours: "07:00 - 14:00",
    description: "Frutas, verduras e legumes frescos direto do produtor. Produtos orgânicos e convencionais.",
    featured: true,
  },
};

// Categories for filtering
const categories = [
  "Todos",
  "Promoções",
  "Frutas",
  "Verduras",
  "Carnes",
  "Laticínios",
  "Bebidas",
  "Padaria",
  "Limpeza",
];

const ShopDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [activeCategory, setActiveCategory] = useState("Todos");
  const [isFavorite, setIsFavorite] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  // Get shop data
  const shop = id ? shopData[id as keyof typeof shopData] : null;

  // Fetch products
  const { data: allProducts, isLoading } = useQuery({
    queryKey: ["products"],
    queryFn: fetchProducts,
  });

  // Filter products by shop
  const shopProducts = allProducts?.filter(product => 
    product.shopName === shop?.name
  ) || [];

  // Filter products by category and search query
  const filteredProducts = shopProducts.filter(product => {
    const matchesCategory = activeCategory === "Todos" || 
                           (activeCategory === "Promoções" && product.isPromo) ||
                           product.category === activeCategory;
    
    const matchesSearch = searchQuery === "" || 
                         product.name.toLowerCase().includes(searchQuery.toLowerCase());
    
    return matchesCategory && matchesSearch;
  });

  if (!shop) {
    return (
      <AnimatedPage className="flex flex-col min-h-screen bg-gray-50">
        <Navbar />
        <main className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-xl font-bold mb-2">Loja não encontrada</h2>
            <p className="text-muted-foreground mb-4">A loja que você está procurando não existe.</p>
            <Button onClick={() => navigate(-1)}>Voltar</Button>
          </div>
        </main>
        <BottomNav />
      </AnimatedPage>
    );
  }

  return (
    <AnimatedPage className="flex flex-col min-h-screen bg-gray-50">
      <Navbar />

      <main className="flex-1 pb-20 md:pb-6">
        {/* Shop Cover Image */}
        <motion.div 
          className="relative h-48 md:h-64 bg-gray-200"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <motion.button
            className="absolute top-4 left-4 z-10 bg-white rounded-full p-2 shadow-sm"
            onClick={() => navigate(-1)}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <ChevronLeft className="h-5 w-5" />
          </motion.button>
          <motion.button
            className="absolute top-4 right-4 z-10 bg-white rounded-full p-2 shadow-sm"
            onClick={() => setIsFavorite(!isFavorite)}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <Heart className={`h-5 w-5 ${isFavorite ? 'fill-cta text-cta' : ''}`} />
          </motion.button>
          <img
            src={shop.coverImage}
            alt={shop.name}
            className="w-full h-full object-cover"
          />
          
          {/* Shop Logo */}
          <div className="absolute -bottom-16 left-4 w-32 h-32 rounded-xl overflow-hidden border-4 border-white bg-white shadow-md">
            <img
              src={shop.image}
              alt={shop.name}
              className="w-full h-full object-cover"
            />
          </div>
        </motion.div>

        {/* Shop Info */}
        <div className="container px-4 mt-20">
          <FadeIn delay={0.1}>
            <div className="flex justify-between items-start mb-1">
              <div>
                <h1 className="text-2xl font-bold">{shop.name}</h1>
                <p className="text-muted-foreground">{shop.category}</p>
              </div>
              <Badge className="bg-eco">{shop.rating} <Star className="ml-1 h-3 w-3 fill-white" /></Badge>
            </div>

            <div className="flex flex-wrap gap-2 mt-2 text-sm text-muted-foreground">
              <div className="flex items-center">
                <Clock className="mr-1 h-4 w-4" />
                <span>{shop.deliveryTime}</span>
              </div>
              <div className="flex items-center">
                <MapPin className="mr-1 h-4 w-4" />
                <span>{shop.address}</span>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mt-4">
              <motion.div 
                className="bg-white p-3 rounded-lg flex items-center border"
                whileHover={{ y: -2, boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)" }}
              >
                <Clock className="h-5 w-5 text-trust mr-2" />
                <div>
                  <p className="text-sm font-medium">Horário de Funcionamento</p>
                  <p className="text-xs text-muted-foreground">{shop.openingHours}</p>
                </div>
              </motion.div>
              
              <motion.div 
                className="bg-white p-3 rounded-lg flex items-center border"
                whileHover={{ y: -2, boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)" }}
              >
                <Phone className="h-5 w-5 text-eco mr-2" />
                <div>
                  <p className="text-sm font-medium">Telefone</p>
                  <p className="text-xs text-muted-foreground">{shop.phone}</p>
                </div>
              </motion.div>
              
              <motion.div 
                className="bg-white p-3 rounded-lg flex items-center border"
                whileHover={{ y: -2, boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)" }}
              >
                <Info className="h-5 w-5 text-cta mr-2" />
                <div>
                  <p className="text-sm font-medium">Taxa de Entrega</p>
                  <p className="text-xs text-muted-foreground">
                    {new Intl.NumberFormat('pt-BR', {
                      style: 'currency',
                      currency: 'BRL',
                    }).format(shop.deliveryFee)}
                  </p>
                </div>
              </motion.div>
            </div>

            <p className="mt-4 text-sm text-muted-foreground">{shop.description}</p>
          </FadeIn>

          {/* Search and Filter */}
          <div className="mt-6 mb-4">
            <div className="flex gap-3 mb-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Buscar produtos..."
                  className="pl-9"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
            </div>

            <div className="overflow-x-auto pb-2 -mx-4 px-4">
              <div className="flex space-x-2 min-w-max">
                {categories.map((category) => (
                  <motion.button
                    key={category}
                    className={`px-3 py-1.5 rounded-full text-sm whitespace-nowrap ${
                      activeCategory === category
                        ? "bg-cta text-white"
                        : "bg-white border text-muted-foreground"
                    }`}
                    onClick={() => setActiveCategory(category)}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {category}
                  </motion.button>
                ))}
              </div>
            </div>
          </div>

          {/* Products */}
          <Tabs defaultValue="products" className="mt-6">
            <TabsList className="grid w-full grid-cols-2 mb-6">
              <TabsTrigger value="products">Produtos</TabsTrigger>
              <TabsTrigger value="info">Informações</TabsTrigger>
            </TabsList>

            <TabsContent value="products">
              {isLoading ? (
                <div className="flex justify-center py-12">
                  <motion.div 
                    className="rounded-full h-12 w-12 border-t-2 border-b-2 border-cta"
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                  />
                </div>
              ) : filteredProducts.length === 0 ? (
                <div className="text-center py-12">
                  <p className="text-muted-foreground">Nenhum produto encontrado</p>
                </div>
              ) : (
                <AnimatedList className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {filteredProducts.map((product) => (
                    <ProductCard
                      key={product.id}
                      id={product.id}
                      name={product.name}
                      image={product.image}
                      price={product.price}
                      originalPrice={product.originalPrice}
                      shopName={product.shopName}
                      isPromo={product.isPromo}
                    />
                  ))}
                </AnimatedList>
              )}
            </TabsContent>

            <TabsContent value="info">
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium mb-2">Sobre {shop.name}</h3>
                  <p className="text-muted-foreground">{shop.description}</p>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-2">Endereço</h3>
                  <p className="text-muted-foreground">{shop.address}</p>
                  <div className="mt-2 h-40 bg-gray-200 rounded-lg">
                    {/* Map would go here */}
                    <div className="h-full flex items-center justify-center text-muted-foreground">
                      Mapa indisponível
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-2">Horário de Funcionamento</h3>
                  <div className="space-y-1">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Segunda a Sexta</span>
                      <span>{shop.openingHours}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Sábado</span>
                      <span>{shop.openingHours}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Domingo</span>
                      <span>Fechado</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-2">Contato</h3>
                  <div className="space-y-1">
                    <div className="flex items-center">
                      <Phone className="h-4 w-4 mr-2 text-muted-foreground" />
                      <span>{shop.phone}</span>
                    </div>
                    <div className="flex items-center">
                      <MapPin className="h-4 w-4 mr-2 text-muted-foreground" />
                      <span>{shop.address}</span>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </main>

      <BottomNav />
    </AnimatedPage>
  );
};

export default ShopDetails;
