import React, { useRef, useState, useEffect, useCallback } from 'react';
import { Loader2 } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface VirtualListProps<T> {
  items: T[];
  renderItem: (item: T, index: number) => React.ReactNode;
  itemHeight: number;
  height?: number | string;
  width?: number | string;
  overscan?: number;
  onEndReached?: () => void;
  endReachedThreshold?: number;
  loading?: boolean;
  loadingComponent?: React.ReactNode;
  emptyComponent?: React.ReactNode;
  className?: string;
  itemClassName?: string;
  keyExtractor?: (item: T, index: number) => string;
  animateItems?: boolean;
  animationVariants?: {
    hidden: any;
    visible: any;
  };
}

/**
 * Componente de lista virtualizada para renderizar grandes listas com performance
 */
export function VirtualList<T>({
  items,
  renderItem,
  itemHeight,
  height = 400,
  width = '100%',
  overscan = 3,
  onEndReached,
  endReachedThreshold = 0.8,
  loading = false,
  loadingComponent,
  emptyComponent,
  className = '',
  itemClassName = '',
  keyExtractor = (_, index) => `virtual-item-${index}`,
  animateItems = false,
  animationVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0 }
  }
}: VirtualListProps<T>) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [scrollTop, setScrollTop] = useState(0);
  const [containerHeight, setContainerHeight] = useState(0);

  // Calcular índices visíveis
  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(
    items.length - 1,
    Math.floor((scrollTop + containerHeight) / itemHeight) + overscan
  );

  // Atualizar altura do container quando montado
  useEffect(() => {
    if (containerRef.current) {
      setContainerHeight(containerRef.current.clientHeight);
    }
  }, []);

  // Manipular evento de scroll
  const handleScroll = useCallback(() => {
    if (containerRef.current) {
      setScrollTop(containerRef.current.scrollTop);

      // Verificar se chegou ao final da lista
      if (onEndReached) {
        const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
        const scrollPosition = scrollTop + clientHeight;
        const threshold = scrollHeight * endReachedThreshold;

        if (scrollPosition >= threshold && !loading) {
          onEndReached();
        }
      }
    }
  }, [onEndReached, endReachedThreshold, loading]);

  // Adicionar event listener de scroll
  useEffect(() => {
    const container = containerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      return () => {
        container.removeEventListener('scroll', handleScroll);
      };
    }
  }, [handleScroll]);

  // Renderizar lista vazia
  if (items.length === 0 && !loading) {
    return (
      <div
        ref={containerRef}
        style={{ height, width, overflow: 'auto' }}
        className={className}
      >
        {emptyComponent || (
          <div className="flex items-center justify-center h-full">
            <p className="text-muted-foreground">Nenhum item encontrado</p>
          </div>
        )}
      </div>
    );
  }

  // Calcular altura total da lista
  const totalHeight = items.length * itemHeight;

  // Renderizar itens visíveis
  const visibleItems = [];
  for (let i = startIndex; i <= endIndex; i++) {
    if (i >= items.length) break;

    const item = items[i];
    const key = keyExtractor(item, i);

    if (animateItems) {
      visibleItems.push(
        <motion.div
          key={key}
          className={itemClassName}
          initial="hidden"
          animate="visible"
          exit="hidden"
          variants={animationVariants}
          transition={{ duration: 0.2, delay: (i - startIndex) * 0.05 }}
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: itemHeight,
            transform: `translateY(${i * itemHeight}px)`,
          }}
        >
          {renderItem(item, i)}
        </motion.div>
      );
    } else {
      visibleItems.push(
        <div
          key={key}
          className={itemClassName}
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: itemHeight,
            transform: `translateY(${i * itemHeight}px)`,
          }}
        >
          {renderItem(item, i)}
        </div>
      );
    }
  }

  return (
    <div
      ref={containerRef}
      style={{ height, width, overflow: 'auto', position: 'relative' }}
      className={className}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        {animateItems ? (
          <AnimatePresence>
            {visibleItems}
          </AnimatePresence>
        ) : (
          visibleItems
        )}
      </div>

      {/* Indicador de carregamento */}
      {loading && (
        <div className="sticky bottom-0 left-0 w-full p-2 flex justify-center bg-white/80">
          {loadingComponent || (
            <div className="flex items-center space-x-2">
              <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Carregando...</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

/**
 * Componente de grade virtualizada para renderizar itens em formato de grade
 */
export function VirtualGrid<T>({
  items,
  renderItem,
  itemHeight,
  columns = 2,
  gap = 16,
  ...props
}: Omit<VirtualListProps<T>, 'itemClassName'> & {
  columns?: number;
  gap?: number;
}) {
  // Calcular altura do item na grade
  const gridItemHeight = itemHeight + gap;

  // Função para renderizar item da grade
  const renderGridItem = (item: T, index: number) => {
    const columnIndex = index % columns;
    const columnWidth = `calc((100% - ${gap * (columns - 1)}px) / ${columns})`;
    const marginLeft = columnIndex > 0 ? `${gap}px` : '0';

    return (
      <div
        style={{
          width: columnWidth,
          marginLeft,
        }}
      >
        {renderItem(item, index)}
      </div>
    );
  };

  // Função para extrair chave do item
  const gridKeyExtractor = (item: T, index: number) => {
    if (props.keyExtractor) {
      return props.keyExtractor(item, index);
    }
    return `grid-item-${index}`;
  };

  // Calcular número de linhas
  const rows = Math.ceil(items.length / columns);

  // Renderizar grade virtualizada
  return (
    <VirtualList
      {...props}
      items={Array.from({ length: rows }).map((_, rowIndex) => {
        const startIndex = rowIndex * columns;
        return items.slice(startIndex, startIndex + columns);
      })}
      renderItem={(rowItems, rowIndex) => (
        <div className="flex w-full" style={{ height: itemHeight }}>
          {rowItems.map((item, colIndex) => {
            const index = rowIndex * columns + colIndex;
            return (
              <div
                key={gridKeyExtractor(item, index)}
                className="flex-1"
                style={{
                  marginLeft: colIndex > 0 ? `${gap}px` : '0',
                }}
              >
                {renderItem(item, index)}
              </div>
            );
          })}
          {/* Preencher espaços vazios na última linha */}
          {rowItems.length < columns &&
            Array.from({ length: columns - rowItems.length }).map((_, i) => (
              <div
                key={`empty-${i}`}
                className="flex-1"
                style={{
                  marginLeft: `${gap}px`,
                }}
              />
            ))}
        </div>
      )}
      itemHeight={gridItemHeight}
      keyExtractor={(_, rowIndex) => `grid-row-${rowIndex}`}
    />
  );
}
