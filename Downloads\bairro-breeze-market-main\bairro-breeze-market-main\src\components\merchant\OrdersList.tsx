import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  Package, 
  Clock, 
  ChevronRight, 
  Loader2, 
  ShoppingBag,
  CheckCircle,
  Truck,
  AlertCircle,
  Search,
  Filter,
  X
} from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";
import { Order } from "@/types/order";
import { fetchOrdersByShopId, updateOrderStatus } from "@/services/orders";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { AnimatedList } from "@/components/animations";

export function OrdersList() {
  const { user } = useAuth();
  
  const [isLoading, setIsLoading] = useState(true);
  const [orders, setOrders] = useState<Order[]>([]);
  const [filteredOrders, setFilteredOrders] = useState<Order[]>([]);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  
  // Filters
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState("");
  
  // Load shop orders
  useEffect(() => {
    const loadOrders = async () => {
      if (!user) return;
      
      setIsLoading(true);
      
      try {
        const shopOrders = await fetchOrdersByShopId(user.id);
        setOrders(shopOrders);
        setFilteredOrders(shopOrders);
      } catch (error) {
        console.error("Error loading orders:", error);
        toast({
          title: "Erro ao carregar pedidos",
          description: "Não foi possível carregar os pedidos. Tente novamente mais tarde.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    loadOrders();
  }, [user]);
  
  // Apply filters
  useEffect(() => {
    let result = [...orders];
    
    // Apply status filter
    if (statusFilter !== "all") {
      result = result.filter(order => order.status === statusFilter);
    }
    
    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      result = result.filter(order => 
        order.id.toLowerCase().includes(query) ||
        order.userName?.toLowerCase().includes(query) ||
        order.deliveryAddress.toLowerCase().includes(query)
      );
    }
    
    setFilteredOrders(result);
  }, [orders, statusFilter, searchQuery]);
  
  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return format(date, "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });
  };
  
  // Get status badge
  const getStatusBadge = (status: Order["status"]) => {
    switch (status) {
      case "pending":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
            Pendente
          </Badge>
        );
      case "in_progress":
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800 hover:bg-blue-100">
            Em preparo
          </Badge>
        );
      case "delivered":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-100">
            Entregue
          </Badge>
        );
      case "cancelled":
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800 hover:bg-red-100">
            Cancelado
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-gray-100 text-gray-800 hover:bg-gray-100">
            Desconhecido
          </Badge>
        );
    }
  };
  
  // Get status icon
  const getStatusIcon = (status: Order["status"]) => {
    switch (status) {
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case "in_progress":
        return <Truck className="h-4 w-4 text-blue-500" />;
      case "delivered":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "cancelled":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Package className="h-4 w-4 text-gray-500" />;
    }
  };
  
  // Handle view order details
  const handleViewOrderDetails = (order: Order) => {
    setSelectedOrder(order);
    setIsDetailsOpen(true);
  };
  
  // Handle update order status
  const handleUpdateStatus = async (orderId: string, newStatus: Order["status"]) => {
    setIsProcessing(true);
    
    try {
      const updatedOrder = await updateOrderStatus(orderId, newStatus);
      
      if (updatedOrder) {
        // Update orders list
        setOrders(prevOrders => 
          prevOrders.map(order => 
            order.id === orderId ? updatedOrder : order
          )
        );
        
        // Update selected order if open
        if (selectedOrder && selectedOrder.id === orderId) {
          setSelectedOrder(updatedOrder);
        }
        
        toast({
          title: "Status atualizado",
          description: "O status do pedido foi atualizado com sucesso.",
        });
      }
    } catch (error) {
      console.error("Error updating order status:", error);
      toast({
        title: "Erro ao atualizar status",
        description: "Não foi possível atualizar o status do pedido. Tente novamente mais tarde.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };
  
  // Clear filters
  const clearFilters = () => {
    setStatusFilter("all");
    setSearchQuery("");
  };
  
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <CardTitle>Pedidos</CardTitle>
              <CardDescription>
                Gerencie os pedidos da sua loja
              </CardDescription>
            </div>
            <div className="flex flex-col sm:flex-row gap-2">
              <div className="relative">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Buscar pedidos..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-8 w-full sm:w-[200px]"
                />
                {searchQuery && (
                  <button
                    className="absolute right-2 top-1/2 transform -translate-y-1/2"
                    onClick={() => setSearchQuery("")}
                  >
                    <X className="h-4 w-4 text-muted-foreground" />
                  </button>
                )}
              </div>
              <Select
                value={statusFilter}
                onValueChange={setStatusFilter}
              >
                <SelectTrigger className="w-full sm:w-[180px]">
                  <div className="flex items-center">
                    <Filter className="mr-2 h-4 w-4" />
                    <SelectValue placeholder="Filtrar por status" />
                  </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos os pedidos</SelectItem>
                  <SelectItem value="pending">Pendentes</SelectItem>
                  <SelectItem value="in_progress">Em preparo</SelectItem>
                  <SelectItem value="delivered">Entregues</SelectItem>
                  <SelectItem value="cancelled">Cancelados</SelectItem>
                </SelectContent>
              </Select>
              {(statusFilter !== "all" || searchQuery) && (
                <Button
                  variant="outline"
                  size="icon"
                  onClick={clearFilters}
                  className="h-10 w-10"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {filteredOrders.length === 0 ? (
            <div className="text-center py-8">
              <ShoppingBag className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-1">Nenhum pedido encontrado</h3>
              <p className="text-muted-foreground mb-4">
                {searchQuery || statusFilter !== "all" 
                  ? "Tente ajustar os filtros para ver mais pedidos" 
                  : "Você ainda não recebeu nenhum pedido"}
              </p>
              {(searchQuery || statusFilter !== "all") && (
                <Button 
                  variant="outline"
                  onClick={clearFilters}
                >
                  Limpar filtros
                </Button>
              )}
            </div>
          ) : (
            <AnimatedList className="space-y-4" delay={0.1} staggerDelay={0.05}>
              {filteredOrders.map((order) => (
                <div 
                  key={order.id} 
                  className="border rounded-lg p-4 hover:border-cta transition-colors"
                >
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div>
                      <div className="flex items-center">
                        <p className="font-medium">Pedido #{order.id.substring(0, 8)}</p>
                        <div className="ml-2">
                          {getStatusBadge(order.status)}
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground flex items-center mt-1">
                        <Clock className="mr-1 h-3 w-3" />
                        {formatDate(order.createdAt)}
                      </p>
                      <div className="mt-2">
                        <p className="text-sm font-medium">{order.userName || "Cliente"}</p>
                        <p className="text-sm text-muted-foreground truncate max-w-[300px]">
                          {order.deliveryAddress}
                        </p>
                      </div>
                      <div className="mt-2 flex items-center text-sm">
                        {getStatusIcon(order.status)}
                        <span className="ml-1">
                          {order.status === "pending" && "Aguardando confirmação"}
                          {order.status === "in_progress" && "Em preparo"}
                          {order.status === "delivered" && "Entregue"}
                          {order.status === "cancelled" && "Cancelado"}
                        </span>
                      </div>
                    </div>
                    <div className="flex flex-col sm:items-end gap-2">
                      <div className="text-right">
                        <p className="text-xs text-muted-foreground">Total</p>
                        <p className="font-bold">{formatCurrency(order.total)}</p>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewOrderDetails(order)}
                        >
                          Detalhes
                          <ChevronRight className="ml-1 h-4 w-4" />
                        </Button>
                        {order.status === "pending" && (
                          <Button
                            size="sm"
                            className="bg-cta hover:bg-cta-dark"
                            onClick={() => handleUpdateStatus(order.id, "in_progress")}
                            disabled={isProcessing}
                          >
                            {isProcessing ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <>
                                <CheckCircle className="mr-1 h-4 w-4" />
                                Aceitar
                              </>
                            )}
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </AnimatedList>
          )}
        </CardContent>
      </Card>
      
      {/* Order Details Dialog */}
      <Dialog open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Detalhes do Pedido</DialogTitle>
            <DialogDescription>
              {selectedOrder && `Pedido #${selectedOrder.id.substring(0, 8)}`}
            </DialogDescription>
          </DialogHeader>
          
          {selectedOrder && (
            <div className="space-y-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm text-muted-foreground">Data do Pedido</p>
                  <p className="font-medium">{formatDate(selectedOrder.createdAt)}</p>
                </div>
                <div>
                  {getStatusBadge(selectedOrder.status)}
                </div>
              </div>
              
              <div>
                <p className="text-sm text-muted-foreground mb-1">Cliente</p>
                <p className="font-medium">{selectedOrder.userName || "Cliente"}</p>
              </div>
              
              <div>
                <p className="text-sm text-muted-foreground mb-1">Endereço de Entrega</p>
                <div className="flex items-start">
                  <MapPin className="h-4 w-4 mr-2 text-muted-foreground flex-shrink-0 mt-0.5" />
                  <p className="font-medium">{selectedOrder.deliveryAddress}</p>
                </div>
              </div>
              
              {selectedOrder.notes && (
                <div>
                  <p className="text-sm text-muted-foreground mb-1">Observações</p>
                  <p className="text-sm">{selectedOrder.notes}</p>
                </div>
              )}
              
              <div>
                <p className="text-sm text-muted-foreground mb-1">Método de Pagamento</p>
                <p className="font-medium">
                  {selectedOrder.paymentMethod === "credit_card" && "Cartão de Crédito"}
                  {selectedOrder.paymentMethod === "debit_card" && "Cartão de Débito"}
                  {selectedOrder.paymentMethod === "pix" && "PIX"}
                  {selectedOrder.paymentMethod === "cash" && "Dinheiro"}
                </p>
              </div>
              
              <div>
                <p className="text-sm text-muted-foreground mb-2">Itens do Pedido</p>
                <div className="space-y-2">
                  {selectedOrder.items.map((item, index) => (
                    <div key={index} className="flex justify-between items-center py-2 border-b">
                      <div className="flex items-center">
                        <div className="mr-3 font-medium">{item.quantity}x</div>
                        <div>{item.name}</div>
                      </div>
                      <div className="font-medium">{formatCurrency(item.price * item.quantity)}</div>
                    </div>
                  ))}
                  <div className="flex justify-between items-center pt-2">
                    <div className="font-medium">Total</div>
                    <div className="font-bold">{formatCurrency(selectedOrder.total)}</div>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          <DialogFooter className="flex flex-col sm:flex-row gap-2">
            <Button
              variant="outline"
              onClick={() => setIsDetailsOpen(false)}
              className="sm:order-1"
            >
              Fechar
            </Button>
            
            {selectedOrder && selectedOrder.status === "pending" && (
              <>
                <Button
                  variant="destructive"
                  onClick={() => handleUpdateStatus(selectedOrder.id, "cancelled")}
                  disabled={isProcessing}
                  className="sm:order-2"
                >
                  {isProcessing ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <X className="h-4 w-4 mr-2" />
                  )}
                  Recusar Pedido
                </Button>
                <Button
                  className="bg-cta hover:bg-cta-dark sm:order-3"
                  onClick={() => handleUpdateStatus(selectedOrder.id, "in_progress")}
                  disabled={isProcessing}
                >
                  {isProcessing ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : (
                    <CheckCircle className="h-4 w-4 mr-2" />
                  )}
                  Aceitar Pedido
                </Button>
              </>
            )}
            
            {selectedOrder && selectedOrder.status === "in_progress" && (
              <Button
                className="bg-cta hover:bg-cta-dark sm:order-2"
                onClick={() => handleUpdateStatus(selectedOrder.id, "delivered")}
                disabled={isProcessing}
              >
                {isProcessing ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <CheckCircle className="h-4 w-4 mr-2" />
                )}
                Marcar como Entregue
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
