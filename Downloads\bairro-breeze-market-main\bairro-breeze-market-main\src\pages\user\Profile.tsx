
import { useAuth } from '@/hooks/useAuth';
import { Navbar } from '@/components/ui/navbar';
import { BottomNav } from '@/components/ui/bottom-nav';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useState } from 'react';
import { User, Package, CreditCard, MapPin, Clock } from 'lucide-react';
import { UserProfileForm } from '@/components/user/UserProfileForm';
import { UserAddressForm } from '@/components/user/UserAddressForm';
import { UserPaymentForm } from '@/components/user/UserPaymentForm';
import { UserOrdersList } from '@/components/user/UserOrdersList';
import { AnimatedPage } from '@/components/animations';

const Profile = () => {
  const { user, signOut } = useAuth();
  const [activeTab, setActiveTab] = useState('profile');

  const handleSignOut = async () => {
    await signOut();
  };

  return (
    <AnimatedPage className="flex flex-col min-h-screen bg-gray-50">
      <Navbar />

      <main className="flex-1 container px-4 py-6 pb-20 md:pb-6">
        <div className="flex flex-col md:flex-row gap-6">
          {/* Sidebar */}
          <div className="w-full md:w-1/4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <User className="mr-2 h-5 w-5" />
                  Minha Conta
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <Tabs
                  value={activeTab}
                  onValueChange={setActiveTab}
                  orientation="vertical"
                  className="w-full"
                >
                  <TabsList className="flex flex-col items-start h-auto p-0 bg-transparent">
                    <TabsTrigger
                      value="profile"
                      className="w-full justify-start px-4 py-2 data-[state=active]:bg-muted"
                    >
                      <User className="mr-2 h-4 w-4" />
                      Perfil
                    </TabsTrigger>
                    <TabsTrigger
                      value="orders"
                      className="w-full justify-start px-4 py-2 data-[state=active]:bg-muted"
                    >
                      <Package className="mr-2 h-4 w-4" />
                      Pedidos
                    </TabsTrigger>
                    <TabsTrigger
                      value="payment"
                      className="w-full justify-start px-4 py-2 data-[state=active]:bg-muted"
                    >
                      <CreditCard className="mr-2 h-4 w-4" />
                      Pagamento
                    </TabsTrigger>
                    <TabsTrigger
                      value="addresses"
                      className="w-full justify-start px-4 py-2 data-[state=active]:bg-muted"
                    >
                      <MapPin className="mr-2 h-4 w-4" />
                      Endereços
                    </TabsTrigger>
                  </TabsList>
                </Tabs>
              </CardContent>
              <CardFooter className="border-t p-4">
                <Button
                  variant="outline"
                  className="w-full text-red-500 hover:text-red-600"
                  onClick={handleSignOut}
                >
                  Sair da conta
                </Button>
              </CardFooter>
            </Card>
          </div>

          {/* Main Content */}
          <div className="w-full md:w-3/4">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsContent value="profile" className="mt-0">
                <UserProfileForm />
              </TabsContent>

              <TabsContent value="orders" className="mt-0">
                <UserOrdersList />
              </TabsContent>

              <TabsContent value="payment" className="mt-0">
                <UserPaymentForm />
              </TabsContent>

              <TabsContent value="addresses" className="mt-0">
                <UserAddressForm />
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </main>

      <BottomNav />
    </AnimatedPage>
  );
};

export default Profile;
