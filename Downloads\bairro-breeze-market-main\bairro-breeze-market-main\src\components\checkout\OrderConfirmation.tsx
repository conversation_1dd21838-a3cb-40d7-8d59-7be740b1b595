import { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  CheckCircle, 
  MapPin, 
  ShoppingBag, 
  Clock, 
  ArrowRight, 
  Home,
  Loader2
} from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { Order } from "@/types/order";
import { fetchOrderById } from "@/services/orders";
import { DeliveryMap } from "@/components/map/DeliveryMap";
import { AnimatedPage } from "@/components/animations";

export function OrderConfirmation() {
  const { orderId } = useParams<{ orderId: string }>();
  const navigate = useNavigate();
  
  const [order, setOrder] = useState<Order | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Fetch order details
  useEffect(() => {
    const getOrderDetails = async () => {
      if (!orderId) {
        setError("ID do pedido não fornecido");
        setIsLoading(false);
        return;
      }
      
      try {
        const orderData = await fetchOrderById(orderId);
        
        if (!orderData) {
          setError("Pedido não encontrado");
          setIsLoading(false);
          return;
        }
        
        setOrder(orderData);
      } catch (error: any) {
        console.error("Error fetching order details:", error);
        setError(error.message || "Erro ao carregar detalhes do pedido");
        
        toast({
          title: "Erro ao carregar pedido",
          description: error.message || "Não foi possível carregar os detalhes do pedido.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    getOrderDetails();
  }, [orderId]);
  
  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };
  
  // Get estimated delivery time
  const getEstimatedDeliveryTime = () => {
    if (!order) return "";
    
    const orderDate = new Date(order.createdAt);
    const estimatedMinutes = 30; // Default 30 minutes
    
    const estimatedDeliveryTime = new Date(orderDate);
    estimatedDeliveryTime.setMinutes(estimatedDeliveryTime.getMinutes() + estimatedMinutes);
    
    return estimatedDeliveryTime.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
        <p className="text-muted-foreground">Carregando detalhes do pedido...</p>
      </div>
    );
  }
  
  if (error || !order) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px]">
        <div className="bg-red-100 p-3 rounded-full mb-4">
          <ShoppingBag className="h-8 w-8 text-red-500" />
        </div>
        <h2 className="text-xl font-bold mb-2">Pedido não encontrado</h2>
        <p className="text-muted-foreground mb-6">{error || "Não foi possível encontrar o pedido solicitado."}</p>
        <Button onClick={() => navigate("/")}>
          Voltar para a Página Inicial
        </Button>
      </div>
    );
  }
  
  return (
    <AnimatedPage className="max-w-3xl mx-auto">
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center bg-green-100 p-4 rounded-full mb-4">
          <CheckCircle className="h-8 w-8 text-green-500" />
        </div>
        <h1 className="text-2xl font-bold mb-2">Pedido Confirmado!</h1>
        <p className="text-muted-foreground">
          Seu pedido #{order.id} foi recebido e está sendo processado.
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <CardTitle>Detalhes do Pedido</CardTitle>
            <CardDescription>
              Informações sobre seu pedido
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="text-sm font-medium mb-2">Status</h3>
              <div className="flex items-center">
                <div className="w-3 h-3 rounded-full bg-yellow-500 mr-2" />
                <p className="text-sm">
                  {order.status === "pending" && "Pendente"}
                  {order.status === "in_progress" && "Em preparo"}
                  {order.status === "delivered" && "Entregue"}
                  {order.status === "cancelled" && "Cancelado"}
                </p>
              </div>
            </div>
            
            <div>
              <h3 className="text-sm font-medium mb-2">Estabelecimento</h3>
              <p className="text-sm">{order.shopName}</p>
            </div>
            
            <div>
              <h3 className="text-sm font-medium mb-2">Endereço de Entrega</h3>
              <div className="flex items-start">
                <MapPin className="h-4 w-4 text-muted-foreground mt-0.5 mr-2" />
                <p className="text-sm">{order.deliveryAddress}</p>
              </div>
            </div>
            
            <div>
              <h3 className="text-sm font-medium mb-2">Tempo Estimado</h3>
              <div className="flex items-center">
                <Clock className="h-4 w-4 text-muted-foreground mr-2" />
                <p className="text-sm">Entrega prevista para {getEstimatedDeliveryTime()}</p>
              </div>
            </div>
            
            {order.notes && (
              <div>
                <h3 className="text-sm font-medium mb-2">Observações</h3>
                <p className="text-sm">{order.notes}</p>
              </div>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Itens do Pedido</CardTitle>
            <CardDescription>
              Produtos incluídos no seu pedido
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {order.items.map((item, index) => (
                <div
                  key={index}
                  className="flex justify-between py-2 border-b last:border-0"
                >
                  <div>
                    <p className="font-medium">{item.name}</p>
                    <p className="text-sm text-muted-foreground">
                      {item.quantity}x {formatCurrency(item.price)}
                    </p>
                  </div>
                  <p className="font-medium">
                    {formatCurrency(item.price * item.quantity)}
                  </p>
                </div>
              ))}
            </div>
            
            <div className="border-t mt-4 pt-4">
              <div className="flex justify-between font-medium text-lg">
                <p>Total</p>
                <p>{formatCurrency(order.total)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Acompanhe sua Entrega</CardTitle>
          <CardDescription>
            Veja onde está seu pedido em tempo real
          </CardDescription>
        </CardHeader>
        <CardContent>
          <DeliveryMap 
            order={order}
            estimatedTime={30}
            className="h-[300px]"
          />
        </CardContent>
      </Card>
      
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={() => navigate("/")}
        >
          <Home className="mr-2 h-4 w-4" />
          Voltar para a Página Inicial
        </Button>
        <Button
          onClick={() => navigate("/orders")}
        >
          Ver Meus Pedidos
          <ArrowRight className="ml-2 h-4 w-4" />
        </Button>
      </div>
    </AnimatedPage>
  );
}
