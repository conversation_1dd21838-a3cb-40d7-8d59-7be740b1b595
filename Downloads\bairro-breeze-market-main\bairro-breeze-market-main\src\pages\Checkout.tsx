import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, ShoppingBag, CheckCircle2, MapPin, CreditCard as CreditCardIcon, CheckCircle } from "lucide-react";
import { useCart } from "@/hooks/useCart";
import { toast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";
import { motion } from "framer-motion";
import { AnimatedPage, AnimatedButton, FadeIn } from "@/components/animations";
import { AddressForm } from "@/components/forms/AddressForm";
import { PaymentForm } from "@/components/forms/PaymentForm";
import { OrderSummary } from "@/components/forms/OrderSummary";
import { formatCurrency } from "@/lib/validations";

// Tipos de etapas do checkout
type CheckoutStep = 'address' | 'payment' | 'summary' | 'confirmation';

const Checkout = () => {
  // Estado para controlar a etapa atual do checkout
  const [step, setStep] = useState<CheckoutStep>('address');

  // Estado para armazenar os dados do checkout
  const [deliveryAddress, setDeliveryAddress] = useState("");
  const [deliveryInstructions, setDeliveryInstructions] = useState("");
  const [paymentMethodId, setPaymentMethodId] = useState("");

  // Hooks
  const navigate = useNavigate();
  const { items, subtotal } = useCart();
  const { user } = useAuth();

  // Manipuladores de eventos
  const handleAddressSelect = (address: string) => {
    setDeliveryAddress(address);
  };

  const handleInstructionsChange = (instructions: string) => {
    setDeliveryInstructions(instructions);
  };

  const handlePaymentMethodSelect = (methodId: string) => {
    setPaymentMethodId(methodId);
  };

  // Manipuladores de conclusão de etapa
  const handleAddressComplete = () => {
    if (!deliveryAddress) {
      toast({
        title: "Endereço necessário",
        description: "Por favor, selecione ou adicione um endereço de entrega.",
        variant: "destructive"
      });
      return;
    }

    setStep('payment');
  };

  const handlePaymentComplete = () => {
    if (!paymentMethodId) {
      toast({
        title: "Método de pagamento necessário",
        description: "Por favor, selecione um método de pagamento.",
        variant: "destructive"
      });
      return;
    }

    setStep('summary');
  };

  const handleOrderComplete = () => {
    setStep('confirmation');
  };

  if (items.length === 0 && step !== 'confirmation') {
    return (
      <AnimatedPage className="min-h-screen bg-gray-50 flex flex-col">
        <header className="bg-eco text-white sticky top-0 z-10">
          <div className="container px-4 h-16 flex items-center">
            <motion.button
              className="mr-4"
              onClick={() => navigate(-1)}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <ChevronLeft className="h-6 w-6" />
            </motion.button>
            <h1 className="text-xl font-medium">Finalizar Compra</h1>
          </div>
        </header>

        <main className="flex-1 container px-4 py-6 max-w-3xl mx-auto flex flex-col items-center justify-center">
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <ShoppingBag className="h-16 w-16 text-muted-foreground mb-4" />
          </motion.div>
          <motion.h2
            className="text-xl font-medium mb-2"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.1 }}
          >
            Seu carrinho está vazio
          </motion.h2>
          <motion.p
            className="text-muted-foreground mb-6"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            Adicione produtos ao carrinho para continuar
          </motion.p>
          <AnimatedButton
            className="bg-cta hover:bg-cta-dark text-white"
            onClick={() => navigate('/')}
            whileHoverScale={1.05}
            whileTapScale={0.95}
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            Explorar produtos
          </AnimatedButton>
        </main>
      </AnimatedPage>
    );
  }

  if (step === 'confirmation') {
    return (
      <AnimatedPage className="min-h-screen bg-gray-50 flex flex-col">
        <header className="bg-eco text-white sticky top-0 z-10">
          <div className="container px-4 h-16 flex items-center">
            <h1 className="text-xl font-medium">Pedido Confirmado</h1>
          </div>
        </header>

        <main className="flex-1 container px-4 py-6 max-w-3xl mx-auto flex flex-col items-center justify-center">
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="bg-eco-light p-6 rounded-full"
          >
            <CheckCircle className="h-16 w-16 text-eco" />
          </motion.div>
          <motion.h2
            className="text-2xl font-medium mt-6 mb-2"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.1 }}
          >
            Pedido realizado com sucesso!
          </motion.h2>
          <motion.p
            className="text-muted-foreground mb-6 text-center"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            Seu pedido foi recebido e está sendo processado.<br />
            Você receberá atualizações sobre o status do seu pedido.
          </motion.p>
          <div className="flex flex-col sm:flex-row gap-3 w-full max-w-md">
            <AnimatedButton
              className="flex-1 bg-cta hover:bg-cta-dark text-white"
              onClick={() => navigate('/orders')}
              whileHoverScale={1.05}
              whileTapScale={0.95}
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              Ver meus pedidos
            </AnimatedButton>
            <AnimatedButton
              className="flex-1"
              variant="outline"
              onClick={() => navigate('/')}
              whileHoverScale={1.05}
              whileTapScale={0.95}
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.4 }}
            >
              Continuar comprando
            </AnimatedButton>
          </div>
        </main>
      </AnimatedPage>
    );
  }

  return (
    <AnimatedPage className="min-h-screen bg-gray-50 flex flex-col">
      <header className="bg-eco text-white sticky top-0 z-10">
        <div className="container px-4 h-16 flex items-center">
          <motion.button
            className="mr-4"
            onClick={() => navigate(-1)}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <ChevronLeft className="h-6 w-6" />
          </motion.button>
          <h1 className="text-xl font-medium">Finalizar Compra</h1>
        </div>
      </header>

      <main className="flex-1 container px-4 py-6 max-w-3xl mx-auto">
        <div className="space-y-8">
          {/* Checkout Progress */}
          <div className="flex justify-between items-center mb-6">
            <div className="flex-1 flex flex-col items-center">
              <div className={`w-8 h-8 rounded-full ${step === 'address' ? 'bg-eco' : 'bg-eco-dark'} text-white flex items-center justify-center mb-1`}>
                <MapPin className="h-4 w-4" />
              </div>
              <span className={`text-xs ${step === 'address' ? 'text-eco' : 'text-eco-dark'} font-medium`}>Endereço</span>
            </div>
            <div className="flex-1 h-1 bg-gray-200 mx-2">
              <div className={`h-full bg-eco ${step === 'address' ? 'w-0' : step === 'payment' ? 'w-1/2' : 'w-full'}`}></div>
            </div>
            <div className="flex-1 flex flex-col items-center">
              <div className={`w-8 h-8 rounded-full ${step === 'payment' ? 'bg-eco' : step === 'address' ? 'bg-gray-200 text-muted-foreground' : 'bg-eco-dark text-white'} flex items-center justify-center mb-1`}>
                <CreditCardIcon className="h-4 w-4" />
              </div>
              <span className={`text-xs ${step === 'payment' ? 'text-eco' : step === 'address' ? 'text-muted-foreground' : 'text-eco-dark'} font-medium`}>Pagamento</span>
            </div>
            <div className="flex-1 h-1 bg-gray-200 mx-2">
              <div className={`h-full bg-eco ${step === 'address' || step === 'payment' ? 'w-0' : step === 'summary' ? 'w-1/2' : 'w-full'}`}></div>
            </div>
            <div className="flex-1 flex flex-col items-center">
              <div className={`w-8 h-8 rounded-full ${step === 'summary' ? 'bg-eco' : step === 'confirmation' ? 'bg-eco-dark text-white' : 'bg-gray-200 text-muted-foreground'} flex items-center justify-center mb-1`}>
                <CheckCircle2 className="h-4 w-4" />
              </div>
              <span className={`text-xs ${step === 'summary' ? 'text-eco' : step === 'confirmation' ? 'text-eco-dark' : 'text-muted-foreground'} font-medium`}>Confirmação</span>
            </div>
          </div>

          {/* Step Content */}
          <FadeIn>
            <motion.section
              className="bg-white p-6 rounded-lg border"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              {step === 'address' && (
                <>
                  <div className="flex items-center mb-6">
                    <div className="bg-eco-light p-2 rounded-full mr-3">
                      <MapPin className="h-5 w-5 text-eco" />
                    </div>
                    <h2 className="text-lg font-medium">Endereço de Entrega</h2>
                  </div>
                  <AddressForm
                    onAddressSelect={handleAddressSelect}
                    onComplete={handleAddressComplete}
                  />
                </>
              )}

              {step === 'payment' && (
                <>
                  <div className="flex items-center mb-6">
                    <div className="bg-eco-light p-2 rounded-full mr-3">
                      <CreditCardIcon className="h-5 w-5 text-eco" />
                    </div>
                    <h2 className="text-lg font-medium">Forma de Pagamento</h2>
                  </div>
                  <PaymentForm
                    onPaymentMethodSelect={handlePaymentMethodSelect}
                    onComplete={handlePaymentComplete}
                  />
                </>
              )}

              {step === 'summary' && (
                <>
                  <div className="flex items-center mb-6">
                    <div className="bg-eco-light p-2 rounded-full mr-3">
                      <CheckCircle2 className="h-5 w-5 text-eco" />
                    </div>
                    <h2 className="text-lg font-medium">Resumo do Pedido</h2>
                  </div>
                  <OrderSummary
                    deliveryAddress={deliveryAddress}
                    deliveryInstructions={deliveryInstructions}
                    paymentMethodId={paymentMethodId}
                    onComplete={handleOrderComplete}
                  />
                </>
              )}
            </motion.section>
          </FadeIn>

          {/* Navigation Buttons */}
          {(step === 'payment' || step === 'summary') && (
            <FadeIn delay={0.3}>
              <Button
                variant="outline"
                onClick={() => setStep(step === 'payment' ? 'address' : 'payment')}
                className="w-full"
              >
                Voltar para {step === 'payment' ? 'Endereço' : 'Pagamento'}
              </Button>
            </FadeIn>
          )}
        </div>
      </main>
    </AnimatedPage>
  );
};

export default Checkout;
