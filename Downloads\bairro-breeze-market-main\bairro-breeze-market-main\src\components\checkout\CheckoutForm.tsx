import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  CreditCard, 
  Banknote, 
  QrCode, 
  MapPin, 
  ShoppingBag, 
  Clock, 
  ChevronRight,
  Loader2
} from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { useCart } from "@/hooks/useCart";
import { useAuth } from "@/hooks/useAuth";
import { PaymentMethod, processCheckout, getSavedPaymentMethods, calculateDeliveryFee } from "@/services/checkout";
import { AnimatePresence, motion } from "framer-motion";

export function CheckoutForm() {
  const { cart, clearCart, totalPrice } = useCart();
  const { user } = useAuth();
  const navigate = useNavigate();
  
  const [step, setStep] = useState<"address" | "payment" | "review">("address");
  const [isLoading, setIsLoading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [deliveryAddress, setDeliveryAddress] = useState(user?.user_metadata?.address || "");
  const [notes, setNotes] = useState("");
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>("");
  const [deliveryFee, setDeliveryFee] = useState(0);
  
  // Load payment methods
  useState(() => {
    const loadPaymentMethods = async () => {
      setIsLoading(true);
      try {
        const methods = await getSavedPaymentMethods();
        setPaymentMethods(methods);
        if (methods.length > 0) {
          setSelectedPaymentMethod(methods[0].id);
        }
        
        // Calculate delivery fee
        if (cart.length > 0) {
          const fee = await calculateDeliveryFee(cart, deliveryAddress);
          setDeliveryFee(fee);
        }
      } catch (error) {
        console.error("Error loading payment methods:", error);
        toast({
          title: "Erro ao carregar métodos de pagamento",
          description: "Não foi possível carregar seus métodos de pagamento salvos.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    loadPaymentMethods();
  }, []);
  
  // Handle address form submission
  const handleAddressSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!deliveryAddress.trim()) {
      toast({
        title: "Endereço obrigatório",
        description: "Por favor, informe o endereço de entrega.",
        variant: "destructive",
      });
      return;
    }
    
    setStep("payment");
  };
  
  // Handle payment form submission
  const handlePaymentSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedPaymentMethod) {
      toast({
        title: "Método de pagamento obrigatório",
        description: "Por favor, selecione um método de pagamento.",
        variant: "destructive",
      });
      return;
    }
    
    setStep("review");
  };
  
  // Handle checkout submission
  const handleCheckout = async () => {
    if (cart.length === 0) {
      toast({
        title: "Carrinho vazio",
        description: "Adicione produtos ao carrinho para finalizar a compra.",
        variant: "destructive",
      });
      return;
    }
    
    if (!deliveryAddress.trim()) {
      toast({
        title: "Endereço obrigatório",
        description: "Por favor, informe o endereço de entrega.",
        variant: "destructive",
      });
      setStep("address");
      return;
    }
    
    if (!selectedPaymentMethod) {
      toast({
        title: "Método de pagamento obrigatório",
        description: "Por favor, selecione um método de pagamento.",
        variant: "destructive",
      });
      setStep("payment");
      return;
    }
    
    setIsProcessing(true);
    
    try {
      const paymentMethod = paymentMethods.find(pm => pm.id === selectedPaymentMethod);
      
      if (!paymentMethod) {
        throw new Error("Método de pagamento não encontrado");
      }
      
      const result = await processCheckout({
        items: cart,
        total: totalPrice + deliveryFee,
        deliveryAddress,
        paymentMethod,
        notes,
      });
      
      if (result.success && result.order) {
        // Clear cart
        clearCart();
        
        // Show success message
        toast({
          title: "Pedido realizado com sucesso!",
          description: `Seu pedido #${result.order.id} foi recebido e está sendo processado.`,
        });
        
        // Navigate to order confirmation page
        navigate(`/orders/${result.order.id}`);
      } else {
        throw new Error(result.error || "Erro ao processar o pagamento");
      }
    } catch (error: any) {
      console.error("Error processing checkout:", error);
      toast({
        title: "Erro ao finalizar compra",
        description: error.message || "Ocorreu um erro ao processar seu pedido. Por favor, tente novamente.",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };
  
  // Get payment method icon
  const getPaymentMethodIcon = (type: PaymentMethod["type"]) => {
    switch (type) {
      case "credit_card":
      case "debit_card":
        return <CreditCard className="h-4 w-4" />;
      case "pix":
        return <QrCode className="h-4 w-4" />;
      case "cash":
        return <Banknote className="h-4 w-4" />;
      default:
        return <CreditCard className="h-4 w-4" />;
    }
  };
  
  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };
  
  return (
    <div className="max-w-3xl mx-auto">
      {/* Checkout Steps */}
      <div className="flex justify-between mb-6">
        <div 
          className={`flex flex-col items-center ${step === "address" ? "text-primary" : "text-muted-foreground"}`}
          onClick={() => setStep("address")}
        >
          <div className={`w-8 h-8 rounded-full flex items-center justify-center mb-1 ${step === "address" ? "bg-primary text-primary-foreground" : "bg-muted"}`}>
            <MapPin className="h-4 w-4" />
          </div>
          <span className="text-xs">Endereço</span>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className={`h-0.5 w-full ${step !== "address" ? "bg-primary" : "bg-muted"}`} />
        </div>
        <div 
          className={`flex flex-col items-center ${step === "payment" ? "text-primary" : "text-muted-foreground"}`}
          onClick={() => step !== "address" && setStep("payment")}
        >
          <div className={`w-8 h-8 rounded-full flex items-center justify-center mb-1 ${step === "payment" ? "bg-primary text-primary-foreground" : "bg-muted"}`}>
            <CreditCard className="h-4 w-4" />
          </div>
          <span className="text-xs">Pagamento</span>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className={`h-0.5 w-full ${step === "review" ? "bg-primary" : "bg-muted"}`} />
        </div>
        <div 
          className={`flex flex-col items-center ${step === "review" ? "text-primary" : "text-muted-foreground"}`}
          onClick={() => step === "review" && setStep("review")}
        >
          <div className={`w-8 h-8 rounded-full flex items-center justify-center mb-1 ${step === "review" ? "bg-primary text-primary-foreground" : "bg-muted"}`}>
            <ShoppingBag className="h-4 w-4" />
          </div>
          <span className="text-xs">Revisão</span>
        </div>
      </div>
      
      {/* Checkout Forms */}
      <AnimatePresence mode="wait">
        {step === "address" && (
          <motion.div
            key="address"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.2 }}
          >
            <Card>
              <CardHeader>
                <CardTitle>Endereço de Entrega</CardTitle>
                <CardDescription>
                  Informe o endereço onde deseja receber seu pedido
                </CardDescription>
              </CardHeader>
              <form onSubmit={handleAddressSubmit}>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="address">Endereço completo</Label>
                    <Textarea
                      id="address"
                      placeholder="Rua, número, complemento, bairro, cidade, estado, CEP"
                      value={deliveryAddress}
                      onChange={(e) => setDeliveryAddress(e.target.value)}
                      className="min-h-[100px]"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="notes">Observações (opcional)</Label>
                    <Textarea
                      id="notes"
                      placeholder="Instruções para entrega, referências, etc."
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                    />
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate("/cart")}
                  >
                    Voltar ao Carrinho
                  </Button>
                  <Button type="submit">
                    Continuar
                    <ChevronRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardFooter>
              </form>
            </Card>
          </motion.div>
        )}
        
        {step === "payment" && (
          <motion.div
            key="payment"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.2 }}
          >
            <Card>
              <CardHeader>
                <CardTitle>Método de Pagamento</CardTitle>
                <CardDescription>
                  Selecione como deseja pagar pelo seu pedido
                </CardDescription>
              </CardHeader>
              <form onSubmit={handlePaymentSubmit}>
                <CardContent className="space-y-4">
                  {isLoading ? (
                    <div className="flex justify-center py-8">
                      <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                    </div>
                  ) : paymentMethods.length === 0 ? (
                    <div className="text-center py-8">
                      <p className="text-muted-foreground">
                        Nenhum método de pagamento encontrado
                      </p>
                    </div>
                  ) : (
                    <RadioGroup
                      value={selectedPaymentMethod}
                      onValueChange={setSelectedPaymentMethod}
                      className="space-y-2"
                    >
                      {paymentMethods.map((method) => (
                        <div
                          key={method.id}
                          className="flex items-center space-x-2 border rounded-md p-3"
                        >
                          <RadioGroupItem value={method.id} id={method.id} />
                          <Label
                            htmlFor={method.id}
                            className="flex-1 flex items-center cursor-pointer"
                          >
                            <div className="flex items-center">
                              <div className="mr-2">
                                {getPaymentMethodIcon(method.type)}
                              </div>
                              <div>
                                <p className="font-medium">{method.name}</p>
                                {method.last4 && (
                                  <p className="text-xs text-muted-foreground">
                                    **** **** **** {method.last4}
                                    {method.expiryDate && ` • ${method.expiryDate}`}
                                  </p>
                                )}
                              </div>
                            </div>
                          </Label>
                        </div>
                      ))}
                    </RadioGroup>
                  )}
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setStep("address")}
                  >
                    Voltar
                  </Button>
                  <Button type="submit" disabled={!selectedPaymentMethod}>
                    Continuar
                    <ChevronRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardFooter>
              </form>
            </Card>
          </motion.div>
        )}
        
        {step === "review" && (
          <motion.div
            key="review"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.2 }}
          >
            <Card>
              <CardHeader>
                <CardTitle>Revisão do Pedido</CardTitle>
                <CardDescription>
                  Confira os detalhes do seu pedido antes de finalizar
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <h3 className="text-sm font-medium mb-2">Itens do Pedido</h3>
                  <div className="space-y-2">
                    {cart.map((item) => (
                      <div
                        key={`${item.productId}-${item.shopName}`}
                        className="flex justify-between py-2 border-b"
                      >
                        <div>
                          <p className="font-medium">{item.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {item.shopName} • {item.quantity}x
                          </p>
                        </div>
                        <p className="font-medium">
                          {formatCurrency(item.price * item.quantity)}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h3 className="text-sm font-medium mb-2">Endereço de Entrega</h3>
                  <div className="flex items-start">
                    <MapPin className="h-4 w-4 text-muted-foreground mt-0.5 mr-2" />
                    <p className="text-sm">{deliveryAddress}</p>
                  </div>
                  {notes && (
                    <div className="flex items-start mt-2">
                      <p className="text-xs text-muted-foreground ml-6">
                        Observações: {notes}
                      </p>
                    </div>
                  )}
                </div>
                
                <div>
                  <h3 className="text-sm font-medium mb-2">Método de Pagamento</h3>
                  {selectedPaymentMethod && (
                    <div className="flex items-center">
                      {getPaymentMethodIcon(
                        paymentMethods.find(
                          (pm) => pm.id === selectedPaymentMethod
                        )?.type || "credit_card"
                      )}
                      <p className="text-sm ml-2">
                        {paymentMethods.find(
                          (pm) => pm.id === selectedPaymentMethod
                        )?.name || ""}
                        {paymentMethods.find(
                          (pm) => pm.id === selectedPaymentMethod
                        )?.last4 && (
                          <span className="text-muted-foreground">
                            {" "}
                            **** {
                              paymentMethods.find(
                                (pm) => pm.id === selectedPaymentMethod
                              )?.last4
                            }
                          </span>
                        )}
                      </p>
                    </div>
                  )}
                </div>
                
                <div>
                  <h3 className="text-sm font-medium mb-2">Tempo Estimado</h3>
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 text-muted-foreground mr-2" />
                    <p className="text-sm">30-45 minutos</p>
                  </div>
                </div>
                
                <div className="border-t pt-4">
                  <div className="flex justify-between mb-2">
                    <p className="text-sm">Subtotal</p>
                    <p className="text-sm font-medium">{formatCurrency(totalPrice)}</p>
                  </div>
                  <div className="flex justify-between mb-2">
                    <p className="text-sm">Taxa de entrega</p>
                    <p className="text-sm font-medium">{formatCurrency(deliveryFee)}</p>
                  </div>
                  <div className="flex justify-between font-medium text-lg">
                    <p>Total</p>
                    <p>{formatCurrency(totalPrice + deliveryFee)}</p>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setStep("payment")}
                >
                  Voltar
                </Button>
                <Button
                  onClick={handleCheckout}
                  disabled={isProcessing}
                  className="bg-cta hover:bg-cta-dark"
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processando...
                    </>
                  ) : (
                    <>
                      Finalizar Compra
                      <ShoppingBag className="ml-2 h-4 w-4" />
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
