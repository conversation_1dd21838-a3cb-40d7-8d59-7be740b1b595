
import { useLocation } from "react-router-dom";
import { useEffect } from "react";
import { Button } from "@/components/ui/button";
import { ShoppingBag } from "lucide-react";

const NotFound = () => {
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 px-4">
      <div className="text-center max-w-md">
        <div className="bg-eco-light p-6 rounded-full inline-flex mb-6">
          <ShoppingBag className="h-12 w-12 text-eco" />
        </div>
        <h1 className="text-4xl font-bold mb-4 text-gray-900">Página não encontrada</h1>
        <p className="text-lg text-gray-600 mb-8">
          <PERSON><PERSON><PERSON><PERSON>, não conseguimos encontrar a página que você está procurando.
        </p>
        <Button 
          size="lg" 
          className="bg-cta hover:bg-cta-dark text-white"
          onClick={() => window.location.href = "/"}
        >
          Voltar para a página inicial
        </Button>
      </div>
    </div>
  );
};

export default NotFound;
