import { supabaseClient } from '@/lib/supabase';

// Review interface
export interface Review {
  id: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  type: 'product' | 'shop' | 'deliverer';
  itemId: string;
  rating: number;
  comment?: string;
  createdAt: string;
}

// Mock reviews for development mode
let mockReviews: Review[] = [
  {
    id: '1',
    userId: 'user1',
    userName: '<PERSON>',
    type: 'product',
    itemId: '1',
    rating: 4.5,
    comment: 'Produto excelente, chegou rápido e bem embalado.',
    createdAt: '2024-04-18T10:00:00Z'
  },
  {
    id: '2',
    userId: 'user2',
    userName: '<PERSON>',
    type: 'shop',
    itemId: '2',
    rating: 5,
    comment: 'Ótima padaria, produtos sempre frescos.',
    createdAt: '2024-04-18T11:00:00Z'
  },
  {
    id: '3',
    userId: 'user3',
    userName: '<PERSON>',
    type: 'deliverer',
    itemId: 'deliverer1',
    rating: 4,
    comment: 'Entrega rápida e entregador educado.',
    createdAt: '2024-04-18T12:00:00Z'
  }
];

/**
 * Get reviews for an item
 * @param itemId Item ID
 * @param type Item type
 * @returns Promise with reviews
 */
export const getReviews = async (
  itemId: string,
  type: Review['type']
): Promise<Review[]> => {
  try {
    // In a real app, this would fetch from a database
    
    // For now, use mock data
    return mockReviews
      .filter(review => review.itemId === itemId && review.type === type)
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  } catch (error) {
    console.error(`Error fetching reviews for ${type} ${itemId}:`, error);
    return [];
  }
};

/**
 * Get average rating for an item
 * @param itemId Item ID
 * @param type Item type
 * @returns Promise with average rating
 */
export const getAverageRating = async (
  itemId: string,
  type: Review['type']
): Promise<number> => {
  try {
    // In a real app, this would fetch from a database
    
    // For now, use mock data
    const reviews = await getReviews(itemId, type);
    
    if (reviews.length === 0) {
      return 0;
    }
    
    const sum = reviews.reduce((total, review) => total + review.rating, 0);
    return sum / reviews.length;
  } catch (error) {
    console.error(`Error fetching average rating for ${type} ${itemId}:`, error);
    return 0;
  }
};

/**
 * Get reviews by a user
 * @param userId User ID
 * @returns Promise with reviews
 */
export const getUserReviews = async (userId: string): Promise<Review[]> => {
  try {
    // In a real app, this would fetch from a database
    
    // For now, use mock data
    return mockReviews
      .filter(review => review.userId === userId)
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  } catch (error) {
    console.error(`Error fetching reviews for user ${userId}:`, error);
    return [];
  }
};

/**
 * Create a review
 * @param review Review data
 * @returns Promise with created review
 */
export const createReview = async (
  review: Omit<Review, 'id' | 'createdAt'>
): Promise<Review | null> => {
  try {
    // In a real app, this would insert into the database
    
    // For now, update mock data
    const newReview: Review = {
      id: `${mockReviews.length + 1}`,
      ...review,
      createdAt: new Date().toISOString()
    };
    
    mockReviews.push(newReview);
    
    return newReview;
  } catch (error) {
    console.error('Error creating review:', error);
    return null;
  }
};

/**
 * Update a review
 * @param reviewId Review ID
 * @param data Updated review data
 * @returns Promise with updated review
 */
export const updateReview = async (
  reviewId: string,
  data: Pick<Review, 'rating' | 'comment'>
): Promise<Review | null> => {
  try {
    // In a real app, this would update the database
    
    // For now, update mock data
    const index = mockReviews.findIndex(review => review.id === reviewId);
    
    if (index >= 0) {
      mockReviews[index] = {
        ...mockReviews[index],
        ...data
      };
      
      return mockReviews[index];
    }
    
    return null;
  } catch (error) {
    console.error(`Error updating review ${reviewId}:`, error);
    return null;
  }
};

/**
 * Delete a review
 * @param reviewId Review ID
 * @returns Promise with success status
 */
export const deleteReview = async (reviewId: string): Promise<boolean> => {
  try {
    // In a real app, this would delete from the database
    
    // For now, update mock data
    const index = mockReviews.findIndex(review => review.id === reviewId);
    
    if (index >= 0) {
      mockReviews.splice(index, 1);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error deleting review ${reviewId}:`, error);
    return false;
  }
};

/**
 * Check if a user has reviewed an item
 * @param userId User ID
 * @param itemId Item ID
 * @param type Item type
 * @returns Promise with review or null
 */
export const getUserReviewForItem = async (
  userId: string,
  itemId: string,
  type: Review['type']
): Promise<Review | null> => {
  try {
    // In a real app, this would fetch from a database
    
    // For now, use mock data
    const review = mockReviews.find(
      review => review.userId === userId && review.itemId === itemId && review.type === type
    );
    
    return review || null;
  } catch (error) {
    console.error(`Error checking if user ${userId} has reviewed ${type} ${itemId}:`, error);
    return null;
  }
};
