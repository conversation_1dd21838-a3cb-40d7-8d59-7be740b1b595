import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/hooks/useAuth";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Truck, MapPin, TrendingUp, Clock, DollarSign, LogOut, Navigation, CheckCircle2 } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { AnimatedPage } from "@/components/animations";
import { LoadingState } from "@/components/ui/async-state";
import { DeliveryMap } from "@/components/maps/DeliveryMap";

const DelivererDashboard = () => {
  const { user, signOut } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [isOnline, setIsOnline] = useState(false);
  const [currentDelivery, setCurrentDelivery] = useState<any>(null);

  // Verificar se o usuário está autenticado e é um entregador
  useEffect(() => {
    const checkAuth = async () => {
      if (!user) {
        navigate("/login");
        return;
      }

      // Verificar se o usuário é um entregador
      if (user.user_metadata?.role !== "deliverer") {
        toast({
          title: "Acesso negado",
          description: "Você não tem permissão para acessar esta página.",
          variant: "destructive",
        });
        navigate("/");
        return;
      }

      // Simular carregamento de dados
      setTimeout(() => {
        setLoading(false);
      }, 1000);
    };

    checkAuth();
  }, [user, navigate]);

  // Alternar status online/offline
  const toggleOnlineStatus = () => {
    setIsOnline(!isOnline);
    
    toast({
      title: !isOnline ? "Você está online" : "Você está offline",
      description: !isOnline 
        ? "Agora você pode receber solicitações de entrega." 
        : "Você não receberá novas solicitações de entrega.",
    });
  };

  // Aceitar entrega
  const acceptDelivery = () => {
    setCurrentDelivery({
      id: "del_123456",
      orderId: "order_123456",
      customerName: "Maria Oliveira",
      customerAddress: "Rua Augusta, 1500, São Paulo, SP",
      shopName: "Hamburgueria Artesanal",
      shopAddress: "Av. Paulista, 1000, São Paulo, SP",
      items: 3,
      total: 78.50,
      distance: 2.5,
      estimatedTime: 25,
    });
    
    toast({
      title: "Entrega aceita",
      description: "Você aceitou uma nova entrega. Dirija-se ao estabelecimento para retirar o pedido.",
    });
  };

  // Completar entrega
  const completeDelivery = () => {
    setCurrentDelivery(null);
    
    toast({
      title: "Entrega concluída",
      description: "Parabéns! Você concluiu a entrega com sucesso.",
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingState message="Carregando painel do entregador..." />
      </div>
    );
  }

  return (
    <AnimatedPage className="min-h-screen bg-gray-50">
      <header className="bg-eco text-white sticky top-0 z-10">
        <div className="container px-4 h-16 flex items-center justify-between">
          <div className="flex items-center">
            <Truck className="h-6 w-6 mr-2" />
            <h1 className="text-xl font-medium">Painel do Entregador</h1>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Switch 
                id="online-mode" 
                checked={isOnline} 
                onCheckedChange={toggleOnlineStatus} 
              />
              <Label htmlFor="online-mode" className="text-white">
                {isOnline ? "Online" : "Offline"}
              </Label>
            </div>
            <Button variant="ghost" onClick={signOut} className="text-white">
              <LogOut className="h-5 w-5 mr-2" />
              Sair
            </Button>
          </div>
        </div>
      </header>

      <main className="container px-4 py-6 max-w-6xl mx-auto">
        {/* Resumo */}
        <section className="mb-8">
          <h2 className="text-2xl font-bold mb-4">Bem-vindo, {user?.user_metadata?.name || "Entregador"}</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Entregas Hoje</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <Truck className="h-5 w-5 text-eco mr-2" />
                  <span className="text-2xl font-bold">8</span>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Ganhos Hoje</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <DollarSign className="h-5 w-5 text-eco mr-2" />
                  <span className="text-2xl font-bold">R$ 120,00</span>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Tempo Médio</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <Clock className="h-5 w-5 text-eco mr-2" />
                  <span className="text-2xl font-bold">22 min</span>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">Avaliação</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center">
                  <CheckCircle2 className="h-5 w-5 text-eco mr-2" />
                  <span className="text-2xl font-bold">4.9</span>
                  <span className="text-sm text-muted-foreground ml-2">(45 avaliações)</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Entrega atual ou disponíveis */}
        {currentDelivery ? (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Entrega em Andamento</CardTitle>
              <CardDescription>Detalhes da entrega atual</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Pedido</h3>
                    <p className="font-medium">#{currentDelivery.orderId}</p>
                  </div>
                  
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Cliente</h3>
                    <p className="font-medium">{currentDelivery.customerName}</p>
                    <p className="text-sm text-muted-foreground">{currentDelivery.customerAddress}</p>
                  </div>
                  
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground">Estabelecimento</h3>
                    <p className="font-medium">{currentDelivery.shopName}</p>
                    <p className="text-sm text-muted-foreground">{currentDelivery.shopAddress}</p>
                  </div>
                  
                  <div className="flex space-x-6">
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Itens</h3>
                      <p className="font-medium">{currentDelivery.items}</p>
                    </div>
                    
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Valor</h3>
                      <p className="font-medium">R$ {currentDelivery.total.toFixed(2)}</p>
                    </div>
                    
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Distância</h3>
                      <p className="font-medium">{currentDelivery.distance} km</p>
                    </div>
                    
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground">Tempo estimado</h3>
                      <p className="font-medium">{currentDelivery.estimatedTime} min</p>
                    </div>
                  </div>
                  
                  <div className="pt-4 flex space-x-2">
                    <Button variant="outline" className="flex-1">
                      <Navigation className="h-4 w-4 mr-2" />
                      Navegar
                    </Button>
                    <Button className="flex-1 bg-eco hover:bg-eco-dark" onClick={completeDelivery}>
                      <CheckCircle2 className="h-4 w-4 mr-2" />
                      Concluir Entrega
                    </Button>
                  </div>
                </div>
                
                <div className="h-[300px]">
                  <DeliveryMap 
                    orderAddress={currentDelivery.customerAddress}
                    shopAddress={currentDelivery.shopAddress}
                    delivererId="deliverer_123"
                    estimatedTime={currentDelivery.estimatedTime}
                    height="100%"
                    className="rounded-md overflow-hidden"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        ) : isOnline ? (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Entregas Disponíveis</CardTitle>
              <CardDescription>Selecione uma entrega para aceitar</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border rounded-md p-4">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-medium">Pedido #123456</h3>
                      <div className="flex items-center mt-1">
                        <MapPin className="h-4 w-4 text-muted-foreground mr-1" />
                        <p className="text-sm text-muted-foreground">2.5 km • Hamburgueria Artesanal</p>
                      </div>
                      <div className="flex items-center mt-1">
                        <DollarSign className="h-4 w-4 text-muted-foreground mr-1" />
                        <p className="text-sm text-muted-foreground">R$ 12,00 • 3 itens</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">25 min</p>
                      <p className="text-xs text-muted-foreground">Tempo estimado</p>
                    </div>
                  </div>
                  <div className="flex justify-end mt-4">
                    <Button className="bg-eco hover:bg-eco-dark" onClick={acceptDelivery}>
                      Aceitar Entrega
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card className="mb-8 border-dashed">
            <CardContent className="flex flex-col items-center justify-center py-12">
              <div className="bg-gray-100 p-4 rounded-full mb-4">
                <Truck className="h-8 w-8 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-medium mb-2">Você está offline</h3>
              <p className="text-muted-foreground text-center mb-6">
                Ative o modo online para começar a receber solicitações de entrega.
              </p>
              <Button className="bg-eco hover:bg-eco-dark" onClick={toggleOnlineStatus}>
                Ficar Online
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Abas principais */}
        <Tabs defaultValue="history">
          <TabsList className="mb-6">
            <TabsTrigger value="history" className="flex items-center">
              <Clock className="h-4 w-4 mr-2" />
              Histórico
            </TabsTrigger>
            <TabsTrigger value="earnings" className="flex items-center">
              <DollarSign className="h-4 w-4 mr-2" />
              Ganhos
            </TabsTrigger>
            <TabsTrigger value="stats" className="flex items-center">
              <TrendingUp className="h-4 w-4 mr-2" />
              Estatísticas
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="history">
            <Card>
              <CardHeader>
                <CardTitle>Histórico de Entregas</CardTitle>
                <CardDescription>Suas entregas recentes</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="border rounded-md p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium">Pedido #123455</h3>
                        <p className="text-sm text-muted-foreground">Pizzaria Napolitana • 3.2 km</p>
                        <div className="flex items-center mt-2">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Concluído
                          </span>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">R$ 15,00</p>
                        <p className="text-xs text-muted-foreground">Hoje, 14:30</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="border rounded-md p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium">Pedido #123454</h3>
                        <p className="text-sm text-muted-foreground">Mercado Express • 1.8 km</p>
                        <div className="flex items-center mt-2">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Concluído
                          </span>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">R$ 10,00</p>
                        <p className="text-xs text-muted-foreground">Hoje, 12:15</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="border rounded-md p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium">Pedido #123453</h3>
                        <p className="text-sm text-muted-foreground">Farmácia Saúde • 2.5 km</p>
                        <div className="flex items-center mt-2">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Concluído
                          </span>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">R$ 8,00</p>
                        <p className="text-xs text-muted-foreground">Hoje, 10:45</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full">Ver Histórico Completo</Button>
              </CardFooter>
            </Card>
          </TabsContent>
          
          <TabsContent value="earnings">
            <Card>
              <CardHeader>
                <CardTitle>Seus Ganhos</CardTitle>
                <CardDescription>Acompanhe seus ganhos com entregas</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Hoje</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-2xl font-bold">R$ 120,00</p>
                        <p className="text-sm text-muted-foreground">8 entregas</p>
                      </CardContent>
                    </Card>
                    
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Esta Semana</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-2xl font-bold">R$ 580,00</p>
                        <p className="text-sm text-muted-foreground">32 entregas</p>
                      </CardContent>
                    </Card>
                    
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Este Mês</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-2xl font-bold">R$ 2.450,00</p>
                        <p className="text-sm text-muted-foreground">145 entregas</p>
                      </CardContent>
                    </Card>
                  </div>
                  
                  <div className="h-[250px] flex items-center justify-center bg-gray-100 rounded-md">
                    <p className="text-muted-foreground">Gráfico de ganhos será exibido aqui</p>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button className="w-full bg-eco hover:bg-eco-dark">Solicitar Pagamento</Button>
              </CardFooter>
            </Card>
          </TabsContent>
          
          <TabsContent value="stats">
            <Card>
              <CardHeader>
                <CardTitle>Suas Estatísticas</CardTitle>
                <CardDescription>Acompanhe seu desempenho</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Taxa de Aceitação</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-2xl font-bold">92%</p>
                        <p className="text-sm text-green-600">+5% esta semana</p>
                      </CardContent>
                    </Card>
                    
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Tempo Médio</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-2xl font-bold">22 min</p>
                        <p className="text-sm text-green-600">-3 min esta semana</p>
                      </CardContent>
                    </Card>
                    
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Distância Total</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-2xl font-bold">145 km</p>
                        <p className="text-sm text-muted-foreground">Esta semana</p>
                      </CardContent>
                    </Card>
                    
                    <Card>
                      <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium">Avaliação</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-2xl font-bold">4.9</p>
                        <p className="text-sm text-green-600">+0.1 esta semana</p>
                      </CardContent>
                    </Card>
                  </div>
                  
                  <div className="h-[250px] flex items-center justify-center bg-gray-100 rounded-md">
                    <p className="text-muted-foreground">Gráfico de estatísticas será exibido aqui</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>
    </AnimatedPage>
  );
};

export default DelivererDashboard;
