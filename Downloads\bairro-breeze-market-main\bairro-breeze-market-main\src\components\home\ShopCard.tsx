import { motion } from "framer-motion";
import { Star } from "lucide-react";
import { Link } from "react-router-dom";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";

interface ShopCardProps {
  id: string;
  name: string;
  image: string;
  category: string;
  rating: number;
  deliveryTime: string;
  deliveryFee: number;
  featured?: boolean;
  className?: string;
}

export function ShopCard({
  id,
  name,
  image,
  category,
  rating,
  deliveryTime,
  deliveryFee,
  featured = false,
  className,
}: ShopCardProps) {
  const formattedDeliveryFee = new Intl.NumberFormat("pt-BR", {
    style: "currency",
    currency: "BRL",
  }).format(deliveryFee);

  return (
    <motion.div
      whileHover={{ y: -5 }}
      className={cn(
        "bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow",
        className
      )}
    >
      <Link to={`/shop/${id}`}>
        <div className="relative">
          <img
            src={image}
            alt={name}
            className="w-full h-32 object-cover"
          />
          {featured && (
            <Badge
              className="absolute top-2 right-2 bg-cta text-white"
            >
              Destaque
            </Badge>
          )}
        </div>
        <div className="p-4">
          <h3 className="font-bold text-lg mb-1 truncate">{name}</h3>
          <p className="text-muted-foreground text-sm mb-2">{category}</p>
          
          <div className="flex items-center mb-2">
            <div className="flex items-center bg-eco-50 text-eco-800 rounded px-2 py-0.5 text-xs font-medium">
              <Star className="h-3 w-3 fill-eco-800 mr-1" />
              {rating.toFixed(1)}
            </div>
            <span className="mx-2 text-muted-foreground">•</span>
            <span className="text-sm text-muted-foreground">{deliveryTime}</span>
          </div>
          
          <div className="text-sm">
            Entrega: {formattedDeliveryFee}
          </div>
        </div>
      </Link>
    </motion.div>
  );
}
