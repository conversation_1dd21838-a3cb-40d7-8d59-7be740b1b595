import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Heart, Star, Clock, MapPin } from "lucide-react";
import { motion } from "framer-motion";
import { toast } from "@/hooks/use-toast";
import { Link } from "react-router-dom";

interface ShopCardProps {
  id: string;
  name: string;
  description?: string;
  image: string;
  rating: number;
  category?: string;
  deliveryTime: string;
  deliveryFee: number;
  distance?: string;
  isOpen?: boolean;
}

export function ShopCard({
  id,
  name,
  description,
  image,
  rating,
  category,
  deliveryTime,
  deliveryFee,
  distance = "2.5 km",
  isOpen = true
}: ShopCardProps) {
  const [isFavorite, setIsFavorite] = useState(false);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const toggleFavorite = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsFavorite(!isFavorite);
    
    toast({
      title: isFavorite ? "Removido dos favoritos" : "Adicionado aos favoritos",
      description: `${name} foi ${isFavorite ? "removido dos" : "adicionado aos"} favoritos.`,
    });
  };

  return (
    <motion.div
      whileHover={{ y: -5, boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)" }}
      className="bg-white rounded-lg overflow-hidden border"
    >
      <Link to={`/shop/${id}`} className="block">
        <div className="relative">
          <img
            src={image}
            alt={name}
            className="w-full h-32 object-cover"
          />
          
          {!isOpen && (
            <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
              <Badge className="bg-gray-800 text-white">
                Fechado
              </Badge>
            </div>
          )}
          
          <Button
            variant="ghost"
            size="icon"
            className={`absolute top-2 right-2 rounded-full bg-white/80 hover:bg-white ${
              isFavorite ? "text-red-500" : "text-gray-500"
            }`}
            onClick={toggleFavorite}
          >
            <Heart className={`h-5 w-5 ${isFavorite ? "fill-current" : ""}`} />
          </Button>
        </div>
        
        <div className="p-3">
          <div className="flex justify-between items-start mb-1">
            <h3 className="font-medium">{name}</h3>
          </div>
          
          {category && (
            <p className="text-xs text-muted-foreground mb-1">{category}</p>
          )}
          
          <div className="flex items-center gap-2 mb-2">
            <div className="flex items-center">
              <Star className="h-3 w-3 text-yellow-400 fill-yellow-400 mr-1" />
              <span className="text-xs font-medium">{rating.toFixed(1)}</span>
            </div>
            
            <div className="w-1 h-1 bg-gray-300 rounded-full"></div>
            
            <div className="flex items-center">
              <Clock className="h-3 w-3 text-muted-foreground mr-1" />
              <span className="text-xs">{deliveryTime} min</span>
            </div>
            
            <div className="w-1 h-1 bg-gray-300 rounded-full"></div>
            
            <div className="flex items-center">
              <MapPin className="h-3 w-3 text-muted-foreground mr-1" />
              <span className="text-xs">{distance}</span>
            </div>
          </div>
          
          <div className="flex justify-between items-center mt-2">
            <div className="flex items-center">
              <p className="text-xs">
                Entrega: <span className="font-medium">{formatCurrency(deliveryFee)}</span>
              </p>
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              className="h-8 px-3 rounded-full bg-eco-light text-eco hover:bg-eco hover:text-white text-xs"
            >
              Ver loja
            </Button>
          </div>
        </div>
      </Link>
    </motion.div>
  );
}
