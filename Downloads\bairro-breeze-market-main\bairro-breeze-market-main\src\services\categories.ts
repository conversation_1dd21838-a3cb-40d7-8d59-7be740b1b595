import { categoryService } from './categoryService';
import { Tables } from '@/types/database';

// Category interface for frontend
export interface Category {
  id: string;
  name: string;
  icon: string;
}

// Convert database category to frontend category model
const mapDatabaseCategoryToCategory = (dbCategory: Tables<'categories'>): Category => ({
  id: dbCategory.id,
  name: dbCategory.name,
  icon: dbCategory.icon
});

// Mock categories for development mode
const mockCategories: Category[] = [
  { id: '1', name: '<PERSON>rca<PERSON>', icon: 'ShoppingBag' },
  { id: '2', name: '<PERSON><PERSON>', icon: 'Utensils' },
  { id: '3', name: '<PERSON><PERSON><PERSON>', icon: 'Pill' },
  { id: '4', name: 'Padar<PERSON>', icon: 'Croissant' },
  { id: '5', name: 'Açougue', icon: 'Beef' },
  { id: '6', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', icon: 'Apple' },
  { id: '7', name: '<PERSON><PERSON><PERSON>', icon: 'Wine' },
  { id: '8', name: 'Pet Shop', icon: 'Paw' },
];

// Fetch all categories
export const fetchCategories = async (): Promise<Category[]> => {
  try {
    const categories = await categoryService.getAllCategories();
    return categories.map(mapDatabaseCategoryToCategory);
  } catch (error) {
    console.error('Error fetching categories:', error);
    return mockCategories;
  }
};
