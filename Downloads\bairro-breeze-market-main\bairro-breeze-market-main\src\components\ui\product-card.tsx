import { ShoppingCart } from "lucide-react";
import { But<PERSON> } from "./button";
import { Badge } from "./badge";
import { Link } from "react-router-dom";
import { useCart } from "@/hooks/useCart";
import { Product } from "@/types/product";
import { MouseEvent } from "react";

interface ProductCardProps extends Omit<Product, 'id'> {
  id: string;
  name: string;
  image: string;
  price: number;
  originalPrice?: number;
  shopName: string;
  isPromo?: boolean;
}

export function ProductCard({
  id,
  name,
  image,
  price,
  originalPrice,
  shopName,
  isPromo = false,
}: ProductCardProps) {
  const { addItem } = useCart();
  const formattedPrice = new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL',
  }).format(price);

  const formattedOriginalPrice = originalPrice
    ? new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL',
      }).format(originalPrice)
    : null;

  return (
    <Link to={`/product/${encodeURIComponent(name)}`} className="block">
      <div className="overflow-hidden transition-all border rounded-lg hover:shadow-md">
        <div className="relative h-36">
          <img
            src={image}
            alt={name}
            className="object-cover w-full h-full"
          />
          {isPromo && (
            <Badge className="absolute top-2 left-2 bg-cta text-white">
              Promoção
            </Badge>
          )}
        </div>
        <div className="p-3">
          <h3 className="text-sm font-medium line-clamp-2">{name}</h3>
          <p className="text-xs text-muted-foreground">{shopName}</p>
          <div className="flex items-end justify-between mt-2">
            <div>
              <div className="text-sm font-bold">{formattedPrice}</div>
              {formattedOriginalPrice && (
                <div className="text-xs text-muted-foreground line-through">
                  {formattedOriginalPrice}
                </div>
              )}
            </div>
            <Button
              size="icon"
              variant="ghost"
              className="h-8 w-8"
              onClick={(e: MouseEvent) => {
                e.preventDefault();
                e.stopPropagation();
                addItem({ id, name, image, price, originalPrice, shopName, isPromo }, 1);
              }}
            >
              <ShoppingCart className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>
    </Link>
  );
}
