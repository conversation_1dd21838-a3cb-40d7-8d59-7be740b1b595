import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { DelivererOrdersList } from "@/components/deliverer/DelivererOrdersList";
import { AnimatedPage } from "@/components/animations";
import { LoadingState } from "@/components/ui/async-state";
import { toast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";
import { Truck, LogOut, LayoutDashboard } from "lucide-react";

export default function DelivererOrders() {
  const { user, signOut, loading } = useAuth();
  const navigate = useNavigate();
  
  // Handle sign out
  const handleSignOut = async () => {
    try {
      await signOut();
      navigate("/login");
    } catch (error) {
      console.error("Error signing out:", error);
      toast({
        title: "Erro ao sair",
        description: "Não foi possível sair da sua conta. Tente novamente mais tarde.",
        variant: "destructive",
      });
    }
  };
  
  // Redirect to login if not authenticated
  if (!loading && !user) {
    navigate('/login');
    return null;
  }
  
  // Check if user is a deliverer
  if (!loading && user && user.user_metadata?.role !== "deliverer") {
    toast({
      title: "Acesso negado",
      description: "Você não tem permissão para acessar esta página.",
      variant: "destructive",
    });
    navigate('/');
    return null;
  }
  
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingState message="Carregando..." />
      </div>
    );
  }
  
  return (
    <AnimatedPage className="min-h-screen flex flex-col bg-gray-50">
      <header className="bg-trust text-white sticky top-0 z-10">
        <div className="container px-4 h-16 flex items-center justify-between">
          <div className="flex items-center">
            <Truck className="h-6 w-6 mr-2" />
            <h1 className="text-xl font-medium">Painel do Entregador</h1>
          </div>
          <div className="flex items-center gap-2">
            <Button 
              variant="ghost" 
              className="text-white"
              onClick={() => navigate("/deliverer/dashboard")}
            >
              <LayoutDashboard className="h-5 w-5 mr-2" />
              Dashboard
            </Button>
            <Button variant="ghost" onClick={handleSignOut} className="text-white">
              <LogOut className="h-5 w-5 mr-2" />
              Sair
            </Button>
          </div>
        </div>
      </header>
      
      <main className="flex-1 container px-4 py-6 max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Gerenciamento de Entregas</h1>
            <p className="text-muted-foreground">
              Visualize e gerencie suas entregas
            </p>
          </div>
        </div>
        
        <DelivererOrdersList />
      </main>
    </AnimatedPage>
  );
}
