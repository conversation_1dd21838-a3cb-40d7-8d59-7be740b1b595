import { ApiService } from './api';
import { Tables, Insertable, Updatable } from '@/types/database';
import { supabaseClient } from '@/lib/supabase';

// Create a typed service for deliveries
class DeliveryService extends ApiService<'deliveries'> {
  constructor() {
    super('deliveries');
  }

  /**
   * Get deliveries by deliverer ID
   * @param delivererId Deliverer ID
   * @returns Promise with deliveries
   */
  async getByDelivererId(delivererId: string): Promise<Tables<'deliveries'>[]> {
    try {
      const { data, error } = await supabaseClient
        .from('deliveries')
        .select('*')
        .eq('deliverer_id', delivererId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching deliveries by deliverer ID:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in getByDelivererId:', error);
      return [];
    }
  }

  /**
   * Get deliveries by status
   * @param status Delivery status
   * @param delivererId Optional deliverer ID
   * @returns Promise with deliveries
   */
  async getByStatus(
    status: Tables<'deliveries'>['status'],
    delivererId?: string
  ): Promise<Tables<'deliveries'>[]> {
    try {
      let query = supabaseClient
        .from('deliveries')
        .select('*')
        .eq('status', status);

      if (delivererId) {
        query = query.eq('deliverer_id', delivererId);
      }

      const { data, error } = await query.order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching deliveries by status:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in getByStatus:', error);
      return [];
    }
  }

  /**
   * Update delivery status
   * @param deliveryId Delivery ID
   * @param status New status
   * @returns Promise with updated delivery
   */
  async updateStatus(
    deliveryId: string,
    status: Tables<'deliveries'>['status']
  ): Promise<Tables<'deliveries'> | null> {
    try {
      const { data, error } = await supabaseClient
        .from('deliveries')
        .update({ status })
        .eq('id', deliveryId)
        .select()
        .single();

      if (error) {
        console.error('Error updating delivery status:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in updateStatus:', error);
      return null;
    }
  }

  /**
   * Complete a delivery
   * @param deliveryId Delivery ID
   * @returns Promise with completed delivery
   */
  async completeDelivery(deliveryId: string): Promise<Tables<'deliveries'> | null> {
    try {
      // Get the delivery to find the order ID
      const { data: delivery, error: fetchError } = await supabaseClient
        .from('deliveries')
        .select('*')
        .eq('id', deliveryId)
        .single();

      if (fetchError) {
        console.error('Error fetching delivery:', fetchError);
        throw fetchError;
      }

      // Update the delivery status
      const { data, error } = await supabaseClient
        .from('deliveries')
        .update({
          status: 'completed',
          delivery_time: new Date().toISOString()
        })
        .eq('id', deliveryId)
        .select()
        .single();

      if (error) {
        console.error('Error completing delivery:', error);
        throw error;
      }

      // Update the order status
      const { error: orderError } = await supabaseClient
        .from('orders')
        .update({ status: 'delivered' })
        .eq('id', delivery.order_id);

      if (orderError) {
        console.error('Error updating order status:', orderError);
        throw orderError;
      }

      return data;
    } catch (error) {
      console.error('Error in completeDelivery:', error);
      return null;
    }
  }

  /**
   * Get earnings for a deliverer
   * @param delivererId Deliverer ID
   * @param period 'day', 'week', 'month', or 'all'
   * @returns Promise with total earnings
   */
  async getEarnings(
    delivererId: string,
    period: 'day' | 'week' | 'month' | 'all' = 'all'
  ): Promise<number> {
    try {
      let query = supabaseClient
        .from('deliveries')
        .select('earnings')
        .eq('deliverer_id', delivererId)
        .eq('status', 'completed');

      // Apply date filter based on period
      if (period !== 'all') {
        const now = new Date();
        let startDate: Date;

        switch (period) {
          case 'day':
            startDate = new Date(now.setHours(0, 0, 0, 0));
            break;
          case 'week':
            startDate = new Date(now.setDate(now.getDate() - now.getDay()));
            startDate.setHours(0, 0, 0, 0);
            break;
          case 'month':
            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
            break;
          default:
            startDate = new Date(0); // Beginning of time
        }

        query = query.gte('delivery_time', startDate.toISOString());
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching earnings:', error);
        throw error;
      }

      // Calculate total earnings
      return data.reduce((total, item) => total + (item.earnings || 0), 0);
    } catch (error) {
      console.error('Error in getEarnings:', error);
      return 0;
    }
  }
}

export const deliveryService = new DeliveryService();
