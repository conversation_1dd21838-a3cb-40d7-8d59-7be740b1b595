import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { CustomerOrdersList } from "@/components/customer/CustomerOrdersList";
import { AnimatedPage } from "@/components/animations";
import { LoadingState } from "@/components/ui/async-state";
import { toast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";
import { Navbar } from "@/components/ui/navbar";
import { BottomNav } from "@/components/ui/bottom-nav";
import { ShoppingBag } from "lucide-react";

export default function CustomerOrders() {
  const { user, loading } = useAuth();
  const navigate = useNavigate();
  
  // Redirect to login if not authenticated
  if (!loading && !user) {
    navigate('/login');
    return null;
  }
  
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingState message="Carregando..." />
      </div>
    );
  }
  
  return (
    <AnimatedPage className="min-h-screen flex flex-col bg-gray-50">
      <Navbar />
      
      <main className="flex-1 container px-4 py-6 max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Meus Pedidos</h1>
            <p className="text-muted-foreground">
              Visualize e acompanhe seus pedidos
            </p>
          </div>
          <div className="mt-4 md:mt-0">
            <Button 
              className="bg-cta hover:bg-cta-dark"
              onClick={() => navigate("/")}
            >
              <ShoppingBag className="mr-2 h-4 w-4" />
              Continuar Comprando
            </Button>
          </div>
        </div>
        
        <CustomerOrdersList />
      </main>
      
      <BottomNav />
    </AnimatedPage>
  );
}
