import { cn } from "@/lib/utils";
import { ReactNode } from "react";

interface GradientBackgroundProps {
  children: ReactNode;
  className?: string;
  colorFrom?: string;
  colorTo?: string;
  direction?: "to-r" | "to-l" | "to-t" | "to-b" | "to-tr" | "to-tl" | "to-br" | "to-bl";
}

export function GradientBackground({
  children,
  className,
  colorFrom = "from-cta-100",
  colorTo = "to-cta-300",
  direction = "to-r",
}: GradientBackgroundProps) {
  return (
    <div
      className={cn(
        `bg-gradient-${direction}`,
        colorFrom,
        colorTo,
        "rounded-lg p-6",
        className
      )}
    >
      {children}
    </div>
  );
}
