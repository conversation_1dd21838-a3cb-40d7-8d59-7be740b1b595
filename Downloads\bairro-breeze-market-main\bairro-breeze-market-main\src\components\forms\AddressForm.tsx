import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Loader2, MapPin, Check, Plus } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";
import { motion } from "framer-motion";
import { 
  getAddresses, 
  createAddress, 
  setDefaultAddress,
  Address
} from "@/services/addresses";
import { getLocationByAddress } from "@/services/map";
import { 
  addressSchema, 
  AddressFormValues,
  formatZipCode
} from "@/lib/validations";

interface AddressFormProps {
  onAddressSelect: (address: string) => void;
  onComplete: () => void;
}

export function AddressForm({ onAddressSelect, onComplete }: AddressFormProps) {
  const [savedAddresses, setSavedAddresses] = useState<Address[]>([]);
  const [selectedAddressId, setSelectedAddressId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingAddresses, setIsLoadingAddresses] = useState(false);
  const [showNewAddressForm, setShowNewAddressForm] = useState(false);
  const { user } = useAuth();

  // React Hook Form com validação Zod
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isSubmitting, isValid }
  } = useForm<AddressFormValues>({
    resolver: zodResolver(addressSchema),
    mode: "onChange"
  });

  // Observar o campo de CEP para formatação
  const zipCode = watch("zipCode");

  // Efeito para formatar o CEP
  useEffect(() => {
    if (zipCode) {
      const formattedZipCode = formatZipCode(zipCode);
      if (formattedZipCode !== zipCode) {
        setValue("zipCode", formattedZipCode);
      }
    }
  }, [zipCode, setValue]);

  // Carregar endereços salvos
  useEffect(() => {
    const loadAddresses = async () => {
      if (!user) return;
      
      setIsLoadingAddresses(true);
      try {
        const addresses = await getAddresses(user.id);
        setSavedAddresses(addresses);
        
        // Selecionar o endereço padrão se existir
        const defaultAddress = addresses.find(addr => addr.isDefault);
        if (defaultAddress) {
          setSelectedAddressId(defaultAddress.id);
          onAddressSelect(formatFullAddress(defaultAddress));
        }
      } catch (error) {
        console.error("Error loading addresses:", error);
        toast({
          title: "Erro ao carregar endereços",
          description: "Não foi possível carregar seus endereços salvos.",
          variant: "destructive"
        });
      } finally {
        setIsLoadingAddresses(false);
      }
    };
    
    loadAddresses();
  }, [user, onAddressSelect]);

  // Formatar endereço completo
  const formatFullAddress = (address: Address): string => {
    return `${address.street}, ${address.number}${address.complement ? ` - ${address.complement}` : ''} - ${address.neighborhood}, ${address.city}, ${address.state} - ${address.zipCode}`;
  };

  // Enviar formulário
  const onSubmit = async (data: AddressFormValues) => {
    setIsLoading(true);
    
    try {
      // Validar endereço com serviço de geocodificação
      const fullAddress = `${data.street}, ${data.number} - ${data.neighborhood}, ${data.city}, ${data.state} - ${data.zipCode}`;
      const location = await getLocationByAddress(fullAddress);
      
      if (location) {
        // Endereço válido
        onAddressSelect(fullAddress);
        
        // Salvar endereço se usuário estiver logado
        if (user) {
          await saveAddress(data);
        }
        
        // Ir para próxima etapa
        onComplete();
      } else {
        toast({
          title: "Endereço não encontrado",
          description: "Não foi possível validar o endereço. Verifique os dados e tente novamente.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error("Error validating address:", error);
      toast({
        title: "Erro ao validar endereço",
        description: "Ocorreu um erro ao validar o endereço. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Salvar novo endereço
  const saveAddress = async (data: AddressFormValues) => {
    if (!user) return;
    
    try {
      const newAddress = await createAddress({
        userId: user.id,
        name: data.name,
        street: data.street,
        number: data.number,
        complement: data.complement,
        neighborhood: data.neighborhood,
        city: data.city,
        state: data.state,
        zipCode: data.zipCode,
        isDefault: savedAddresses.length === 0 // Primeiro endereço é o padrão
      });
      
      if (newAddress) {
        toast({
          title: "Endereço salvo",
          description: "Seu endereço foi salvo com sucesso.",
        });
        
        // Atualizar lista de endereços
        setSavedAddresses(prev => [...prev, newAddress]);
        setSelectedAddressId(newAddress.id);
      }
    } catch (error) {
      console.error("Error saving address:", error);
      toast({
        title: "Erro ao salvar endereço",
        description: "Ocorreu um erro ao salvar o endereço. Tente novamente.",
        variant: "destructive"
      });
    }
  };

  // Selecionar endereço existente
  const handleAddressSelect = (addressId: string) => {
    setSelectedAddressId(addressId);
    
    const selectedAddress = savedAddresses.find(addr => addr.id === addressId);
    if (selectedAddress) {
      onAddressSelect(formatFullAddress(selectedAddress));
      
      // Definir como padrão se não for
      if (!selectedAddress.isDefault) {
        setDefaultAddress(addressId).catch(error => {
          console.error("Error setting default address:", error);
        });
      }
    }
  };

  // Continuar com endereço selecionado
  const handleContinue = () => {
    if (selectedAddressId) {
      onComplete();
    } else {
      toast({
        title: "Selecione um endereço",
        description: "Por favor, selecione um endereço para continuar.",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Endereços salvos */}
      {user && savedAddresses.length > 0 && !showNewAddressForm && (
        <div className="space-y-4">
          <RadioGroup value={selectedAddressId || ""} onValueChange={handleAddressSelect}>
            <div className="space-y-3">
              {savedAddresses.map((address) => (
                <motion.div
                  key={address.id}
                  className="flex items-start space-x-3 border rounded-md p-3"
                  whileHover={{ backgroundColor: "#f9fafb" }}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                >
                  <RadioGroupItem value={address.id} id={`address-${address.id}`} className="mt-1" />
                  <div className="flex-1">
                    <Label htmlFor={`address-${address.id}`} className="font-medium cursor-pointer">
                      {address.name} {address.isDefault && <span className="text-xs text-eco ml-2">(Padrão)</span>}
                    </Label>
                    <p className="text-sm text-muted-foreground mt-1">
                      {formatFullAddress(address)}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          </RadioGroup>

          <div className="flex justify-between items-center pt-2">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => setShowNewAddressForm(true)}
              className="text-sm"
            >
              <Plus className="mr-1 h-4 w-4" />
              Novo endereço
            </Button>
            
            <Button
              type="button"
              onClick={handleContinue}
              disabled={!selectedAddressId}
              className="bg-eco hover:bg-eco-dark"
            >
              Continuar
            </Button>
          </div>
        </div>
      )}

      {/* Formulário de novo endereço */}
      {(showNewAddressForm || !user || savedAddresses.length === 0) && (
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="col-span-2">
              <Label htmlFor="name">Nome do endereço</Label>
              <Input
                id="name"
                placeholder="Ex: Casa, Trabalho"
                {...register("name")}
                className={errors.name ? "border-red-500" : ""}
              />
              {errors.name && (
                <p className="text-red-500 text-xs mt-1">{errors.name.message}</p>
              )}
            </div>
            
            <div className="col-span-2">
              <Label htmlFor="zipCode">CEP</Label>
              <Input
                id="zipCode"
                placeholder="00000-000"
                {...register("zipCode")}
                className={errors.zipCode ? "border-red-500" : ""}
              />
              {errors.zipCode && (
                <p className="text-red-500 text-xs mt-1">{errors.zipCode.message}</p>
              )}
            </div>
            
            <div className="col-span-2">
              <Label htmlFor="street">Rua</Label>
              <Input
                id="street"
                placeholder="Nome da rua"
                {...register("street")}
                className={errors.street ? "border-red-500" : ""}
              />
              {errors.street && (
                <p className="text-red-500 text-xs mt-1">{errors.street.message}</p>
              )}
            </div>
            
            <div className="col-span-1">
              <Label htmlFor="number">Número</Label>
              <Input
                id="number"
                placeholder="123"
                {...register("number")}
                className={errors.number ? "border-red-500" : ""}
              />
              {errors.number && (
                <p className="text-red-500 text-xs mt-1">{errors.number.message}</p>
              )}
            </div>
            
            <div className="col-span-1">
              <Label htmlFor="complement">Complemento</Label>
              <Input
                id="complement"
                placeholder="Apto, Bloco, etc."
                {...register("complement")}
              />
            </div>
            
            <div className="col-span-2">
              <Label htmlFor="neighborhood">Bairro</Label>
              <Input
                id="neighborhood"
                placeholder="Bairro"
                {...register("neighborhood")}
                className={errors.neighborhood ? "border-red-500" : ""}
              />
              {errors.neighborhood && (
                <p className="text-red-500 text-xs mt-1">{errors.neighborhood.message}</p>
              )}
            </div>
            
            <div className="col-span-1">
              <Label htmlFor="city">Cidade</Label>
              <Input
                id="city"
                placeholder="Cidade"
                {...register("city")}
                className={errors.city ? "border-red-500" : ""}
              />
              {errors.city && (
                <p className="text-red-500 text-xs mt-1">{errors.city.message}</p>
              )}
            </div>
            
            <div className="col-span-1">
              <Label htmlFor="state">Estado</Label>
              <Input
                id="state"
                placeholder="UF"
                maxLength={2}
                {...register("state")}
                className={errors.state ? "border-red-500" : ""}
              />
              {errors.state && (
                <p className="text-red-500 text-xs mt-1">{errors.state.message}</p>
              )}
            </div>
          </div>

          <div className="flex justify-between items-center pt-2">
            {showNewAddressForm && (
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowNewAddressForm(false)}
              >
                Cancelar
              </Button>
            )}
            
            <Button
              type="submit"
              disabled={isSubmitting || !isValid}
              className={`${showNewAddressForm ? "" : "ml-auto"} bg-eco hover:bg-eco-dark`}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Validando...
                </>
              ) : (
                <>
                  <MapPin className="mr-2 h-4 w-4" />
                  {user ? "Salvar e Continuar" : "Continuar"}
                </>
              )}
            </Button>
          </div>
        </form>
      )}
    </div>
  );
}
