
import { Star } from "lucide-react";
import { Badge } from "./badge";

interface ShopCardProps {
  name: string;
  image: string;
  category: string;
  deliveryTime: string;
  rating: number;
  distance: string;
  isOpen: boolean;
}

export function ShopCard({
  name,
  image,
  category,
  deliveryTime,
  rating,
  distance,
  isOpen,
}: ShopCardProps) {
  return (
    <div className="overflow-hidden transition-all border rounded-lg cursor-pointer hover:shadow-md">
      <div className="relative h-32">
        <img
          src={image}
          alt={name}
          className="object-cover w-full h-full"
        />
        {!isOpen && (
          <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
            <span className="px-2 py-1 text-sm font-medium text-white bg-gray-800 rounded">
              Fechado
            </span>
          </div>
        )}
        <Badge 
          className="absolute top-2 right-2 bg-white text-foreground" 
          variant="outline"
        >
          {distance}
        </Badge>
      </div>
      <div className="p-3">
        <h3 className="text-base font-medium">{name}</h3>
        <p className="text-sm text-muted-foreground">{category}</p>
        <div className="flex items-center justify-between mt-2">
          <div className="flex items-center">
            <Star className="w-4 h-4 mr-1 text-yellow-400 fill-yellow-400" />
            <span className="text-sm">{rating}</span>
          </div>
          <span className="text-sm text-muted-foreground">{deliveryTime}</span>
        </div>
      </div>
    </div>
  );
}
