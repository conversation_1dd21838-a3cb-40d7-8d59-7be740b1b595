# Guia do Usuário - Já Comprei App

Este guia explica como utilizar o aplicativo "Já Comprei" com os diferentes perfis de usuário: cliente, comerciante e entregador.

## Índice

1. [Primeiros Passos](#primeiros-passos)
2. [Perfil de Cliente](#perfil-de-cliente)
3. [Perfil de Comerciante](#perfil-de-comerciante)
4. [Perfi<PERSON> de Entregador](#perfil-de-entregador)
5. [Dicas e Truques](#dicas-e-truques)
6. [Solução de Problemas](#solução-de-problemas)

## Primeiros Passos

### Instalação e Configuração

1. Acesse o aplicativo através do navegador em [jacomprei.com](https://jacomprei.com) ou instale o aplicativo em seu dispositivo.
2. Crie uma conta clicando em "Registrar" ou faça login se já tiver uma conta.
3. <PERSON><PERSON><PERSON> o registro, você precisará escolher um perfil: cliente, comerciante ou entregador.

### Modo de Desenvolvimento

Se você estiver executando o aplicativo em modo de desenvolvimento (sem conexão com o Supabase), você pode:

1. Fazer login com qualquer email e senha
2. Selecionar o perfil desejado (cliente, comerciante ou entregador) na tela de login
3. Explorar todas as funcionalidades com dados simulados

## Perfil de Cliente

### Navegação e Descoberta

1. **Página Inicial**: Explore produtos e lojas populares
2. **Categorias**: Navegue por produtos organizados por categoria
3. **Busca**: Pesquise produtos ou lojas específicas
4. **Filtros**: Refine resultados por preço, avaliação, distância, etc.

### Compras

1. **Detalhes do Produto**: Veja informações detalhadas, fotos e avaliações
2. **Adicionar ao Carrinho**: Adicione produtos ao seu carrinho
   - Nota: Só é possível adicionar produtos da mesma loja em um único pedido
3. **Carrinho**: Revise itens, ajuste quantidades ou remova produtos
4. **Checkout**: Siga o processo de três etapas:
   - Endereço: Informe onde deseja receber o pedido
   - Pagamento: Escolha o método de pagamento
   - Revisão: Confirme os detalhes antes de finalizar

### Acompanhamento de Pedidos

1. **Confirmação**: Receba detalhes do pedido após a finalização
2. **Rastreamento**: Acompanhe o status do pedido em tempo real
3. **Mapa**: Visualize a localização do entregador durante a entrega
4. **Histórico**: Acesse todos os seus pedidos anteriores

### Perfil e Configurações

1. **Meu Perfil**: Gerencie suas informações pessoais
2. **Endereços**: Adicione e gerencie endereços de entrega
3. **Métodos de Pagamento**: Gerencie cartões e outras formas de pagamento
4. **Avaliações**: Veja suas avaliações de produtos e lojas

## Perfil de Comerciante

### Dashboard

1. **Visão Geral**: Veja estatísticas de vendas, pedidos recentes e métricas importantes
2. **Pedidos Ativos**: Gerencie pedidos que precisam de atenção
3. **Análises**: Explore dados de vendas, produtos populares e tendências

### Gerenciamento de Produtos

1. **Catálogo**: Visualize todos os seus produtos
2. **Adicionar Produto**: Crie novos produtos com detalhes, fotos e preços
3. **Editar Produto**: Atualize informações, preços e disponibilidade
4. **Promoções**: Configure descontos e ofertas especiais

### Gerenciamento de Pedidos

1. **Novos Pedidos**: Aceite ou recuse pedidos recebidos
2. **Em Preparação**: Acompanhe pedidos em andamento
3. **Prontos para Entrega**: Marque pedidos como prontos para o entregador
4. **Histórico**: Veja todos os pedidos anteriores

### Configurações da Loja

1. **Perfil da Loja**: Atualize informações, fotos e descrição
2. **Horários de Funcionamento**: Configure quando sua loja está disponível
3. **Taxas de Entrega**: Defina valores para entrega
4. **Categorias**: Organize seus produtos em categorias

## Perfil de Entregador

### Dashboard

1. **Entregas Disponíveis**: Veja pedidos disponíveis para entrega na sua região
2. **Entregas Ativas**: Acompanhe entregas que você aceitou
3. **Ganhos**: Visualize seus ganhos diários, semanais e mensais

### Processo de Entrega

1. **Aceitar Entrega**: Escolha pedidos disponíveis para entregar
2. **Navegação**: Use o mapa para encontrar a loja e o endereço de entrega
3. **Atualização de Status**: Atualize o status da entrega em cada etapa:
   - A caminho da loja
   - Retirada do pedido
   - A caminho do cliente
   - Entrega concluída
4. **Confirmação**: Confirme a entrega após entregar ao cliente

### Perfil e Configurações

1. **Meu Perfil**: Gerencie suas informações pessoais
2. **Veículo**: Configure detalhes do seu veículo (bicicleta, moto, carro)
3. **Área de Atuação**: Defina a região onde deseja realizar entregas
4. **Disponibilidade**: Configure seus horários de disponibilidade
5. **Informações Bancárias**: Gerencie dados para recebimento de pagamentos

## Dicas e Truques

### Para Clientes

- Salve endereços frequentes para agilizar o checkout
- Ative notificações para acompanhar o status dos pedidos
- Avalie produtos e lojas para ajudar outros usuários
- Verifique promoções na página inicial regularmente

### Para Comerciantes

- Mantenha seu catálogo atualizado com fotos de qualidade
- Responda rapidamente aos pedidos para melhorar sua avaliação
- Utilize promoções para atrair mais clientes
- Analise os relatórios de vendas para otimizar seu negócio

### Para Entregadores

- Mantenha seu status atualizado para melhor experiência do cliente
- Planeje rotas eficientes para maximizar entregas
- Mantenha seu perfil completo para aumentar a confiança dos clientes
- Configure corretamente sua área de atuação para receber entregas relevantes

## Solução de Problemas

### Problemas Comuns

1. **Não consigo fazer login**
   - Verifique se o email e senha estão corretos
   - Tente recuperar sua senha
   - Em modo de desenvolvimento, deixe as credenciais do Supabase em branco

2. **Não consigo adicionar produtos de lojas diferentes**
   - O aplicativo permite apenas produtos da mesma loja em um pedido
   - Finalize o pedido atual ou esvazie o carrinho para adicionar produtos de outra loja

3. **O mapa não está carregando**
   - Verifique sua conexão com a internet
   - Permita acesso à sua localização no navegador
   - Em modo de desenvolvimento, o mapa usa dados simulados

4. **Não estou recebendo notificações**
   - Verifique as permissões de notificação no navegador
   - Certifique-se de que as notificações estão ativadas nas configurações

### Contato e Suporte

Para problemas não resolvidos, entre em contato com o suporte:

- Email: <EMAIL>
- Chat: Disponível no aplicativo
- Telefone: (11) 1234-5678

---

Este guia está em constante atualização. Para a versão mais recente, visite [jacomprei.com/guia](https://jacomprei.com/guia).
