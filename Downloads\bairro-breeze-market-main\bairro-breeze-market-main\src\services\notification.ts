import { supabaseClient } from '@/lib/supabase';
import { Order, OrderStatus } from '@/types/order';
import {
  Notification,
  NotificationType,
  CreateNotificationRequest,
  NotificationFilters
} from '@/types/notification';

// Mock notifications for development mode
let mockNotifications: Notification[] = [
  {
    id: '1',
    userId: 'user1',
    title: 'Pedido Confirmado',
    message: 'Seu pedido #1234 foi confirmado e está sendo preparado.',
    type: 'order',
    read: false,
    createdAt: '2024-04-18T10:05:00Z',
    data: { orderId: '1234' }
  },
  {
    id: '2',
    userId: 'user1',
    title: 'Entrega em Andamento',
    message: 'Seu pedido #1234 saiu para entrega e chegará em breve.',
    type: 'delivery',
    read: true,
    createdAt: '2024-04-18T10:30:00Z',
    data: { orderId: '1234', deliveryId: '5678' }
  }
];

/**
 * Get notifications for a user
 * @param userId User ID
 * @param limit Number of notifications to return
 * @returns Promise with notifications
 */
export const getNotifications = async (
  userId: string,
  limit = 10
): Promise<Notification[]> => {
  try {
    // In a real app, this would fetch from a database

    // For now, return mock data
    return mockNotifications
      .filter(notification => notification.userId === userId)
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, limit);
  } catch (error) {
    console.error(`Error fetching notifications for user ${userId}:`, error);
    return [];
  }
};

/**
 * Get unread notification count for a user
 * @param userId User ID
 * @returns Promise with unread count
 */
export const getUnreadCount = async (userId: string): Promise<number> => {
  try {
    // In a real app, this would fetch from a database

    // For now, count mock data
    return mockNotifications.filter(
      notification => notification.userId === userId && !notification.read
    ).length;
  } catch (error) {
    console.error(`Error fetching unread count for user ${userId}:`, error);
    return 0;
  }
};

/**
 * Mark notification as read
 * @param notificationId Notification ID
 * @returns Promise with success status
 */
export const markAsRead = async (notificationId: string): Promise<boolean> => {
  try {
    // In a real app, this would update the database

    // For now, update mock data
    const notification = mockNotifications.find(n => n.id === notificationId);
    if (notification) {
      notification.read = true;
      return true;
    }

    return false;
  } catch (error) {
    console.error(`Error marking notification ${notificationId} as read:`, error);
    return false;
  }
};

/**
 * Mark all notifications as read for a user
 * @param userId User ID
 * @returns Promise with success status
 */
export const markAllAsRead = async (userId: string): Promise<boolean> => {
  try {
    // In a real app, this would update the database

    // For now, update mock data
    mockNotifications.forEach(notification => {
      if (notification.userId === userId) {
        notification.read = true;
      }
    });

    return true;
  } catch (error) {
    console.error(`Error marking all notifications as read for user ${userId}:`, error);
    return false;
  }
};

/**
 * Send order confirmation email
 * @param email User email
 * @param order Order data
 * @returns Promise with success status
 */
export const sendOrderConfirmationEmail = async (
  email: string,
  order: Order
): Promise<boolean> => {
  try {
    // In a real app, this would use an email service

    // For now, just log it
    console.info(`[DEV] Sending order confirmation email to ${email} for order ${order.id}`);

    // Create a notification for the user
    await createNotification({
      userId: order.id.split('-')[0], // Mock user ID extraction
      title: 'Pedido Confirmado',
      message: `Seu pedido #${order.id} foi confirmado e está sendo processado.`,
      type: 'order',
      data: { orderId: order.id }
    });

    return true;
  } catch (error) {
    console.error(`Error sending order confirmation email to ${email}:`, error);
    return false;
  }
};

/**
 * Send order status update email
 * @param email User email
 * @param order Order data
 * @param status New status
 * @returns Promise with success status
 */
export const sendOrderStatusUpdateEmail = async (
  email: string,
  order: Order,
  status: OrderStatus
): Promise<boolean> => {
  try {
    // In a real app, this would use an email service

    // For now, just log it
    console.info(`[DEV] Sending order status update email to ${email} for order ${order.id} (${status})`);

    // Create a notification for the user
    let title = '';
    let message = '';
    let type: NotificationType = 'order';
    let actionUrl = `/orders/${order.id}`;
    let actionLabel = 'Ver Pedido';

    switch (status) {
      case 'in_progress':
        title = 'Pedido em Preparação';
        message = `Seu pedido #${order.id} está sendo preparado e logo sairá para entrega.`;
        break;
      case 'delivered':
        title = 'Pedido Entregue';
        message = `Seu pedido #${order.id} foi entregue com sucesso. Aproveite!`;
        actionLabel = 'Avaliar Pedido';
        break;
      case 'cancelled':
        title = 'Pedido Cancelado';
        message = `Seu pedido #${order.id} foi cancelado. Entre em contato conosco se precisar de ajuda.`;
        actionLabel = 'Falar com Suporte';
        actionUrl = '/support';
        break;
      default:
        title = 'Atualização de Pedido';
        message = `Seu pedido #${order.id} foi atualizado para ${status}.`;
    }

    // Create notification
    await createNotification({
      userId: order.userId || order.id.split('-')[0], // Use real user ID if available
      title,
      message,
      type,
      actionUrl,
      actionLabel,
      data: {
        orderId: order.id,
        status,
        shopName: order.shopName,
        total: order.total
      }
    });

    return true;
  } catch (error) {
    console.error(`Error sending order status update email to ${email}:`, error);
    return false;
  }
};

/**
 * Send delivery notification
 * @param email User email
 * @param order Order data
 * @param estimatedTime Estimated delivery time in minutes
 * @returns Promise with success status
 */
export const sendDeliveryNotification = async (
  email: string,
  order: Order,
  estimatedTime: number
): Promise<boolean> => {
  try {
    // In a real app, this would use an email service

    // For now, just log it
    console.info(`[DEV] Sending delivery notification to ${email} for order ${order.id} (ETA: ${estimatedTime} min)`);

    // Create a notification for the user
    await createNotification({
      userId: order.id.split('-')[0], // Mock user ID extraction
      title: 'Entrega em Andamento',
      message: `Seu pedido #${order.id} saiu para entrega e chegará em aproximadamente ${estimatedTime} minutos.`,
      type: 'delivery',
      data: { orderId: order.id, estimatedTime }
    });

    return true;
  } catch (error) {
    console.error(`Error sending delivery notification to ${email}:`, error);
    return false;
  }
};

/**
 * Create a notification
 * @param request Notification data
 * @returns Promise with created notification
 */
export const createNotification = async (
  request: CreateNotificationRequest
): Promise<Notification | null> => {
  try {
    // In a real app, this would insert into the database

    // For now, create a mock notification
    const now = new Date().toISOString();
    const newNotification: Notification = {
      id: `notif_${Date.now()}`,
      ...request,
      read: false,
      createdAt: now,
      expiresAt: request.expiresAt || undefined
    };

    mockNotifications.push(newNotification);

    // In a real app, we would also send push notifications here if enabled
    // For now, just log it
    console.info(`[DEV] Notification created: ${newNotification.title}`);

    return newNotification;
  } catch (error) {
    console.error('Error creating notification:', error);
    return null;
  }
};

/**
 * Delete a notification
 * @param notificationId Notification ID
 * @returns Promise with success status
 */
export const deleteNotification = async (notificationId: string): Promise<boolean> => {
  try {
    // In a real app, this would delete from the database

    // For now, update mock data
    const index = mockNotifications.findIndex(n => n.id === notificationId);
    if (index >= 0) {
      mockNotifications.splice(index, 1);
      return true;
    }

    return false;
  } catch (error) {
    console.error(`Error deleting notification ${notificationId}:`, error);
    return false;
  }
};

/**
 * Subscribe to real-time notifications
 * @param userId User ID
 * @param callback Callback function to handle new notifications
 * @returns Unsubscribe function
 */
export const subscribeToNotifications = (
  userId: string,
  callback: (notification: Notification) => void
): () => void => {
  try {
    // In a real app, this would use Supabase Realtime

    // For now, return a mock unsubscribe function
    console.info(`[DEV] Subscribed to notifications for user ${userId}`);

    return () => {
      console.info(`[DEV] Unsubscribed from notifications for user ${userId}`);
    };
  } catch (error) {
    console.error(`Error subscribing to notifications for user ${userId}:`, error);
    return () => {};
  }
};
