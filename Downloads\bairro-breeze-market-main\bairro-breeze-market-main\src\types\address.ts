/**
 * Representa um endereço completo
 */
export interface Address {
  id: string;
  userId: string;
  name: string; // Ex: "Casa", "Trabalho"
  street: string;
  number: string;
  complement?: string;
  neighborhood: string;
  city: string;
  state: string;
  zipCode: string;
  latitude?: number;
  longitude?: number;
  isDefault: boolean;
  createdAt: string;
  updatedAt?: string;
}

/**
 * Dados necessários para criar um novo endereço
 */
export interface CreateAddressRequest {
  userId: string;
  name: string;
  street: string;
  number: string;
  complement?: string;
  neighborhood: string;
  city: string;
  state: string;
  zipCode: string;
  latitude?: number;
  longitude?: number;
  isDefault?: boolean;
}

/**
 * Resposta de validação de endereço
 */
export interface AddressValidationResponse {
  isValid: boolean;
  formattedAddress?: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
  error?: {
    code: string;
    message: string;
  };
}

/**
 * Filtros para busca de endereços
 */
export interface AddressFilters {
  userId?: string;
  isDefault?: boolean;
}
