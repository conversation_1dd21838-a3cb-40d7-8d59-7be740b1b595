
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Clock,
  MapPin,
  Navigation,
  Package,
  TrendingUp,
  Truck,
  User,
} from "lucide-react";
import { DeliveryManagement } from "@/components/deliverer/DeliveryManagement";
import { EarningsAnalytics } from "@/components/deliverer/EarningsAnalytics";
import { CompletedDeliveries } from "@/components/deliverer/CompletedDeliveries";

interface DelivererDashboardProps {
  activeTab?: "current" | "pending" | "completed" | "earnings" | "map";
}

const DelivererDashboard = ({ activeTab = "current" }: DelivererDashboardProps) => {
  const [currentTab, setCurrentTab] = useState(activeTab);
  return (
    <div className="flex min-h-screen flex-col bg-gray-50">
      <header className="bg-trust border-b">
        <div className="container flex h-16 items-center justify-between px-4">
          <div className="flex items-center">
            <span className="text-xl font-bold text-white">Já Comprei</span>
            <span className="ml-2 rounded-md bg-white px-2 py-1 text-xs font-medium text-trust">
              Área do Entregador
            </span>
          </div>
          <div className="flex items-center space-x-4">
            <Badge variant="outline" className="text-white border-white flex items-center">
              <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
              Disponível
            </Badge>
            <Button variant="ghost" className="text-white">
              <User className="mr-2 h-4 w-4" />
              Meu Perfil
            </Button>
          </div>
        </div>
      </header>

      <main className="flex-1 container px-4 py-8">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Bem-vindo, Carlos</h1>
            <p className="text-muted-foreground">
              Gerencie suas entregas e acompanhe seus ganhos
            </p>
          </div>
          <div className="mt-4 md:mt-0">
            <Button className="bg-trust hover:bg-trust-dark">
              <Navigation className="mr-2 h-4 w-4" />
              Iniciar Rota
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3 mb-8">
          <div className="rounded-lg border bg-card p-4">
            <div className="flex items-center space-x-2">
              <Package className="h-4 w-4 text-trust" />
              <span className="text-sm font-medium">Entregas Hoje</span>
            </div>
            <div className="mt-2">
              <span className="text-2xl font-bold">12</span>
              <span className="ml-2 text-xs text-muted-foreground">4 pendentes</span>
            </div>
          </div>
          <div className="rounded-lg border bg-card p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-4 w-4 text-trust" />
              <span className="text-sm font-medium">Ganhos Hoje</span>
            </div>
            <div className="mt-2">
              <span className="text-2xl font-bold">R$ 120,00</span>
              <span className="ml-2 text-xs text-green-500">+10% </span>
            </div>
          </div>
          <div className="rounded-lg border bg-card p-4">
            <div className="flex items-center space-x-2">
              <Truck className="h-4 w-4 text-trust" />
              <span className="text-sm font-medium">Distância</span>
            </div>
            <div className="mt-2">
              <span className="text-2xl font-bold">24 km</span>
              <span className="ml-2 text-xs text-muted-foreground">hoje</span>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <Tabs value={currentTab} onValueChange={(value) => setCurrentTab(value as any)}>
          <TabsList className="mb-4">
            <TabsTrigger value="current">Entrega Atual</TabsTrigger>
            <TabsTrigger value="pending">Pendentes</TabsTrigger>
            <TabsTrigger value="completed">Concluídas</TabsTrigger>
            <TabsTrigger value="earnings">Ganhos</TabsTrigger>
          </TabsList>

          <TabsContent value="current" className="p-4 border rounded-lg">
            <div className="bg-white rounded-lg border p-4 mb-4">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="font-medium">Pedido #1234</h3>
                  <div className="flex items-center mt-1 text-sm text-muted-foreground">
                    <Clock className="mr-1 h-4 w-4" />
                    <span>Estimativa: 15min</span>
                  </div>
                </div>
                <Badge>Em Andamento</Badge>
              </div>

              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-trust-light mr-3">
                    <MapPin className="h-5 w-5 text-trust" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">Mercado do Bairro</p>
                    <p className="text-xs text-muted-foreground">Rua das Flores, 123 - Jardim Primavera</p>
                  </div>
                </div>

                <div className="ml-5 h-10 border-l-2 border-dashed border-muted-foreground"></div>

                <div className="flex items-start">
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-cta-light mr-3">
                    <MapPin className="h-5 w-5 text-cta" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">Maria Silva</p>
                    <p className="text-xs text-muted-foreground">Av. das Palmeiras, 456 - Apto 302</p>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-2 mt-6">
                <Button variant="outline" size="sm">
                  Ver Detalhes
                </Button>
                <Button size="sm" className="bg-cta hover:bg-cta-dark">
                  Confirmar Entrega
                </Button>
              </div>
            </div>

            <div className="bg-muted p-4 rounded-lg text-center">
              <p className="text-sm text-muted-foreground">Mapa de rota será exibido aqui</p>
            </div>
          </TabsContent>

          <TabsContent value="pending" className="p-4 border rounded-lg">
            <h2 className="text-lg font-medium mb-4">Entregas Pendentes</h2>
            <DeliveryManagement />
          </TabsContent>

          <TabsContent value="completed" className="p-4 border rounded-lg">
            <h2 className="text-lg font-medium mb-4">Histórico de Entregas</h2>
            <CompletedDeliveries />
          </TabsContent>

          <TabsContent value="earnings" className="p-4 border rounded-lg">
            <EarningsAnalytics />
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
};

export default DelivererDashboard;
