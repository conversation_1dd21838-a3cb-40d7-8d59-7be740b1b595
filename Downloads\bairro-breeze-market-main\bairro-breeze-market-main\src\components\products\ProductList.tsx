
import { useQuery } from "@tanstack/react-query";
import { fetchProducts } from "@/services/products";
import { ProductCard } from "@/components/ui/product-card";
import { Loader2 } from "lucide-react";

export const ProductList = () => {
  const { data: products, isLoading } = useQuery({
    queryKey: ["products"],
    queryFn: fetchProducts,
  });

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader2 className="h-6 w-6 animate-spin" />
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
      {products?.map((product) => (
        <ProductCard
          key={product.id}
          id={product.id}
          name={product.name}
          image={product.image}
          price={product.price}
          originalPrice={product.originalPrice}
          shopName={product.shopName}
          isPromo={product.isPromo}
        />
      ))}
    </div>
  );
};
