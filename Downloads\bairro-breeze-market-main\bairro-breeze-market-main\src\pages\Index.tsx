import { useState } from "react";
import { Navbar } from "@/components/ui/navbar";
import { BottomNav } from "@/components/ui/bottom-nav";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { AddressBar } from "@/components/ui/address-bar";
import { SearchBar } from "@/components/ui/search-bar";
import { ProductList } from "@/components/products/ProductList";
import { Apple, Beef, Coffee, Fish, Pizza, ShoppingBag, Truck, Utensils, Gift, ChevronRight } from "lucide-react";
import { motion } from "framer-motion";
import { AnimatedPage, AnimatedList, FadeIn } from "@/components/animations";
import { CategoryCarousel } from "@/components/home/<USER>";
import { PromotionBanner } from "@/components/home/<USER>";
import { ShopCard } from "@/components/home/<USER>";
import { Link } from "react-router-dom";

// Mock data for categories with images
const categories = [
  {
    id: "1",
    name: "Merca<PERSON>",
    icon: "https://cdn-icons-png.flaticon.com/512/3724/3724763.png",
    color: "eco"
  },
  {
    id: "2",
    name: "Restaurantes",
    icon: "https://cdn-icons-png.flaticon.com/512/2424/2424848.png",
    color: "cta"
  },
  {
    id: "3",
    name: "Pizzarias",
    icon: "https://cdn-icons-png.flaticon.com/512/3132/3132693.png",
    color: "rose"
  },
  {
    id: "4",
    name: "Cafeterias",
    icon: "https://cdn-icons-png.flaticon.com/512/2935/2935307.png",
    color: "trust"
  },
  {
    id: "5",
    name: "Açougues",
    icon: "https://cdn-icons-png.flaticon.com/512/3082/3082051.png",
    color: "cta"
  },
  {
    id: "6",
    name: "Peixarias",
    icon: "https://cdn-icons-png.flaticon.com/512/2970/2970014.png",
    color: "trust"
  },
  {
    id: "7",
    name: "Frutarias",
    icon: "https://cdn-icons-png.flaticon.com/512/3194/3194591.png",
    color: "eco"
  },
  {
    id: "8",
    name: "Presentes",
    icon: "https://cdn-icons-png.flaticon.com/512/2589/2589175.png",
    color: "purple"
  },
];

// Mock data for shops
const shops = [
  {
    id: "1",
    name: "Mercado do Bairro",
    image: "https://images.unsplash.com/photo-1618160702438-9b02ab6515c9",
    category: "Mercado",
    deliveryTime: "15-30 min",
    rating: 4.8,
    deliveryFee: 5.0,
    featured: true,
  },
  {
    id: "2",
    name: "Padaria São José",
    image: "https://images.unsplash.com/photo-1608198093002-ad4e005484ec",
    category: "Padaria",
    deliveryTime: "10-20 min",
    rating: 4.5,
    deliveryFee: 3.5,
    featured: false,
  },
  {
    id: "3",
    name: "Açougue Premium",
    image: "https://images.unsplash.com/photo-1582562124811-c09040d0a901",
    category: "Açougue",
    deliveryTime: "20-35 min",
    rating: 4.7,
    deliveryFee: 6.0,
    featured: false,
  },
  {
    id: "4",
    name: "Feira da Terra",
    image: "https://images.unsplash.com/photo-1506484381205-f7945653044d",
    category: "Feira",
    deliveryTime: "25-40 min",
    rating: 4.6,
    deliveryFee: 4.5,
    featured: true,
  },
];

// Promotion banners data
const promotions = [
  {
    id: "1",
    title: "Frutas Frescas",
    description: "Receba frutas orgânicas com até 20% de desconto",
    image: "https://cdn-icons-png.flaticon.com/512/3194/3194591.png",
    link: "/category/fruits",
    colorFrom: "from-eco-100",
    colorTo: "to-eco-300",
  },
  {
    id: "2",
    title: "Super Ofertas",
    description: "Produtos essenciais com preços imperdíveis",
    image: "https://cdn-icons-png.flaticon.com/512/2331/2331966.png",
    link: "/promotions",
    colorFrom: "from-cta-100",
    colorTo: "to-cta-300",
  }
];

const products = [
  {
    name: "Caixa de Morangos Orgânicos 500g",
    image: "https://images.unsplash.com/photo-1465146344425-f00d5f5c8f07",
    price: 15.9,
    originalPrice: 19.9,
    shopName: "Feira da Terra",
    isPromo: true,
  },
  {
    name: "Filé Mignon Premium kg",
    image: "https://images.unsplash.com/photo-1582562124811-c09040d0a901",
    price: 69.9,
    shopName: "Açougue Premium",
  },
  {
    name: "Café Especial Torrado 250g",
    image: "https://images.unsplash.com/photo-1721322800607-8c38375eef04",
    price: 24.9,
    originalPrice: 29.9,
    shopName: "Padaria São José",
    isPromo: true,
  },
  {
    name: "Pão Artesanal Integral",
    image: "https://images.unsplash.com/photo-1721322800607-8c38375eef04",
    price: 12.5,
    shopName: "Padaria São José",
  },
];

const Index = () => {
  const [activeTab, setActiveTab] = useState<"shops" | "products">("shops");

  return (
    <AnimatedPage className="flex flex-col min-h-screen bg-gray-50">
      <Navbar />
      <AddressBar />

      <main className="flex-1 container px-4 py-6 pb-20 md:pb-6">
        {/* Search bar for mobile */}
        <FadeIn className="md:hidden mb-6" delay={0.1}>
          <SearchBar />
        </FadeIn>

        {/* Welcome Banner */}
        <FadeIn className="mb-6" delay={0.2}>
          <motion.div
            className="relative overflow-hidden rounded-xl bg-gradient-to-r from-trust-600 to-trust-800 text-white p-6"
            whileHover={{ scale: 1.01 }}
            transition={{ type: "spring", stiffness: 300, damping: 20 }}
          >
            <div className="relative z-10">
              <h1 className="text-2xl font-bold mb-2">Bem-vindo ao Já Comprei!</h1>
              <p className="text-sm mb-4">
                Apoie o comércio local e receba em minutos.
              </p>
              <div className="flex items-center space-x-2">
                <Button className="bg-white text-trust-800 hover:bg-white/90">
                  Explorar Agora
                </Button>
                <div className="flex items-center">
                  <Truck className="mr-1 h-4 w-4" />
                  <span className="text-xs">Entrega rápida</span>
                </div>
              </div>
              <div className="flex items-center space-x-2 mt-2 text-xs text-white opacity-80">
                <Link to="/merchant" className="underline">Sou comerciante</Link>
                <span>•</span>
                <Link to="/deliverer" className="underline">Sou entregador</Link>
              </div>
            </div>
            <div
              className="absolute inset-0 bg-gradient-to-r from-trust-dark to-transparent"
              style={{
                backgroundImage: "url('https://images.unsplash.com/photo-1506744038136-46273834b3fb')",
                backgroundSize: "cover",
                backgroundPosition: "center",
                opacity: 0.2
              }}
            />

            {/* Decorative elements */}
            <div className="absolute top-0 right-0 w-32 h-32 rounded-full bg-white/10 -mr-10 -mt-10" />
            <div className="absolute bottom-0 left-0 w-24 h-24 rounded-full bg-white/10 -ml-10 -mb-10" />
          </motion.div>
        </FadeIn>

        {/* Categories */}
        <FadeIn className="mb-8" delay={0.3}>
          <CategoryCarousel categories={categories} />
        </FadeIn>

        {/* Promotions */}
        <FadeIn className="mb-8" delay={0.4}>
          <div className="space-y-4">
            {promotions.map((promo) => (
              <PromotionBanner
                key={promo.id}
                title={promo.title}
                description={promo.description}
                image={promo.image}
                link={promo.link}
                colorFrom={promo.colorFrom}
                colorTo={promo.colorTo}
              />
            ))}
          </div>
        </FadeIn>

        {/* Content Tabs */}
        <FadeIn delay={0.5}>
          <div className="flex border-b mb-6">
            <motion.button
              className={`pb-2 px-4 text-sm font-medium relative ${
                activeTab === "shops"
                  ? "text-cta"
                  : "text-muted-foreground"
              }`}
              onClick={() => setActiveTab("shops")}
            >
              Lojas Próximas
              {activeTab === "shops" && (
                <motion.div
                  className="absolute bottom-0 left-0 right-0 h-0.5 bg-cta"
                  layoutId="activeTab"
                />
              )}
            </motion.button>
            <motion.button
              className={`pb-2 px-4 text-sm font-medium relative ${
                activeTab === "products"
                  ? "text-cta"
                  : "text-muted-foreground"
              }`}
              onClick={() => setActiveTab("products")}
            >
              Produtos em Destaque
              {activeTab === "products" && (
                <motion.div
                  className="absolute bottom-0 left-0 right-0 h-0.5 bg-cta"
                  layoutId="activeTab"
                />
              )}
            </motion.button>
          </div>
        </FadeIn>

        {/* Shops List */}
        {activeTab === "shops" && (
          <FadeIn delay={0.6}>
            <section>
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-bold">Lojas Populares</h2>
                <Link to="/shops" className="text-sm text-cta flex items-center">
                  Ver todas
                  <ChevronRight className="h-4 w-4 ml-1" />
                </Link>
              </div>
              <AnimatedList className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {shops.map((shop) => (
                  <ShopCard
                    key={shop.id}
                    id={shop.id}
                    name={shop.name}
                    image={shop.image}
                    category={shop.category}
                    rating={shop.rating}
                    deliveryTime={shop.deliveryTime}
                    deliveryFee={shop.deliveryFee}
                    featured={shop.featured}
                  />
                ))}
              </AnimatedList>
            </section>
          </FadeIn>
        )}

        {/* Products List */}
        {activeTab === "products" && (
          <FadeIn delay={0.6}>
            <section>
              <ProductList />
            </section>
          </FadeIn>
        )}
      </main>

      <BottomNav />
    </AnimatedPage>
  );
};

export default Index;
