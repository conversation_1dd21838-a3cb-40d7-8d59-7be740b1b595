import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { 
  CreditCard, 
  Loader2, 
  Check, 
  Plus, 
  Trash2, 
  Star, 
  QrCode, 
  Banknote 
} from "lucide-react";
import { 
  getPaymentMethods, 
  addPaymentMethod, 
  removePaymentMethod, 
  setDefaultPaymentMethod,
  PaymentMethod
} from "@/services/payment";
import { useAuth } from "@/hooks/useAuth";
import { motion } from "framer-motion";
import { toast } from "@/hooks/use-toast";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";

interface PaymentFormProps {
  onPaymentMethodSelect: (method: string) => void;
  onComplete: () => void;
}

export function PaymentForm({ onPaymentMethodSelect, onComplete }: PaymentFormProps) {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [selectedMethodId, setSelectedMethodId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMethods, setIsLoadingMethods] = useState(false);
  const [isAddCardDialogOpen, setIsAddCardDialogOpen] = useState(false);
  
  // New card form state
  const [cardNumber, setCardNumber] = useState("");
  const [cardName, setCardName] = useState("");
  const [expiryMonth, setExpiryMonth] = useState("");
  const [expiryYear, setExpiryYear] = useState("");
  const [cvv, setCvv] = useState("");
  const [saveCard, setSaveCard] = useState(true);
  
  const { user } = useAuth();

  // Load saved payment methods
  useEffect(() => {
    const loadPaymentMethods = async () => {
      if (!user) return;
      
      setIsLoadingMethods(true);
      try {
        const methods = await getPaymentMethods(user.id);
        setPaymentMethods(methods);
        
        // Select default method if available
        const defaultMethod = methods.find(method => method.isDefault);
        if (defaultMethod) {
          setSelectedMethodId(defaultMethod.id);
          onPaymentMethodSelect(defaultMethod.id);
        } else if (methods.length > 0) {
          setSelectedMethodId(methods[0].id);
          onPaymentMethodSelect(methods[0].id);
        }
      } catch (error) {
        console.error("Error loading payment methods:", error);
        toast({
          title: "Erro ao carregar métodos de pagamento",
          description: "Não foi possível carregar seus métodos de pagamento salvos.",
          variant: "destructive"
        });
      } finally {
        setIsLoadingMethods(false);
      }
    };
    
    loadPaymentMethods();
  }, [user, onPaymentMethodSelect]);

  // Handle payment method selection
  const handleMethodSelect = (methodId: string) => {
    setSelectedMethodId(methodId);
    onPaymentMethodSelect(methodId);
    
    const selectedMethod = paymentMethods.find(method => method.id === methodId);
    if (selectedMethod && !selectedMethod.isDefault) {
      setDefaultPaymentMethod(user?.id || "", methodId).catch(error => {
        console.error("Error setting default payment method:", error);
      });
    }
  };

  // Format card number with spaces
  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, "").replace(/[^0-9]/gi, "");
    const matches = v.match(/\d{4,16}/g);
    const match = (matches && matches[0]) || "";
    const parts = [];
    
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    
    if (parts.length) {
      return parts.join(" ");
    } else {
      return value;
    }
  };

  // Handle card number input
  const handleCardNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formattedValue = formatCardNumber(e.target.value);
    setCardNumber(formattedValue);
  };

  // Add new card
  const handleAddCard = async () => {
    // Validate card details
    if (cardNumber.replace(/\s/g, "").length < 16) {
      toast({
        title: "Número de cartão inválido",
        description: "Por favor, insira um número de cartão válido.",
        variant: "destructive"
      });
      return;
    }
    
    if (!cardName) {
      toast({
        title: "Nome no cartão inválido",
        description: "Por favor, insira o nome como aparece no cartão.",
        variant: "destructive"
      });
      return;
    }
    
    if (!expiryMonth || !expiryYear || parseInt(expiryMonth) > 12) {
      toast({
        title: "Data de validade inválida",
        description: "Por favor, insira uma data de validade válida.",
        variant: "destructive"
      });
      return;
    }
    
    if (cvv.length < 3) {
      toast({
        title: "CVV inválido",
        description: "Por favor, insira um código de segurança válido.",
        variant: "destructive"
      });
      return;
    }
    
    setIsLoading(true);
    
    try {
      // In a real app, this would tokenize the card with a payment processor
      const last4 = cardNumber.replace(/\s/g, "").slice(-4);
      const cardBrand = getCardBrand(cardNumber);
      
      const newCard = await addPaymentMethod(user?.id || "", {
        type: "credit_card",
        last4,
        brand: cardBrand,
        expiryMonth: parseInt(expiryMonth),
        expiryYear: parseInt(expiryYear),
        holderName: cardName
      });
      
      if (newCard) {
        toast({
          title: "Cartão adicionado",
          description: "Seu cartão foi adicionado com sucesso.",
        });
        
        // Update payment methods
        setPaymentMethods(prev => [...prev, newCard]);
        setSelectedMethodId(newCard.id);
        onPaymentMethodSelect(newCard.id);
        
        // Close dialog
        setIsAddCardDialogOpen(false);
        
        // Reset form
        setCardNumber("");
        setCardName("");
        setExpiryMonth("");
        setExpiryYear("");
        setCvv("");
      }
    } catch (error) {
      console.error("Error adding card:", error);
      toast({
        title: "Erro ao adicionar cartão",
        description: "Ocorreu um erro ao adicionar o cartão. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Remove payment method
  const handleRemoveMethod = async (methodId: string) => {
    if (!user) return;
    
    try {
      const success = await removePaymentMethod(user.id, methodId);
      
      if (success) {
        toast({
          title: "Método de pagamento removido",
          description: "Seu método de pagamento foi removido com sucesso.",
        });
        
        // Update payment methods
        setPaymentMethods(prev => prev.filter(method => method.id !== methodId));
        
        // If the removed method was selected, select another one
        if (selectedMethodId === methodId) {
          const defaultMethod = paymentMethods.find(method => method.id !== methodId);
          if (defaultMethod) {
            setSelectedMethodId(defaultMethod.id);
            onPaymentMethodSelect(defaultMethod.id);
          } else {
            setSelectedMethodId(null);
          }
        }
      }
    } catch (error) {
      console.error("Error removing payment method:", error);
      toast({
        title: "Erro ao remover método de pagamento",
        description: "Ocorreu um erro ao remover o método de pagamento. Tente novamente.",
        variant: "destructive"
      });
    }
  };

  // Continue with selected payment method
  const handleContinue = () => {
    if (selectedMethodId) {
      onComplete();
    } else {
      toast({
        title: "Selecione um método de pagamento",
        description: "Por favor, selecione um método de pagamento para continuar.",
        variant: "destructive"
      });
    }
  };

  // Get card brand based on number
  const getCardBrand = (number: string): string => {
    const cleanNumber = number.replace(/\s+/g, "");
    
    if (/^4/.test(cleanNumber)) return "Visa";
    if (/^5[1-5]/.test(cleanNumber)) return "Mastercard";
    if (/^3[47]/.test(cleanNumber)) return "American Express";
    if (/^6(?:011|5)/.test(cleanNumber)) return "Discover";
    
    return "Desconhecido";
  };

  // Get card icon based on brand
  const getCardIcon = (brand: string) => {
    switch (brand.toLowerCase()) {
      case "visa":
        return "💳"; // Replace with actual icons in a real app
      case "mastercard":
        return "💳";
      case "american express":
        return "💳";
      case "discover":
        return "💳";
      default:
        return "💳";
    }
  };

  if (isLoadingMethods) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-cta" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <RadioGroup value={selectedMethodId || ""} onValueChange={handleMethodSelect}>
        <div className="space-y-3">
          {/* Saved credit cards */}
          {paymentMethods.filter(method => method.type === "credit_card").map((method) => (
            <motion.div
              key={method.id}
              className="flex items-start space-x-3 border rounded-md p-3"
              whileHover={{ backgroundColor: "#f9fafb" }}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
            >
              <RadioGroupItem value={method.id} id={`method-${method.id}`} className="mt-1" />
              <div className="flex-1">
                <div className="flex justify-between">
                  <Label htmlFor={`method-${method.id}`} className="font-medium cursor-pointer flex items-center">
                    <CreditCard className="mr-2 h-4 w-4" />
                    {method.brand} •••• {method.last4}
                    {method.isDefault && (
                      <span className="ml-2 text-xs bg-eco-light text-eco px-2 py-0.5 rounded">
                        Padrão
                      </span>
                    )}
                  </Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0"
                    onClick={(e) => {
                      e.preventDefault();
                      handleRemoveMethod(method.id);
                    }}
                  >
                    <Trash2 className="h-4 w-4 text-muted-foreground" />
                  </Button>
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  {method.holderName} • Expira em {method.expiryMonth}/{method.expiryYear}
                </p>
              </div>
            </motion.div>
          ))}
          
          {/* PIX option */}
          <motion.div
            className="flex items-start space-x-3 border rounded-md p-3"
            whileHover={{ backgroundColor: "#f9fafb" }}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <RadioGroupItem value="pix" id="method-pix" className="mt-1" />
            <div className="flex-1">
              <Label htmlFor="method-pix" className="font-medium cursor-pointer flex items-center">
                <QrCode className="mr-2 h-4 w-4" />
                PIX
              </Label>
              <p className="text-sm text-muted-foreground mt-1">
                Pagamento instantâneo
              </p>
            </div>
          </motion.div>
          
          {/* Cash option */}
          <motion.div
            className="flex items-start space-x-3 border rounded-md p-3"
            whileHover={{ backgroundColor: "#f9fafb" }}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
          >
            <RadioGroupItem value="cash" id="method-cash" className="mt-1" />
            <div className="flex-1">
              <Label htmlFor="method-cash" className="font-medium cursor-pointer flex items-center">
                <Banknote className="mr-2 h-4 w-4" />
                Dinheiro
              </Label>
              <p className="text-sm text-muted-foreground mt-1">
                Pague na entrega
              </p>
            </div>
          </motion.div>
        </div>
      </RadioGroup>
      
      <div className="flex justify-between mt-4">
        <Dialog open={isAddCardDialogOpen} onOpenChange={setIsAddCardDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="outline" type="button">
              <Plus className="mr-2 h-4 w-4" />
              Adicionar cartão
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Adicionar novo cartão</DialogTitle>
              <DialogDescription>
                Adicione um novo cartão de crédito para pagamentos futuros.
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4 py-4">
              <div>
                <Label htmlFor="cardNumber">Número do cartão</Label>
                <Input
                  id="cardNumber"
                  value={cardNumber}
                  onChange={handleCardNumberChange}
                  placeholder="0000 0000 0000 0000"
                  maxLength={19}
                />
              </div>
              
              <div>
                <Label htmlFor="cardName">Nome no cartão</Label>
                <Input
                  id="cardName"
                  value={cardName}
                  onChange={(e) => setCardName(e.target.value)}
                  placeholder="Como aparece no cartão"
                />
              </div>
              
              <div className="grid grid-cols-3 gap-3">
                <div>
                  <Label htmlFor="expiryMonth">Mês</Label>
                  <Input
                    id="expiryMonth"
                    value={expiryMonth}
                    onChange={(e) => setExpiryMonth(e.target.value.replace(/\D/g, ""))}
                    placeholder="MM"
                    maxLength={2}
                  />
                </div>
                <div>
                  <Label htmlFor="expiryYear">Ano</Label>
                  <Input
                    id="expiryYear"
                    value={expiryYear}
                    onChange={(e) => setExpiryYear(e.target.value.replace(/\D/g, ""))}
                    placeholder="AA"
                    maxLength={2}
                  />
                </div>
                <div>
                  <Label htmlFor="cvv">CVV</Label>
                  <Input
                    id="cvv"
                    value={cvv}
                    onChange={(e) => setCvv(e.target.value.replace(/\D/g, ""))}
                    placeholder="123"
                    maxLength={4}
                  />
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="saveCard" 
                  checked={saveCard}
                  onCheckedChange={(checked) => setSaveCard(checked as boolean)}
                />
                <Label htmlFor="saveCard">Salvar cartão para compras futuras</Label>
              </div>
            </div>
            
            <DialogFooter>
              <Button 
                variant="outline" 
                onClick={() => setIsAddCardDialogOpen(false)}
              >
                Cancelar
              </Button>
              <Button 
                className="bg-cta hover:bg-cta-dark"
                onClick={handleAddCard}
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processando...
                  </>
                ) : (
                  "Adicionar cartão"
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        
        <Button 
          type="button"
          className="bg-cta hover:bg-cta-dark"
          onClick={handleContinue}
        >
          Continuar
        </Button>
      </div>
    </div>
  );
}
