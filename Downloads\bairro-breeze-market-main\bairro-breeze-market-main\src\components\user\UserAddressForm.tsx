import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  Card<PERSON>ooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog";
import { 
  MapPin, 
  Plus, 
  Edit, 
  Trash2, 
  Home, 
  Building, 
  Briefcase, 
  Loader2 
} from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

// Interface for address
interface Address {
  id: string;
  userId: string;
  name: string;
  street: string;
  number: string;
  complement?: string;
  neighborhood: string;
  city: string;
  state: string;
  zipCode: string;
  isDefault: boolean;
  type: "home" | "work" | "other";
}

// Mock addresses
const mockAddresses: Address[] = [
  {
    id: "addr_1",
    userId: "user1",
    name: "Casa",
    street: "Rua das Flores",
    number: "123",
    complement: "Apto 101",
    neighborhood: "Centro",
    city: "São Paulo",
    state: "SP",
    zipCode: "01000-000",
    isDefault: true,
    type: "home"
  },
  {
    id: "addr_2",
    userId: "user1",
    name: "Trabalho",
    street: "Av. Paulista",
    number: "1000",
    neighborhood: "Bela Vista",
    city: "São Paulo",
    state: "SP",
    zipCode: "01310-100",
    isDefault: false,
    type: "work"
  }
];

export function UserAddressForm() {
  const { user } = useAuth();
  
  const [isLoading, setIsLoading] = useState(true);
  const [addresses, setAddresses] = useState<Address[]>([]);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedAddress, setSelectedAddress] = useState<Address | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  
  // Form state
  const [addressName, setAddressName] = useState("");
  const [addressType, setAddressType] = useState<"home" | "work" | "other">("home");
  const [street, setStreet] = useState("");
  const [number, setNumber] = useState("");
  const [complement, setComplement] = useState("");
  const [neighborhood, setNeighborhood] = useState("");
  const [city, setCity] = useState("");
  const [state, setState] = useState("");
  const [zipCode, setZipCode] = useState("");
  const [isDefault, setIsDefault] = useState(false);
  
  // Load addresses
  useEffect(() => {
    const loadAddresses = async () => {
      if (!user) return;
      
      setIsLoading(true);
      
      try {
        // In a real app, this would call an API to get the user's addresses
        // For now, use mock data
        setTimeout(() => {
          setAddresses(mockAddresses);
          setIsLoading(false);
        }, 1000);
      } catch (error) {
        console.error("Error loading addresses:", error);
        toast({
          title: "Erro ao carregar endereços",
          description: "Não foi possível carregar seus endereços. Tente novamente mais tarde.",
          variant: "destructive",
        });
        setIsLoading(false);
      }
    };
    
    loadAddresses();
  }, [user]);
  
  // Reset form
  const resetForm = () => {
    setAddressName("");
    setAddressType("home");
    setStreet("");
    setNumber("");
    setComplement("");
    setNeighborhood("");
    setCity("");
    setState("");
    setZipCode("");
    setIsDefault(false);
  };
  
  // Set form values from address
  const setFormFromAddress = (address: Address) => {
    setAddressName(address.name);
    setAddressType(address.type);
    setStreet(address.street);
    setNumber(address.number);
    setComplement(address.complement || "");
    setNeighborhood(address.neighborhood);
    setCity(address.city);
    setState(address.state);
    setZipCode(address.zipCode);
    setIsDefault(address.isDefault);
  };
  
  // Handle add address
  const handleAddAddress = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user) {
      toast({
        title: "Erro",
        description: "Você precisa estar logado para adicionar um endereço.",
        variant: "destructive",
      });
      return;
    }
    
    setIsSaving(true);
    
    try {
      // In a real app, this would call an API to add the address
      // For now, simulate API call
      setTimeout(() => {
        const newAddress: Address = {
          id: `addr_${Date.now()}`,
          userId: user.id,
          name: addressName,
          type: addressType,
          street,
          number,
          complement: complement || undefined,
          neighborhood,
          city,
          state,
          zipCode,
          isDefault,
        };
        
        // If this is the default address, update other addresses
        let updatedAddresses = [...addresses];
        if (isDefault) {
          updatedAddresses = updatedAddresses.map(addr => ({
            ...addr,
            isDefault: false,
          }));
        }
        
        // Add new address
        setAddresses([...updatedAddresses, newAddress]);
        
        toast({
          title: "Endereço adicionado",
          description: "Seu endereço foi adicionado com sucesso.",
        });
        
        resetForm();
        setIsAddDialogOpen(false);
        setIsSaving(false);
      }, 1000);
    } catch (error) {
      console.error("Error adding address:", error);
      toast({
        title: "Erro ao adicionar endereço",
        description: "Não foi possível adicionar seu endereço. Tente novamente mais tarde.",
        variant: "destructive",
      });
      setIsSaving(false);
    }
  };
  
  // Handle edit address
  const handleEditAddress = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user || !selectedAddress) {
      toast({
        title: "Erro",
        description: "Você precisa estar logado para editar um endereço.",
        variant: "destructive",
      });
      return;
    }
    
    setIsSaving(true);
    
    try {
      // In a real app, this would call an API to update the address
      // For now, simulate API call
      setTimeout(() => {
        const updatedAddress: Address = {
          ...selectedAddress,
          name: addressName,
          type: addressType,
          street,
          number,
          complement: complement || undefined,
          neighborhood,
          city,
          state,
          zipCode,
          isDefault,
        };
        
        // If this is the default address, update other addresses
        let updatedAddresses = addresses.map(addr => 
          addr.id === selectedAddress.id 
            ? updatedAddress 
            : isDefault ? { ...addr, isDefault: false } : addr
        );
        
        setAddresses(updatedAddresses);
        
        toast({
          title: "Endereço atualizado",
          description: "Seu endereço foi atualizado com sucesso.",
        });
        
        resetForm();
        setIsEditDialogOpen(false);
        setSelectedAddress(null);
        setIsSaving(false);
      }, 1000);
    } catch (error) {
      console.error("Error updating address:", error);
      toast({
        title: "Erro ao atualizar endereço",
        description: "Não foi possível atualizar seu endereço. Tente novamente mais tarde.",
        variant: "destructive",
      });
      setIsSaving(false);
    }
  };
  
  // Handle delete address
  const handleDeleteAddress = async () => {
    if (!user || !selectedAddress) {
      toast({
        title: "Erro",
        description: "Você precisa estar logado para remover um endereço.",
        variant: "destructive",
      });
      return;
    }
    
    setIsSaving(true);
    
    try {
      // In a real app, this would call an API to delete the address
      // For now, simulate API call
      setTimeout(() => {
        const updatedAddresses = addresses.filter(addr => addr.id !== selectedAddress.id);
        
        setAddresses(updatedAddresses);
        
        toast({
          title: "Endereço removido",
          description: "Seu endereço foi removido com sucesso.",
        });
        
        setIsDeleteDialogOpen(false);
        setSelectedAddress(null);
        setIsSaving(false);
      }, 1000);
    } catch (error) {
      console.error("Error deleting address:", error);
      toast({
        title: "Erro ao remover endereço",
        description: "Não foi possível remover seu endereço. Tente novamente mais tarde.",
        variant: "destructive",
      });
      setIsSaving(false);
    }
  };
  
  // Get address type icon
  const getAddressTypeIcon = (type: Address["type"]) => {
    switch (type) {
      case "home":
        return <Home className="h-4 w-4" />;
      case "work":
        return <Briefcase className="h-4 w-4" />;
      case "other":
        return <Building className="h-4 w-4" />;
      default:
        return <MapPin className="h-4 w-4" />;
    }
  };
  
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Meus Endereços</CardTitle>
              <CardDescription>
                Gerencie seus endereços de entrega
              </CardDescription>
            </div>
            <Button 
              className="bg-cta hover:bg-cta-dark"
              onClick={() => {
                resetForm();
                setIsAddDialogOpen(true);
              }}
            >
              <Plus className="h-4 w-4 mr-2" />
              Adicionar
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {addresses.length === 0 ? (
            <div className="text-center py-8">
              <MapPin className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-1">Nenhum endereço cadastrado</h3>
              <p className="text-muted-foreground mb-4">
                Adicione um endereço para facilitar suas compras
              </p>
              <Button 
                className="bg-cta hover:bg-cta-dark"
                onClick={() => {
                  resetForm();
                  setIsAddDialogOpen(true);
                }}
              >
                <Plus className="h-4 w-4 mr-2" />
                Adicionar Endereço
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {addresses.map((address) => (
                <div 
                  key={address.id} 
                  className={`border rounded-lg p-4 ${address.isDefault ? "border-cta bg-cta/5" : ""}`}
                >
                  <div className="flex justify-between items-start">
                    <div className="flex items-start">
                      <div className={`p-2 rounded-full mr-3 ${address.isDefault ? "bg-cta-light" : "bg-muted"}`}>
                        {getAddressTypeIcon(address.type)}
                      </div>
                      <div>
                        <div className="flex items-center">
                          <p className="font-medium">{address.name}</p>
                          {address.isDefault && (
                            <span className="ml-2 text-xs bg-cta text-white px-2 py-0.5 rounded-full">
                              Principal
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground">
                          {address.street}, {address.number}
                          {address.complement && `, ${address.complement}`}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {address.neighborhood}, {address.city} - {address.state}
                        </p>
                        <p className="text-sm text-muted-foreground">
                          {address.zipCode}
                        </p>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => {
                          setSelectedAddress(address);
                          setFormFromAddress(address);
                          setIsEditDialogOpen(true);
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="text-destructive"
                        onClick={() => {
                          setSelectedAddress(address);
                          setIsDeleteDialogOpen(true);
                        }}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* Add Address Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Adicionar Endereço</DialogTitle>
            <DialogDescription>
              Preencha os detalhes do novo endereço
            </DialogDescription>
          </DialogHeader>
          
          <form onSubmit={handleAddAddress} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="addressName">Nome do endereço</Label>
                  <Input
                    id="addressName"
                    value={addressName}
                    onChange={(e) => setAddressName(e.target.value)}
                    placeholder="Ex: Casa, Trabalho"
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>Tipo de endereço</Label>
                  <RadioGroup
                    value={addressType}
                    onValueChange={(value) => setAddressType(value as "home" | "work" | "other")}
                    className="flex space-x-4"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="home" id="home" />
                      <Label htmlFor="home" className="flex items-center">
                        <Home className="h-4 w-4 mr-1" />
                        Casa
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="work" id="work" />
                      <Label htmlFor="work" className="flex items-center">
                        <Briefcase className="h-4 w-4 mr-1" />
                        Trabalho
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="other" id="other" />
                      <Label htmlFor="other" className="flex items-center">
                        <Building className="h-4 w-4 mr-1" />
                        Outro
                      </Label>
                    </div>
                  </RadioGroup>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="street">Rua</Label>
                  <Input
                    id="street"
                    value={street}
                    onChange={(e) => setStreet(e.target.value)}
                    placeholder="Nome da rua"
                    required
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="number">Número</Label>
                    <Input
                      id="number"
                      value={number}
                      onChange={(e) => setNumber(e.target.value)}
                      placeholder="123"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="complement">Complemento</Label>
                    <Input
                      id="complement"
                      value={complement}
                      onChange={(e) => setComplement(e.target.value)}
                      placeholder="Apto, Bloco, etc."
                    />
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="neighborhood">Bairro</Label>
                  <Input
                    id="neighborhood"
                    value={neighborhood}
                    onChange={(e) => setNeighborhood(e.target.value)}
                    placeholder="Nome do bairro"
                    required
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="city">Cidade</Label>
                    <Input
                      id="city"
                      value={city}
                      onChange={(e) => setCity(e.target.value)}
                      placeholder="Nome da cidade"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="state">Estado</Label>
                    <Input
                      id="state"
                      value={state}
                      onChange={(e) => setState(e.target.value)}
                      placeholder="UF"
                      maxLength={2}
                      required
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="zipCode">CEP</Label>
                  <Input
                    id="zipCode"
                    value={zipCode}
                    onChange={(e) => setZipCode(e.target.value)}
                    placeholder="00000-000"
                    required
                  />
                </div>
                
                <div className="flex items-center space-x-2 pt-4">
                  <input
                    type="checkbox"
                    id="isDefault"
                    checked={isDefault}
                    onChange={(e) => setIsDefault(e.target.checked)}
                    className="h-4 w-4 rounded border-gray-300 text-cta focus:ring-cta"
                  />
                  <Label htmlFor="isDefault">Definir como endereço principal</Label>
                </div>
              </div>
            </div>
            
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsAddDialogOpen(false)}
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                className="bg-cta hover:bg-cta-dark"
                disabled={isSaving}
              >
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Salvando...
                  </>
                ) : (
                  <>
                    <Plus className="mr-2 h-4 w-4" />
                    Adicionar Endereço
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
      
      {/* Edit Address Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Editar Endereço</DialogTitle>
            <DialogDescription>
              Atualize os detalhes do endereço
            </DialogDescription>
          </DialogHeader>
          
          <form onSubmit={handleEditAddress} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-addressName">Nome do endereço</Label>
                  <Input
                    id="edit-addressName"
                    value={addressName}
                    onChange={(e) => setAddressName(e.target.value)}
                    placeholder="Ex: Casa, Trabalho"
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>Tipo de endereço</Label>
                  <RadioGroup
                    value={addressType}
                    onValueChange={(value) => setAddressType(value as "home" | "work" | "other")}
                    className="flex space-x-4"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="home" id="edit-home" />
                      <Label htmlFor="edit-home" className="flex items-center">
                        <Home className="h-4 w-4 mr-1" />
                        Casa
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="work" id="edit-work" />
                      <Label htmlFor="edit-work" className="flex items-center">
                        <Briefcase className="h-4 w-4 mr-1" />
                        Trabalho
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="other" id="edit-other" />
                      <Label htmlFor="edit-other" className="flex items-center">
                        <Building className="h-4 w-4 mr-1" />
                        Outro
                      </Label>
                    </div>
                  </RadioGroup>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="edit-street">Rua</Label>
                  <Input
                    id="edit-street"
                    value={street}
                    onChange={(e) => setStreet(e.target.value)}
                    placeholder="Nome da rua"
                    required
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="edit-number">Número</Label>
                    <Input
                      id="edit-number"
                      value={number}
                      onChange={(e) => setNumber(e.target.value)}
                      placeholder="123"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-complement">Complemento</Label>
                    <Input
                      id="edit-complement"
                      value={complement}
                      onChange={(e) => setComplement(e.target.value)}
                      placeholder="Apto, Bloco, etc."
                    />
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-neighborhood">Bairro</Label>
                  <Input
                    id="edit-neighborhood"
                    value={neighborhood}
                    onChange={(e) => setNeighborhood(e.target.value)}
                    placeholder="Nome do bairro"
                    required
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="edit-city">Cidade</Label>
                    <Input
                      id="edit-city"
                      value={city}
                      onChange={(e) => setCity(e.target.value)}
                      placeholder="Nome da cidade"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-state">Estado</Label>
                    <Input
                      id="edit-state"
                      value={state}
                      onChange={(e) => setState(e.target.value)}
                      placeholder="UF"
                      maxLength={2}
                      required
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="edit-zipCode">CEP</Label>
                  <Input
                    id="edit-zipCode"
                    value={zipCode}
                    onChange={(e) => setZipCode(e.target.value)}
                    placeholder="00000-000"
                    required
                  />
                </div>
                
                <div className="flex items-center space-x-2 pt-4">
                  <input
                    type="checkbox"
                    id="edit-isDefault"
                    checked={isDefault}
                    onChange={(e) => setIsDefault(e.target.checked)}
                    className="h-4 w-4 rounded border-gray-300 text-cta focus:ring-cta"
                  />
                  <Label htmlFor="edit-isDefault">Definir como endereço principal</Label>
                </div>
              </div>
            </div>
            
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsEditDialogOpen(false)}
              >
                Cancelar
              </Button>
              <Button
                type="submit"
                className="bg-cta hover:bg-cta-dark"
                disabled={isSaving}
              >
                {isSaving ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Salvando...
                  </>
                ) : (
                  <>
                    <Edit className="mr-2 h-4 w-4" />
                    Salvar Alterações
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
      
      {/* Delete Address Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Remover Endereço</DialogTitle>
            <DialogDescription>
              Tem certeza que deseja remover este endereço? Esta ação não pode ser desfeita.
            </DialogDescription>
          </DialogHeader>
          
          {selectedAddress && (
            <div className="py-4">
              <div className="flex items-start">
                <div className={`p-2 rounded-full mr-3 ${selectedAddress.isDefault ? "bg-cta-light" : "bg-muted"}`}>
                  {getAddressTypeIcon(selectedAddress.type)}
                </div>
                <div>
                  <div className="flex items-center">
                    <p className="font-medium">{selectedAddress.name}</p>
                    {selectedAddress.isDefault && (
                      <span className="ml-2 text-xs bg-cta text-white px-2 py-0.5 rounded-full">
                        Principal
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {selectedAddress.street}, {selectedAddress.number}
                    {selectedAddress.complement && `, ${selectedAddress.complement}`}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {selectedAddress.neighborhood}, {selectedAddress.city} - {selectedAddress.state}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    {selectedAddress.zipCode}
                  </p>
                </div>
              </div>
            </div>
          )}
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancelar
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteAddress}
              disabled={isSaving}
            >
              {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Removendo...
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Remover Endereço
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
