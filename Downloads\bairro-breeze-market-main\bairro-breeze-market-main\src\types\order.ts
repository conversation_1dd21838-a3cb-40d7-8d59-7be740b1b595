/**
 * Representa um item dentro de um pedido
 */
export interface OrderItem {
  productId: string;
  quantity: number;
  price: number;
  name: string;
  image?: string;
}

/**
 * Status possíveis para um pedido
 */
export type OrderStatus = 'pending' | 'in_progress' | 'delivered' | 'cancelled';

/**
 * Status possíveis para um pagamento
 */
export type PaymentStatus = 'pending' | 'paid' | 'failed' | 'refunded';

/**
 * Representa um pedido completo
 */
export interface Order {
  id: string;
  status: OrderStatus;
  items: OrderItem[];
  total: number;
  createdAt: string;
  updatedAt?: string;
  shopId?: string;
  shopName: string;
  userId?: string;
  userName?: string;
  deliveryAddress: string;
  deliveryInstructions?: string;
  delivererId?: string;
  delivererName?: string;
  estimatedDeliveryTime?: number; // em minutos
  paymentMethod: string;
  paymentStatus?: PaymentStatus;
  paymentId?: string;
  trackingCode?: string;
}

/**
 * Dados necessários para criar um novo pedido
 */
export interface CreateOrderRequest {
  items: OrderItem[];
  total: number;
  deliveryAddress: string;
  deliveryInstructions?: string;
  paymentMethod: string;
  userId: string;
  shopId?: string;
}
