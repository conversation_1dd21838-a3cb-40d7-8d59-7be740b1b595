import React from 'react';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { ShoppingBag, Store, Truck, User, LogIn } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';

export default function SimpleIndex() {
  const { user } = useAuth();

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4 text-eco">Já Comprei</h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          Marketplace local que conecta clientes, comerciantes e entregadores para facilitar compras e entregas rápidas no bairro.
        </p>
      </div>

      {!user ? (
        <div className="flex flex-col items-center mb-12">
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <Button asChild size="lg" className="bg-eco hover:bg-eco-dark">
              <Link to="/register">Criar uma conta</Link>
            </Button>
            <Button asChild variant="outline" size="lg">
              <Link to="/login">Entrar</Link>
            </Button>
          </div>
          <p className="text-sm text-muted-foreground">
            Já tem uma conta? <Link to="/login" className="text-eco hover:underline">Faça login</Link>
          </p>
        </div>
      ) : (
        <div className="flex justify-center mb-12">
          <Button asChild size="lg" className="bg-eco hover:bg-eco-dark">
            <Link to={user.profile?.role === 'customer' ? '/' : `/${user.profile?.role}`}>
              Ir para o Dashboard
            </Link>
          </Button>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
        <Card>
          <CardHeader>
            <div className="w-12 h-12 rounded-full bg-eco-light flex items-center justify-center mb-4">
              <ShoppingBag className="h-6 w-6 text-eco" />
            </div>
            <CardTitle>Para Clientes</CardTitle>
            <CardDescription>
              Encontre produtos de lojas locais e receba em minutos
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center">
                <span className="w-2 h-2 rounded-full bg-eco mr-2"></span>
                Descubra lojas próximas
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 rounded-full bg-eco mr-2"></span>
                Acompanhe entregas em tempo real
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 rounded-full bg-eco mr-2"></span>
                Receba produtos em minutos
              </li>
            </ul>
          </CardContent>
          <CardFooter>
            <Button asChild variant="outline" className="w-full">
              <Link to="/register?role=customer">
                Cadastre-se como Cliente
              </Link>
            </Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <div className="w-12 h-12 rounded-full bg-cta-light flex items-center justify-center mb-4">
              <Store className="h-6 w-6 text-cta" />
            </div>
            <CardTitle>Para Comerciantes</CardTitle>
            <CardDescription>
              Venda seus produtos online e aumente seu faturamento
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center">
                <span className="w-2 h-2 rounded-full bg-cta mr-2"></span>
                Gerencie seu catálogo online
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 rounded-full bg-cta mr-2"></span>
                Receba pedidos em tempo real
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 rounded-full bg-cta mr-2"></span>
                Acompanhe vendas e desempenho
              </li>
            </ul>
          </CardContent>
          <CardFooter>
            <Button asChild variant="outline" className="w-full">
              <Link to="/register?role=merchant">
                Cadastre-se como Comerciante
              </Link>
            </Button>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <div className="w-12 h-12 rounded-full bg-trust-light flex items-center justify-center mb-4">
              <Truck className="h-6 w-6 text-trust" />
            </div>
            <CardTitle>Para Entregadores</CardTitle>
            <CardDescription>
              Faça entregas quando quiser e ganhe dinheiro extra
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center">
                <span className="w-2 h-2 rounded-full bg-trust mr-2"></span>
                Escolha seus horários
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 rounded-full bg-trust mr-2"></span>
                Receba por entrega realizada
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 rounded-full bg-trust mr-2"></span>
                Acompanhe seus ganhos
              </li>
            </ul>
          </CardContent>
          <CardFooter>
            <Button asChild variant="outline" className="w-full">
              <Link to="/register?role=deliverer">
                Cadastre-se como Entregador
              </Link>
            </Button>
          </CardFooter>
        </Card>
      </div>

      <div className="text-center">
        <h2 className="text-2xl font-bold mb-4">Como funciona</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
          <div className="flex flex-col items-center">
            <div className="w-16 h-16 rounded-full bg-eco-light flex items-center justify-center mb-4">
              <ShoppingBag className="h-8 w-8 text-eco" />
            </div>
            <h3 className="text-lg font-medium mb-2">1. Escolha produtos</h3>
            <p className="text-sm text-muted-foreground">
              Navegue por lojas locais e adicione produtos ao carrinho
            </p>
          </div>
          
          <div className="flex flex-col items-center">
            <div className="w-16 h-16 rounded-full bg-cta-light flex items-center justify-center mb-4">
              <Store className="h-8 w-8 text-cta" />
            </div>
            <h3 className="text-lg font-medium mb-2">2. Loja prepara</h3>
            <p className="text-sm text-muted-foreground">
              A loja recebe seu pedido e prepara os produtos
            </p>
          </div>
          
          <div className="flex flex-col items-center">
            <div className="w-16 h-16 rounded-full bg-trust-light flex items-center justify-center mb-4">
              <Truck className="h-8 w-8 text-trust" />
            </div>
            <h3 className="text-lg font-medium mb-2">3. Entrega rápida</h3>
            <p className="text-sm text-muted-foreground">
              Um entregador leva os produtos até você em minutos
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
