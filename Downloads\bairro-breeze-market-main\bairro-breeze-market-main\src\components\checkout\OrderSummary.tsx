import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { ShoppingBag, CheckCircle, Loader2 } from "lucide-react";
import { useCart } from "@/hooks/useCart";
import { useAuth } from "@/hooks/useAuth";
import { createOrder } from "@/services/orders";
import { processPayment } from "@/services/payment";
import { toast } from "@/hooks/use-toast";
import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import { sendOrderConfirmationEmail } from "@/services/notification";

interface OrderSummaryProps {
  deliveryAddress: string;
  paymentMethodId: string;
  onComplete: () => void;
}

export function OrderSummary({ deliveryAddress, paymentMethodId, onComplete }: OrderSummaryProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const { items, subtotal, clearCart } = useCart();
  const { user } = useAuth();
  const navigate = useNavigate();
  
  const deliveryFee = 5.0;
  const discount = items.some(item => item.price < (item as any).originalPrice) ? 4.0 : 0;
  const total = subtotal + deliveryFee - discount;

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  // Process order
  const processOrder = async () => {
    if (!user) {
      toast({
        title: "Usuário não autenticado",
        description: "Você precisa estar logado para finalizar a compra.",
        variant: "destructive"
      });
      navigate("/login");
      return;
    }
    
    setIsProcessing(true);
    
    try {
      // Create order
      const order = await createOrder({
        items,
        total,
        deliveryAddress,
        paymentMethod: paymentMethodId,
        user
      });
      
      if (!order) {
        throw new Error("Falha ao criar pedido");
      }
      
      // Process payment
      const payment = await processPayment(
        order.id,
        total,
        paymentMethodId
      );
      
      if (!payment) {
        throw new Error("Falha ao processar pagamento");
      }
      
      // Send confirmation email
      await sendOrderConfirmationEmail(user.email, order);
      
      // Clear cart
      clearCart();
      
      // Show success message
      toast({
        title: "Pedido realizado com sucesso!",
        description: "Seu pedido foi recebido e está sendo processado.",
      });
      
      // Complete checkout
      onComplete();
      
      // Navigate to orders page
      setTimeout(() => {
        navigate('/orders');
      }, 1500);
    } catch (error) {
      console.error("Error processing order:", error);
      toast({
        title: "Erro ao finalizar pedido",
        description: "Ocorreu um erro ao processar seu pedido. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Resumo do pedido</h3>
        
        <div className="space-y-4 bg-white p-4 rounded-lg border">
          {/* Items */}
          <div className="space-y-3">
            {items.map((item) => (
              <div key={item.id} className="flex justify-between">
                <div className="flex items-start">
                  <span className="text-muted-foreground mr-2">{item.quantity}x</span>
                  <span>{item.name}</span>
                </div>
                <span>{formatCurrency(item.price * item.quantity)}</span>
              </div>
            ))}
          </div>
          
          <Separator />
          
          {/* Totals */}
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Subtotal</span>
              <span>{formatCurrency(subtotal)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Taxa de entrega</span>
              <span>{formatCurrency(deliveryFee)}</span>
            </div>
            {discount > 0 && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">Desconto</span>
                <span className="text-eco">-{formatCurrency(discount)}</span>
              </div>
            )}
            <Separator />
            <div className="flex justify-between font-bold">
              <span>Total</span>
              <span>{formatCurrency(total)}</span>
            </div>
          </div>
        </div>
        
        {/* Delivery address */}
        <div className="bg-white p-4 rounded-lg border">
          <h4 className="font-medium mb-2">Endereço de entrega</h4>
          <p className="text-sm text-muted-foreground">{deliveryAddress}</p>
        </div>
        
        {/* Payment method */}
        <div className="bg-white p-4 rounded-lg border">
          <h4 className="font-medium mb-2">Forma de pagamento</h4>
          <p className="text-sm text-muted-foreground">
            {paymentMethodId === "pix" ? "PIX" : 
             paymentMethodId === "cash" ? "Dinheiro na entrega" : 
             "Cartão de crédito"}
          </p>
        </div>
        
        {/* Confirm button */}
        <Button 
          className="w-full h-12 text-lg bg-cta hover:bg-cta-dark text-white"
          onClick={processOrder}
          disabled={isProcessing}
        >
          {isProcessing ? (
            <div className="flex items-center">
              <motion.div
                className="mr-2 h-4 w-4 border-2 border-white border-t-transparent rounded-full"
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              />
              Processando...
            </div>
          ) : (
            `Finalizar Compra • ${formatCurrency(total)}`
          )}
        </Button>
      </div>
    </div>
  );
}
