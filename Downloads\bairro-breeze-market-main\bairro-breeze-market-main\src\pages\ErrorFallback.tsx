import React from 'react';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface ErrorFallbackProps {
  error?: Error;
  resetErrorBoundary?: () => void;
}

export default function ErrorFallback({ error, resetErrorBoundary }: ErrorFallbackProps) {
  const navigate = useNavigate();

  const handleReset = () => {
    // Limpar qualquer estado que possa estar causando o erro
    localStorage.removeItem('lastError');
    
    // Se houver uma função para resetar o erro, chamá-la
    if (resetErrorBoundary) {
      resetErrorBoundary();
    } else {
      // Caso contrário, recarregar a página
      window.location.reload();
    }
  };

  const handleGoHome = () => {
    // Navegar para a página inicial
    navigate('/');
    
    // Se houver uma função para resetar o erro, chamá-la
    if (resetErrorBoundary) {
      resetErrorBoundary();
    }
  };

  // Salvar o erro no localStorage para debugging
  if (error) {
    try {
      localStorage.setItem('lastError', JSON.stringify({
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      }));
    } catch (e) {
      console.error('Erro ao salvar erro no localStorage:', e);
    }
  }

  return (
    <div className="flex items-center justify-center min-h-screen bg-background p-4">
      <div className="max-w-md w-full bg-card p-6 rounded-lg shadow-lg text-center">
        <div className="w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center mx-auto mb-4">
          <AlertTriangle className="h-8 w-8 text-destructive" />
        </div>
        
        <h1 className="text-2xl font-bold mb-2">Ops! Algo deu errado</h1>
        
        <p className="text-muted-foreground mb-6">
          Encontramos um problema ao carregar esta página. Isso pode ser um erro temporário.
        </p>
        
        {error && (
          <div className="bg-muted p-3 rounded-md mb-6 text-left overflow-auto max-h-32">
            <p className="text-sm font-mono">{error.message}</p>
          </div>
        )}
        
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Button onClick={handleReset} className="flex items-center">
            <RefreshCw className="mr-2 h-4 w-4" />
            Tentar novamente
          </Button>
          
          <Button variant="outline" onClick={handleGoHome} className="flex items-center">
            <Home className="mr-2 h-4 w-4" />
            Ir para a página inicial
          </Button>
        </div>
        
        <p className="text-xs text-muted-foreground mt-6">
          Se o problema persistir, entre em contato com o suporte.
        </p>
      </div>
    </div>
  );
}
