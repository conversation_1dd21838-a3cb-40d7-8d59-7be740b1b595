import { useState, useEffect, createContext, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { supabaseClient } from '@/lib/supabase';
import { toast } from '@/hooks/use-toast';
import { Tables } from '@/types/database';

export interface AuthUser {
  id: string;
  email: string;
  user_metadata: {
    name?: string;
    role?: 'customer' | 'merchant' | 'deliverer';
  };
  profile?: Tables<'profiles'> | null;
}

interface AuthContextType {
  user: AuthUser | null;
  loading: boolean;
  signIn: (email: string, password: string, role?: 'customer' | 'merchant' | 'deliverer') => Promise<void>;
  signUp: (email: string, password: string, name: string, role: string) => Promise<void>;
  signOut: () => Promise<void>;
  updateProfile: (data: Partial<Tables<'profiles'>>) => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  redirectBasedOnRole: (role?: 'customer' | 'merchant' | 'deliverer') => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  const isDevelopmentMode = process.env.NODE_ENV === 'development' && !import.meta.env.VITE_SUPABASE_URL;

  useEffect(() => {
    // Get the initial session
    supabaseClient.auth.getSession().then(async ({ data: { session } }) => {
      if (session?.user) {
        // Fetch user profile from profiles table
        const { data: profile } = await supabaseClient
          .from('profiles')
          .select('*')
          .eq('id', session.user.id)
          .single();

        setUser({
          id: session.user.id,
          email: session.user.email || '',
          user_metadata: session.user.user_metadata,
          profile
        });
      }
      setLoading(false);
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabaseClient.auth.onAuthStateChange(async (_event, session) => {
      if (session?.user) {
        // Fetch user profile from profiles table
        const { data: profile } = await supabaseClient
          .from('profiles')
          .select('*')
          .eq('id', session.user.id)
          .single();

        setUser({
          id: session.user.id,
          email: session.user.email || '',
          user_metadata: session.user.user_metadata,
          profile
        });
      } else {
        setUser(null);
      }
    });

    return () => subscription?.unsubscribe();
  }, [isDevelopmentMode]);

  /**
   * Redireciona o usuário para a página apropriada com base em seu perfil
   */
  const redirectBasedOnRole = (role?: 'customer' | 'merchant' | 'deliverer') => {
    switch (role) {
      case 'merchant':
        navigate('/merchant');
        break;
      case 'deliverer':
        navigate('/deliverer');
        break;
      case 'customer':
      default:
        navigate('/');
        break;
    }
  };

  /**
   * Realiza o login do usuário
   */
  const signIn = async (email: string, password: string, role?: 'customer' | 'merchant' | 'deliverer') => {
    try {
      if (!email || !password) {
        throw new Error("Email e senha são obrigatórios");
      }

      if (isDevelopmentMode) {
        // No modo de desenvolvimento, permitimos escolher o perfil no login
        const userRole = role || 'customer';

        toast({
          title: "Modo de desenvolvimento",
          description: `Login simulado como ${userRole} realizado com sucesso.`,
        });

        setUser({
          id: 'dev-user-id',
          email: email,
          user_metadata: {
            name: 'Usuário de Teste',
            role: userRole
          }
        });

        redirectBasedOnRole(userRole);
        return;
      }

      const { error, data } = await supabaseClient.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;

      // Redirecionar com base no perfil do usuário
      if (data?.user) {
        const userRole = data.user.user_metadata?.role as 'customer' | 'merchant' | 'deliverer';
        redirectBasedOnRole(userRole);

        toast({
          title: "Login realizado com sucesso",
          description: `Bem-vindo de volta!`,
        });
      } else {
        navigate('/');
      }
    } catch (error: any) {
      console.error('Erro de autenticação:', error);

      // Mensagens de erro mais amigáveis
      let errorMessage = "Falha na autenticação";

      if (error.message.includes("Invalid login")) {
        errorMessage = "Email ou senha incorretos";
      } else if (error.message.includes("Email")) {
        errorMessage = "Email inválido ou não registrado";
      } else if (error.message.includes("Password")) {
        errorMessage = "Senha incorreta";
      }

      toast({
        title: "Erro ao entrar",
        description: errorMessage,
        variant: "destructive"
      });
      throw error;
    }
  };

  /**
   * Registra um novo usuário
   */
  const signUp = async (email: string, password: string, name: string, role: string) => {
    try {
      // Validação de campos
      if (!email || !password || !name || !role) {
        throw new Error("Todos os campos são obrigatórios");
      }

      if (password.length < 6) {
        throw new Error("A senha deve ter pelo menos 6 caracteres");
      }

      // Cast do role para o tipo correto
      const typedRole = role as 'customer' | 'merchant' | 'deliverer';

      // Valida o tipo de perfil
      if (!['customer', 'merchant', 'deliverer'].includes(typedRole)) {
        throw new Error("Tipo de perfil inválido");
      }

      if (isDevelopmentMode) {
        toast({
          title: "Modo de desenvolvimento",
          description: `Registro simulado como ${typedRole} realizado com sucesso.`,
        });

        setUser({
          id: 'dev-user-id',
          email: email,
          user_metadata: {
            name: name,
            role: typedRole
          }
        });

        // Redirecionar com base no perfil
        redirectBasedOnRole(typedRole);
        return;
      }

      const { error, data } = await supabaseClient.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
            role,
          },
        },
      });
      if (error) throw error;

      // Create profile record
      if (data?.user) {
        const { error: profileError } = await supabaseClient
          .from('profiles')
          .insert({
            id: data.user.id,
            name,
            role: typedRole,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          });

        if (profileError) {
          console.error('Error creating profile:', profileError);
          // Tenta continuar mesmo com erro no perfil
        }
      }

      toast({
        title: "Registro realizado com sucesso",
        description: "Verifique seu e-mail para confirmar o cadastro. Você já pode fazer login."
      });

      // Após o registro, redirecionar para a página de login
      navigate('/login', { state: { email, role: typedRole } });
    } catch (error: any) {
      console.error('Erro no registro:', error);

      // Mensagens de erro mais amigáveis
      let errorMessage = "Falha ao registrar";

      if (error.message.includes("email") || error.message.includes("Email")) {
        errorMessage = "Email inválido ou já registrado";
      } else if (error.message.includes("password") || error.message.includes("senha")) {
        errorMessage = "A senha não atende aos requisitos mínimos";
      }

      toast({
        title: "Erro no registro",
        description: errorMessage,
        variant: "destructive"
      });
      throw error;
    }
  };

  const signOut = async () => {
    try {
      if (isDevelopmentMode) {
        setUser(null);
        navigate('/login');
        return;
      }

      const { error } = await supabaseClient.auth.signOut();
      if (error) throw error;
      navigate('/login');
    } catch (error: any) {
      toast({
        title: "Erro ao sair",
        description: error.message || "Falha ao fazer logout",
        variant: "destructive"
      });
      throw error;
    }
  };

  // Update user profile
  const updateProfile = async (data: Partial<Tables<'profiles'>>) => {
    try {
      if (!user) throw new Error('Usuário não autenticado');

      const { error } = await supabaseClient
        .from('profiles')
        .update({
          ...data,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id);

      if (error) throw error;

      // Update local user state with new profile data
      setUser(prev => prev ? {
        ...prev,
        profile: prev.profile ? { ...prev.profile, ...data } : null
      } : null);

      toast({
        title: "Perfil atualizado",
        description: "Suas informações foram atualizadas com sucesso."
      });
    } catch (error: any) {
      toast({
        title: "Erro ao atualizar perfil",
        description: error.message || "Falha ao atualizar informações",
        variant: "destructive"
      });
      throw error;
    }
  };

  // Reset password
  const resetPassword = async (email: string) => {
    try {
      if (isDevelopmentMode) {
        toast({
          title: "Modo de desenvolvimento",
          description: "Email de recuperação simulado enviado com sucesso.",
        });
        return;
      }

      const { error } = await supabaseClient.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      });

      if (error) throw error;

      toast({
        title: "Email enviado",
        description: "Verifique seu email para redefinir sua senha."
      });
    } catch (error: any) {
      toast({
        title: "Erro ao enviar email",
        description: error.message || "Falha ao solicitar redefinição de senha",
        variant: "destructive"
      });
      throw error;
    }
  };

  return (
    <AuthContext.Provider value={{
      user,
      loading,
      signIn,
      signUp,
      signOut,
      updateProfile,
      resetPassword,
      redirectBasedOnRole
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth deve ser usado dentro de um AuthProvider');
  }
  return context;
};
