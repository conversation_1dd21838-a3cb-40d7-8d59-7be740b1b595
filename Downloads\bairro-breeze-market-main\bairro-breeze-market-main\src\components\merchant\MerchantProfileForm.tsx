import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  Avatar, 
  AvatarFallback, 
  AvatarImage 
} from "@/components/ui/avatar";
import { 
  User, 
  Phone, 
  MapPin, 
  Upload, 
  Loader2,
  Store,
  Clock,
  Calendar,
  Info
} from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";

interface MerchantProfile {
  id: string;
  name: string;
  description: string;
  phone: string;
  address: string;
  category: string;
  openingHours: {
    monday: { open: string; close: string; isOpen: boolean };
    tuesday: { open: string; close: string; isOpen: boolean };
    wednesday: { open: string; close: string; isOpen: boolean };
    thursday: { open: string; close: string; isOpen: boolean };
    friday: { open: string; close: string; isOpen: boolean };
    saturday: { open: string; close: string; isOpen: boolean };
    sunday: { open: string; close: string; isOpen: boolean };
  };
  avatarUrl?: string;
  coverImageUrl?: string;
  minOrderValue?: number;
  deliveryFee?: number;
  deliveryTime?: string;
  tags?: string[];
}

// Mock function to fetch merchant profile
const fetchMerchantProfile = async (userId: string): Promise<MerchantProfile> => {
  // Simulate API call
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        id: userId,
        name: "Mercado do Bairro",
        description: "Supermercado de bairro com produtos frescos e preços acessíveis.",
        phone: "(11) 99999-9999",
        address: "Rua das Flores, 123, Centro, São Paulo, SP",
        category: "grocery",
        openingHours: {
          monday: { open: "08:00", close: "20:00", isOpen: true },
          tuesday: { open: "08:00", close: "20:00", isOpen: true },
          wednesday: { open: "08:00", close: "20:00", isOpen: true },
          thursday: { open: "08:00", close: "20:00", isOpen: true },
          friday: { open: "08:00", close: "20:00", isOpen: true },
          saturday: { open: "08:00", close: "14:00", isOpen: true },
          sunday: { open: "08:00", close: "12:00", isOpen: false },
        },
        avatarUrl: "",
        coverImageUrl: "",
        minOrderValue: 20,
        deliveryFee: 5,
        deliveryTime: "30-45",
        tags: ["Mercearia", "Hortifruti", "Padaria"]
      });
    }, 1000);
  });
};

// Mock function to update merchant profile
const updateMerchantProfile = async (userId: string, data: Partial<MerchantProfile>): Promise<MerchantProfile> => {
  // Simulate API call
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        id: userId,
        ...data,
      } as MerchantProfile);
    }, 1000);
  });
};

export function MerchantProfileForm() {
  const { user } = useAuth();
  
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [profile, setProfile] = useState<MerchantProfile | null>(null);
  
  // Form state
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const [phone, setPhone] = useState("");
  const [address, setAddress] = useState("");
  const [category, setCategory] = useState("");
  const [openingHours, setOpeningHours] = useState<MerchantProfile["openingHours"] | null>(null);
  const [minOrderValue, setMinOrderValue] = useState<number | undefined>(undefined);
  const [deliveryFee, setDeliveryFee] = useState<number | undefined>(undefined);
  const [deliveryTime, setDeliveryTime] = useState("");
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);
  const [coverImageFile, setCoverImageFile] = useState<File | null>(null);
  const [coverImagePreview, setCoverImagePreview] = useState<string | null>(null);
  
  // Load merchant profile
  useEffect(() => {
    const loadProfile = async () => {
      if (!user) return;
      
      setIsLoading(true);
      
      try {
        const merchantProfile = await fetchMerchantProfile(user.id);
        
        if (merchantProfile) {
          setProfile(merchantProfile);
          setName(merchantProfile.name || "");
          setDescription(merchantProfile.description || "");
          setPhone(merchantProfile.phone || "");
          setAddress(merchantProfile.address || "");
          setCategory(merchantProfile.category || "");
          setOpeningHours(merchantProfile.openingHours || null);
          setMinOrderValue(merchantProfile.minOrderValue);
          setDeliveryFee(merchantProfile.deliveryFee);
          setDeliveryTime(merchantProfile.deliveryTime || "");
          setAvatarPreview(merchantProfile.avatarUrl || null);
          setCoverImagePreview(merchantProfile.coverImageUrl || null);
        }
      } catch (error) {
        console.error("Error loading merchant profile:", error);
        toast({
          title: "Erro ao carregar perfil",
          description: "Não foi possível carregar as informações da sua loja. Tente novamente mais tarde.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    loadProfile();
  }, [user]);
  
  // Handle avatar change
  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    
    if (file) {
      // Check file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: "Arquivo muito grande",
          description: "O tamanho máximo permitido é 5MB.",
          variant: "destructive",
        });
        return;
      }
      
      // Check file type
      if (!file.type.startsWith("image/")) {
        toast({
          title: "Tipo de arquivo inválido",
          description: "Por favor, selecione uma imagem.",
          variant: "destructive",
        });
        return;
      }
      
      setAvatarFile(file);
      
      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setAvatarPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };
  
  // Handle cover image change
  const handleCoverImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    
    if (file) {
      // Check file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: "Arquivo muito grande",
          description: "O tamanho máximo permitido é 5MB.",
          variant: "destructive",
        });
        return;
      }
      
      // Check file type
      if (!file.type.startsWith("image/")) {
        toast({
          title: "Tipo de arquivo inválido",
          description: "Por favor, selecione uma imagem.",
          variant: "destructive",
        });
        return;
      }
      
      setCoverImageFile(file);
      
      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setCoverImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };
  
  // Handle opening hours change
  const handleOpeningHoursChange = (
    day: keyof MerchantProfile["openingHours"],
    field: "open" | "close" | "isOpen",
    value: string | boolean
  ) => {
    if (!openingHours) return;
    
    setOpeningHours({
      ...openingHours,
      [day]: {
        ...openingHours[day],
        [field]: value
      }
    });
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user) {
      toast({
        title: "Erro",
        description: "Você precisa estar logado para atualizar seu perfil.",
        variant: "destructive",
      });
      return;
    }
    
    setIsSaving(true);
    
    try {
      // Update profile
      const updatedProfile = await updateMerchantProfile(user.id, {
        name,
        description,
        phone,
        address,
        category,
        openingHours: openingHours || undefined,
        minOrderValue,
        deliveryFee,
        deliveryTime,
      });
      
      if (updatedProfile) {
        setProfile(updatedProfile);
        
        toast({
          title: "Perfil atualizado",
          description: "As informações da sua loja foram atualizadas com sucesso.",
        });
      }
    } catch (error) {
      console.error("Error updating merchant profile:", error);
      toast({
        title: "Erro ao atualizar perfil",
        description: "Não foi possível atualizar as informações da sua loja. Tente novamente mais tarde.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };
  
  // Get initials for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };
  
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      <form onSubmit={handleSubmit}>
        <Card>
          <CardHeader>
            <CardTitle>Informações da Loja</CardTitle>
            <CardDescription>
              Atualize as informações da sua loja
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Cover Image */}
            <div className="relative h-40 w-full rounded-md overflow-hidden bg-muted mb-6">
              {coverImagePreview ? (
                <img
                  src={coverImagePreview}
                  alt="Capa da loja"
                  className="h-full w-full object-cover"
                />
              ) : (
                <div className="h-full w-full flex items-center justify-center bg-muted">
                  <Store className="h-12 w-12 text-muted-foreground" />
                </div>
              )}
              <div className="absolute bottom-2 right-2">
                <input
                  type="file"
                  id="coverImage"
                  className="hidden"
                  accept="image/*"
                  onChange={handleCoverImageChange}
                />
                <Button
                  type="button"
                  variant="secondary"
                  size="sm"
                  onClick={() => document.getElementById("coverImage")?.click()}
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Alterar capa
                </Button>
              </div>
            </div>
            
            {/* Avatar */}
            <div className="flex flex-col items-center">
              <Avatar className="h-24 w-24 mb-4">
                <AvatarImage src={avatarPreview || undefined} />
                <AvatarFallback>{getInitials(name || "Loja")}</AvatarFallback>
              </Avatar>
              <div className="flex items-center">
                <input
                  type="file"
                  id="avatar"
                  className="hidden"
                  accept="image/*"
                  onChange={handleAvatarChange}
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => document.getElementById("avatar")?.click()}
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Alterar logo
                </Button>
              </div>
            </div>
            
            {/* Basic Info */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name" className="flex items-center">
                  <Store className="h-4 w-4 mr-2 text-muted-foreground" />
                  Nome da loja
                </Label>
                <Input
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="Nome da sua loja"
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="description" className="flex items-center">
                  <Info className="h-4 w-4 mr-2 text-muted-foreground" />
                  Descrição
                </Label>
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Descreva sua loja em poucas palavras"
                  rows={3}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="category" className="flex items-center">
                  <Store className="h-4 w-4 mr-2 text-muted-foreground" />
                  Categoria
                </Label>
                <Select
                  value={category}
                  onValueChange={setCategory}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione uma categoria" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="grocery">Supermercado</SelectItem>
                    <SelectItem value="restaurant">Restaurante</SelectItem>
                    <SelectItem value="pharmacy">Farmácia</SelectItem>
                    <SelectItem value="petshop">Pet Shop</SelectItem>
                    <SelectItem value="convenience">Conveniência</SelectItem>
                    <SelectItem value="bakery">Padaria</SelectItem>
                    <SelectItem value="other">Outro</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="phone" className="flex items-center">
                  <Phone className="h-4 w-4 mr-2 text-muted-foreground" />
                  Telefone
                </Label>
                <Input
                  id="phone"
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                  placeholder="(00) 00000-0000"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="address" className="flex items-center">
                  <MapPin className="h-4 w-4 mr-2 text-muted-foreground" />
                  Endereço
                </Label>
                <Textarea
                  id="address"
                  value={address}
                  onChange={(e) => setAddress(e.target.value)}
                  placeholder="Rua, número, complemento, bairro, cidade, estado, CEP"
                  rows={3}
                />
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Horário de Funcionamento</CardTitle>
            <CardDescription>
              Configure os horários de funcionamento da sua loja
            </CardDescription>
          </CardHeader>
          <CardContent>
            {openingHours && (
              <div className="space-y-4">
                {Object.entries(openingHours).map(([day, hours]) => (
                  <div key={day} className="flex items-center justify-between border-b pb-4 last:border-0 last:pb-0">
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                      <span className="font-medium">
                        {day === "monday" && "Segunda-feira"}
                        {day === "tuesday" && "Terça-feira"}
                        {day === "wednesday" && "Quarta-feira"}
                        {day === "thursday" && "Quinta-feira"}
                        {day === "friday" && "Sexta-feira"}
                        {day === "saturday" && "Sábado"}
                        {day === "sunday" && "Domingo"}
                      </span>
                    </div>
                    
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={hours.isOpen}
                          onCheckedChange={(checked) => 
                            handleOpeningHoursChange(
                              day as keyof MerchantProfile["openingHours"],
                              "isOpen",
                              checked
                            )
                          }
                        />
                        <span className="text-sm text-muted-foreground">
                          {hours.isOpen ? "Aberto" : "Fechado"}
                        </span>
                      </div>
                      
                      {hours.isOpen && (
                        <div className="flex items-center space-x-2">
                          <Input
                            type="time"
                            value={hours.open}
                            onChange={(e) => 
                              handleOpeningHoursChange(
                                day as keyof MerchantProfile["openingHours"],
                                "open",
                                e.target.value
                              )
                            }
                            className="w-24"
                          />
                          <span>às</span>
                          <Input
                            type="time"
                            value={hours.close}
                            onChange={(e) => 
                              handleOpeningHoursChange(
                                day as keyof MerchantProfile["openingHours"],
                                "close",
                                e.target.value
                              )
                            }
                            className="w-24"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
        
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Configurações de Entrega</CardTitle>
            <CardDescription>
              Configure as opções de entrega da sua loja
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="minOrderValue" className="flex items-center">
                  <ShoppingBag className="h-4 w-4 mr-2 text-muted-foreground" />
                  Pedido mínimo (R$)
                </Label>
                <Input
                  id="minOrderValue"
                  type="number"
                  min="0"
                  step="0.01"
                  value={minOrderValue || ""}
                  onChange={(e) => setMinOrderValue(parseFloat(e.target.value) || undefined)}
                  placeholder="0.00"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="deliveryFee" className="flex items-center">
                  <Truck className="h-4 w-4 mr-2 text-muted-foreground" />
                  Taxa de entrega (R$)
                </Label>
                <Input
                  id="deliveryFee"
                  type="number"
                  min="0"
                  step="0.01"
                  value={deliveryFee || ""}
                  onChange={(e) => setDeliveryFee(parseFloat(e.target.value) || undefined)}
                  placeholder="0.00"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="deliveryTime" className="flex items-center">
                  <Clock className="h-4 w-4 mr-2 text-muted-foreground" />
                  Tempo de entrega (min)
                </Label>
                <Input
                  id="deliveryTime"
                  value={deliveryTime}
                  onChange={(e) => setDeliveryTime(e.target.value)}
                  placeholder="30-45"
                />
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                // Reset form to original values
                if (profile) {
                  setName(profile.name || "");
                  setDescription(profile.description || "");
                  setPhone(profile.phone || "");
                  setAddress(profile.address || "");
                  setCategory(profile.category || "");
                  setOpeningHours(profile.openingHours || null);
                  setMinOrderValue(profile.minOrderValue);
                  setDeliveryFee(profile.deliveryFee);
                  setDeliveryTime(profile.deliveryTime || "");
                  setAvatarPreview(profile.avatarUrl || null);
                  setCoverImagePreview(profile.coverImageUrl || null);
                  setAvatarFile(null);
                  setCoverImageFile(null);
                }
              }}
            >
              Cancelar
            </Button>
            <Button
              type="submit"
              className="bg-cta hover:bg-cta-dark"
              disabled={isSaving}
            >
              {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Salvando...
                </>
              ) : (
                "Salvar Alterações"
              )}
            </Button>
          </CardFooter>
        </Card>
      </form>
    </div>
  );
}
