import { motion } from "framer-motion";
import { Badge, BadgeProps } from "./badge";
import { cn } from "@/lib/utils";

interface AnimatedBadgeProps extends BadgeProps {
  pulse?: boolean;
  bounce?: boolean;
}

export function AnimatedBadge({
  children,
  className,
  pulse = false,
  bounce = false,
  ...props
}: AnimatedBadgeProps) {
  const pulseAnimation = pulse
    ? {
        scale: [1, 1.05, 1],
        transition: {
          duration: 2,
          repeat: Infinity,
          repeatType: "loop" as const,
        },
      }
    : {};

  const bounceAnimation = bounce
    ? {
        y: [0, -3, 0],
        transition: {
          duration: 0.6,
          repeat: Infinity,
          repeatType: "loop" as const,
        },
      }
    : {};

  return (
    <motion.div
      animate={{
        ...pulseAnimation,
        ...bounceAnimation,
      }}
      className="inline-block"
    >
      <Badge className={cn("", className)} {...props}>
        {children}
      </Badge>
    </motion.div>
  );
}
