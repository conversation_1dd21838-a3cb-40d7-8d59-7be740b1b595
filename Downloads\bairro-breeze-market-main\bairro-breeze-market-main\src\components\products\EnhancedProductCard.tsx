import React, { useState, useRef } from 'react';
import { Link } from 'react-router-dom';
import { motion, useMotionValue, useTransform, AnimatePresence } from 'framer-motion';
import { ShoppingCart, Heart, Star, Plus, Minus, Check, Info } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useCart } from '@/hooks/useCart';
import { toast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import { ImageOptimizer } from '@/components/ui/image-optimizer';
import { useMediaQuery } from '@/hooks/use-breakpoint';

interface EnhancedProductCardProps {
  id: string;
  name: string;
  description?: string;
  price: number;
  originalPrice?: number;
  image: string;
  rating?: number;
  shopName?: string;
  category?: string;
  isPromo?: boolean;
  className?: string;
  variant?: 'default' | 'compact' | 'featured';
  onQuickView?: () => void;
}

/**
 * Componente de card de produto aprimorado com animações fluidas e interações
 */
export function EnhancedProductCard({
  id,
  name,
  description,
  price,
  originalPrice,
  image,
  rating = 0,
  shopName,
  category,
  isPromo = false,
  className,
  variant = 'default',
  onQuickView,
}: EnhancedProductCardProps) {
  const [isFavorite, setIsFavorite] = useState(false);
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const [quantity, setQuantity] = useState(1);
  const [isHovered, setIsHovered] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);
  const { addItem, items } = useCart();
  const isInCart = items.some(item => item.id === id);
  const isMobile = !useMediaQuery('sm');
  
  // Calcular desconto
  const discountPercentage = originalPrice 
    ? Math.round(((originalPrice - price) / originalPrice) * 100) 
    : 0;
  
  // Formatar preço
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };
  
  // Efeito de inclinação 3D no hover (apenas em desktop)
  const x = useMotionValue(0);
  const y = useMotionValue(0);
  
  const rotateX = useTransform(y, [-100, 100], [5, -5]);
  const rotateY = useTransform(x, [-100, 100], [-5, 5]);
  
  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (isMobile || !cardRef.current) return;
    
    const rect = cardRef.current.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    
    x.set(e.clientX - centerX);
    y.set(e.clientY - centerY);
  };
  
  const handleMouseLeave = () => {
    x.set(0);
    y.set(0);
    setIsHovered(false);
  };
  
  // Adicionar ao carrinho com animação
  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    setIsAddingToCart(true);
    
    // Adicionar ao carrinho após um pequeno delay para a animação
    setTimeout(() => {
      addItem({
        id,
        name,
        price,
        image,
        quantity,
      });
      
      toast({
        title: "Produto adicionado",
        description: `${name} foi adicionado ao carrinho.`,
      });
      
      setIsAddingToCart(false);
    }, 300);
  };
  
  // Alternar favorito
  const toggleFavorite = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsFavorite(!isFavorite);
    
    toast({
      title: isFavorite ? "Removido dos favoritos" : "Adicionado aos favoritos",
      description: `${name} foi ${isFavorite ? "removido dos" : "adicionado aos"} favoritos.`,
    });
  };
  
  // Abrir visualização rápida
  const handleQuickView = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (onQuickView) onQuickView();
  };
  
  // Incrementar quantidade
  const incrementQuantity = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setQuantity(prev => Math.min(prev + 1, 10));
  };
  
  // Decrementar quantidade
  const decrementQuantity = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setQuantity(prev => Math.max(prev - 1, 1));
  };
  
  // Variantes de animação
  const cardVariants = {
    default: {
      scale: 1,
      boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
    },
    hover: {
      scale: 1.03,
      boxShadow: "0 10px 20px rgba(0,0,0,0.15)",
      y: -5,
    },
    tap: {
      scale: 0.98,
      boxShadow: "0 1px 3px rgba(0,0,0,0.1)",
    },
  };
  
  // Renderizar card compacto
  if (variant === 'compact') {
    return (
      <motion.div
        ref={cardRef}
        className={cn(
          "bg-white rounded-lg overflow-hidden border relative",
          className
        )}
        initial="default"
        whileHover="hover"
        whileTap="tap"
        variants={cardVariants}
      >
        <Link to={`/product/${id}`} className="block">
          <div className="flex items-center p-2">
            <div className="relative w-16 h-16 rounded-md overflow-hidden flex-shrink-0">
              <ImageOptimizer
                src={image}
                alt={name}
                className="w-full h-full object-cover"
                aspectRatio="1/1"
              />
              {isPromo && (
                <Badge className="absolute top-0 left-0 bg-cta text-white text-xs px-1 py-0">
                  -{discountPercentage}%
                </Badge>
              )}
            </div>
            
            <div className="ml-3 flex-1 min-w-0">
              <h3 className="text-sm font-medium line-clamp-1">{name}</h3>
              <p className="text-xs text-muted-foreground line-clamp-1">{shopName}</p>
              <div className="flex items-center justify-between mt-1">
                <div className="flex flex-col">
                  <span className="text-sm font-bold">{formatCurrency(price)}</span>
                  {originalPrice && (
                    <span className="text-xs text-muted-foreground line-through">
                      {formatCurrency(originalPrice)}
                    </span>
                  )}
                </div>
              </div>
            </div>
            
            <Button
              size="icon"
              variant="ghost"
              className="h-8 w-8 ml-2 flex-shrink-0"
              onClick={handleAddToCart}
            >
              <AnimatePresence mode="wait">
                <motion.div
                  key={isAddingToCart ? 'loading' : isInCart ? 'check' : 'cart'}
                  initial={{ scale: 0.5, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  exit={{ scale: 0.5, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  {isAddingToCart ? (
                    <Loader className="h-4 w-4 animate-spin" />
                  ) : isInCart ? (
                    <Check className="h-4 w-4 text-green-500" />
                  ) : (
                    <ShoppingCart className="h-4 w-4" />
                  )}
                </motion.div>
              </AnimatePresence>
            </Button>
          </div>
        </Link>
      </motion.div>
    );
  }
  
  // Renderizar card em destaque
  if (variant === 'featured') {
    return (
      <motion.div
        ref={cardRef}
        className={cn(
          "bg-white rounded-lg overflow-hidden border relative",
          className
        )}
        initial="default"
        whileHover="hover"
        whileTap="tap"
        variants={cardVariants}
        onMouseMove={handleMouseMove}
        onMouseLeave={handleMouseLeave}
        onHoverStart={() => setIsHovered(true)}
        style={{ 
          rotateX: !isMobile ? rotateX : 0, 
          rotateY: !isMobile ? rotateY : 0,
          transformStyle: "preserve-3d",
        }}
      >
        <Link to={`/product/${id}`} className="block">
          <div className="relative">
            <div className="relative h-60 overflow-hidden">
              <ImageOptimizer
                src={image}
                alt={name}
                className="w-full h-full object-cover transition-transform duration-500"
                style={{ transform: isHovered ? "scale(1.1)" : "scale(1)" }}
                aspectRatio="4/3"
              />
              
              <motion.div 
                className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"
                initial={{ opacity: 0 }}
                animate={{ opacity: isHovered ? 1 : 0 }}
                transition={{ duration: 0.3 }}
              />
            </div>
            
            {isPromo && (
              <Badge className="absolute top-2 left-2 bg-cta text-white">
                {discountPercentage}% OFF
              </Badge>
            )}
            
            <div className="absolute top-2 right-2 flex flex-col gap-2">
              <Button
                variant="ghost"
                size="icon"
                className={cn(
                  "rounded-full bg-white/80 hover:bg-white shadow-md",
                  isFavorite ? "text-red-500" : "text-gray-500"
                )}
                onClick={toggleFavorite}
              >
                <Heart className={cn("h-5 w-5", isFavorite ? "fill-current" : "")} />
              </Button>
              
              {onQuickView && (
                <Button
                  variant="ghost"
                  size="icon"
                  className="rounded-full bg-white/80 hover:bg-white shadow-md"
                  onClick={handleQuickView}
                >
                  <Info className="h-5 w-5" />
                </Button>
              )}
            </div>
            
            <motion.div 
              className="absolute bottom-0 left-0 right-0 p-4"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: isHovered ? 0 : 20, opacity: isHovered ? 1 : 0 }}
              transition={{ duration: 0.3 }}
            >
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Star
                      key={i}
                      className={cn(
                        "h-4 w-4",
                        i < Math.floor(rating) ? "text-yellow-400 fill-yellow-400" : "text-gray-300"
                      )}
                    />
                  ))}
                  <span className="ml-1 text-xs text-white">{rating.toFixed(1)}</span>
                </div>
                
                <Button
                  size="sm"
                  className="bg-cta hover:bg-cta-dark text-white"
                  onClick={handleAddToCart}
                >
                  <AnimatePresence mode="wait">
                    <motion.div
                      key={isAddingToCart ? 'loading' : isInCart ? 'check' : 'cart'}
                      initial={{ scale: 0.5, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      exit={{ scale: 0.5, opacity: 0 }}
                      transition={{ duration: 0.2 }}
                      className="flex items-center"
                    >
                      {isAddingToCart ? (
                        <Loader className="h-4 w-4 animate-spin mr-2" />
                      ) : isInCart ? (
                        <Check className="h-4 w-4 mr-2" />
                      ) : (
                        <ShoppingCart className="h-4 w-4 mr-2" />
                      )}
                      {isAddingToCart ? "Adicionando..." : isInCart ? "No Carrinho" : "Comprar"}
                    </motion.div>
                  </AnimatePresence>
                </Button>
              </div>
            </motion.div>
          </div>
          
          <div className="p-4">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="font-medium line-clamp-2">{name}</h3>
                <p className="text-sm text-muted-foreground">{shopName}</p>
              </div>
              <div className="text-right">
                <div className="text-lg font-bold">{formatCurrency(price)}</div>
                {originalPrice && (
                  <div className="text-sm text-muted-foreground line-through">
                    {formatCurrency(originalPrice)}
                  </div>
                )}
              </div>
            </div>
            
            {description && (
              <p className="mt-2 text-sm text-muted-foreground line-clamp-2">{description}</p>
            )}
          </div>
        </Link>
      </motion.div>
    );
  }
  
  // Renderizar card padrão
  return (
    <motion.div
      ref={cardRef}
      className={cn(
        "bg-white rounded-lg overflow-hidden border relative",
        className
      )}
      initial="default"
      whileHover="hover"
      whileTap="tap"
      variants={cardVariants}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      onHoverStart={() => setIsHovered(true)}
      style={{ 
        rotateX: !isMobile ? rotateX : 0, 
        rotateY: !isMobile ? rotateY : 0,
        transformStyle: "preserve-3d",
      }}
    >
      <Link to={`/product/${id}`} className="block">
        <div className="relative">
          <div className="relative h-48 overflow-hidden">
            <ImageOptimizer
              src={image}
              alt={name}
              className="w-full h-full object-cover transition-transform duration-500"
              style={{ transform: isHovered ? "scale(1.1)" : "scale(1)" }}
              aspectRatio="1/1"
            />
          </div>
          
          {isPromo && (
            <Badge className="absolute top-2 left-2 bg-cta text-white">
              {discountPercentage}% OFF
            </Badge>
          )}
          
          <Button
            variant="ghost"
            size="icon"
            className={cn(
              "absolute top-2 right-2 rounded-full bg-white/80 hover:bg-white shadow-md",
              isFavorite ? "text-red-500" : "text-gray-500"
            )}
            onClick={toggleFavorite}
          >
            <Heart className={cn("h-5 w-5", isFavorite ? "fill-current" : "")} />
          </Button>
          
          <motion.div 
            className="absolute bottom-0 left-0 right-0 p-3 bg-gradient-to-t from-black/60 to-transparent"
            initial={{ opacity: 0, y: 10 }}
            animate={{ 
              opacity: isHovered ? 1 : 0,
              y: isHovered ? 0 : 10
            }}
            transition={{ duration: 0.2 }}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-1">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-7 w-7 rounded-full bg-white/90 hover:bg-white"
                  onClick={decrementQuantity}
                  disabled={quantity <= 1}
                >
                  <Minus className="h-3 w-3" />
                </Button>
                <span className="text-white font-medium">{quantity}</span>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-7 w-7 rounded-full bg-white/90 hover:bg-white"
                  onClick={incrementQuantity}
                  disabled={quantity >= 10}
                >
                  <Plus className="h-3 w-3" />
                </Button>
              </div>
              
              <Button
                size="sm"
                className="bg-cta hover:bg-cta-dark text-white"
                onClick={handleAddToCart}
              >
                <AnimatePresence mode="wait">
                  <motion.div
                    key={isAddingToCart ? 'loading' : isInCart ? 'check' : 'cart'}
                    initial={{ scale: 0.5, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    exit={{ scale: 0.5, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    className="flex items-center"
                  >
                    {isAddingToCart ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : isInCart ? (
                      <Check className="h-4 w-4" />
                    ) : (
                      <ShoppingCart className="h-4 w-4" />
                    )}
                  </motion.div>
                </AnimatePresence>
              </Button>
            </div>
          </motion.div>
        </div>
        
        <div className="p-3">
          <h3 className="text-sm font-medium line-clamp-2">{name}</h3>
          <p className="text-xs text-muted-foreground">{shopName}</p>
          <div className="flex items-end justify-between mt-2">
            <div>
              <div className="text-sm font-bold">{formatCurrency(price)}</div>
              {originalPrice && (
                <div className="text-xs text-muted-foreground line-through">
                  {formatCurrency(originalPrice)}
                </div>
              )}
            </div>
            
            {rating > 0 && (
              <div className="flex items-center">
                <Star className="h-3 w-3 text-yellow-400 fill-yellow-400" />
                <span className="ml-1 text-xs">{rating.toFixed(1)}</span>
              </div>
            )}
          </div>
        </div>
      </Link>
    </motion.div>
  );
}

// Componente de loader para o card
function Loader({ className }: { className?: string }) {
  return (
    <svg
      className={cn("animate-spin", className)}
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      viewBox="0 0 24 24"
    >
      <circle
        className="opacity-25"
        cx="12"
        cy="12"
        r="10"
        stroke="currentColor"
        strokeWidth="4"
      ></circle>
      <path
        className="opacity-75"
        fill="currentColor"
        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
      ></path>
    </svg>
  );
}
