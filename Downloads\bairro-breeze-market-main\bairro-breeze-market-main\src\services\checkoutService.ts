/**
 * Serviços básicos de checkout para desenvolvimento
 */
import { CartItem } from "@/types/cart";
import { Order } from "@/types/order";

// Interface for payment method
export interface PaymentMethod {
  id: string;
  type: "credit_card" | "debit_card" | "pix" | "cash";
  name: string;
  last4?: string;
  expiryDate?: string;
}

// Interface for checkout data
export interface CheckoutData {
  items: CartItem[];
  total: number;
  deliveryAddress: string;
  paymentMethod: PaymentMethod;
  notes?: string;
}

// Interface for checkout result
export interface CheckoutResult {
  success: boolean;
  order?: Order;
  error?: string;
}

// Mock payment methods
const mockPaymentMethods: PaymentMethod[] = [
  {
    id: "pm_1",
    type: "credit_card",
    name: "Visa",
    last4: "4242",
    expiryDate: "12/25"
  },
  {
    id: "pm_2",
    type: "credit_card",
    name: "Mastercard",
    last4: "5555",
    expiryDate: "10/24"
  },
  {
    id: "pm_3",
    type: "pix",
    name: "P<PERSON>"
  },
  {
    id: "pm_4",
    type: "cash",
    name: "<PERSON><PERSON><PERSON>"
  }
];

/**
 * Get saved payment methods for the user
 */
export async function getSavedPaymentMethods(): Promise<PaymentMethod[]> {
  try {
    // For now, return mock data
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(mockPaymentMethods);
      }, 500);
    });
  } catch (error) {
    console.error("Error getting saved payment methods:", error);
    return [];
  }
}

/**
 * Process checkout
 */
export async function processCheckout(checkoutData: CheckoutData): Promise<CheckoutResult> {
  try {
    // Simulate API call with a delay
    return new Promise((resolve) => {
      setTimeout(() => {
        // Simulate payment processing
        const paymentSuccessful = Math.random() > 0.1; // 90% success rate
        
        if (!paymentSuccessful) {
          resolve({
            success: false,
            error: "Falha no processamento do pagamento. Por favor, tente novamente."
          });
          return;
        }
        
        // Create order
        const order: Order = {
          id: `order_${Date.now()}`,
          items: checkoutData.items.map(item => ({
            productId: item.id,
            quantity: item.quantity,
            price: item.price,
            name: item.name,
            image: item.image
          })),
          total: checkoutData.total,
          status: "pending",
          createdAt: new Date().toISOString(),
          deliveryAddress: checkoutData.deliveryAddress,
          shopName: checkoutData.items[0].shopName,
          paymentMethod: checkoutData.paymentMethod.type,
          notes: checkoutData.notes
        };
        
        resolve({
          success: true,
          order
        });
      }, 2000);
    });
  } catch (error: any) {
    console.error("Error processing checkout:", error);
    return {
      success: false,
      error: error.message || "Ocorreu um erro ao processar o pagamento."
    };
  }
}

/**
 * Calculate delivery fee
 */
export async function calculateDeliveryFee(items: CartItem[], deliveryAddress: string): Promise<number> {
  try {
    // Return a fixed fee
    return new Promise((resolve) => {
      setTimeout(() => {
        // Base fee
        let fee = 5.0;
        
        // Add a small random amount to simulate distance-based calculation
        fee += Math.random() * 5;
        
        // Round to 2 decimal places
        fee = Math.round(fee * 100) / 100;
        
        resolve(fee);
      }, 500);
    });
  } catch (error) {
    console.error("Error calculating delivery fee:", error);
    return 5.0; // Default fee
  }
}
