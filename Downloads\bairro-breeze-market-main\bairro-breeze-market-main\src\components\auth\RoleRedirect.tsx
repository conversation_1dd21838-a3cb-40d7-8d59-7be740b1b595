import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/useAuth';
import { LoadingState } from '@/components/ui/async-state';

/**
 * Componente que redireciona o usuário para a página apropriada com base em seu perfil
 */
export function RoleRedirect() {
  const { user, loading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (loading) return;

    if (!user) {
      // Se não estiver logado, redirecionar para login
      navigate('/login');
      return;
    }

    // Redirecionar com base no perfil do usuário
    const role = user.user_metadata.role;
    
    switch (role) {
      case 'merchant':
        navigate('/merchant');
        break;
      case 'deliverer':
        navigate('/deliverer');
        break;
      case 'customer':
      default:
        navigate('/');
        break;
    }
  }, [user, loading, navigate]);

  return (
    <div className="flex items-center justify-center min-h-screen">
      <LoadingState message="Redirecionando para sua área..." />
    </div>
  );
}
