import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  ArrowDown, 
  ArrowUp, 
  BarChart, 
  DollarSign, 
  ShoppingBag, 
  TrendingUp, 
  Users,
  Package
} from "lucide-react";
import { Order } from "@/types/order";
import { Product } from "@/types/product";
import { useQuery } from "@tanstack/react-query";
import { fetchOrders } from "@/services/orders";
import { fetchProducts } from "@/services/products";
import { format, subDays, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from "date-fns";
import { ptBR } from "date-fns/locale";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";

interface SalesSummary {
  totalSales: number;
  totalOrders: number;
  averageOrderValue: number;
  percentChange: number;
  topProducts: Array<{
    id: string;
    name: string;
    quantity: number;
    revenue: number;
  }>;
}

export function SalesAnalytics() {
  const [timeRange, setTimeRange] = useState<"today" | "week" | "month" | "all">("week");
  
  // Fetch orders and products
  const { data: orders = [], isLoading: isLoadingOrders } = useQuery({
    queryKey: ["orders"],
    queryFn: fetchOrders,
  });

  const { data: products = [], isLoading: isLoadingProducts } = useQuery({
    queryKey: ["products"],
    queryFn: fetchProducts,
  });

  // Filter orders by time range
  const getFilteredOrders = () => {
    const now = new Date();
    
    switch (timeRange) {
      case "today":
        return orders.filter(order => {
          const orderDate = new Date(order.createdAt);
          return orderDate.toDateString() === now.toDateString();
        });
      case "week":
        const weekStart = startOfWeek(now, { weekStartsOn: 1 });
        const weekEnd = endOfWeek(now, { weekStartsOn: 1 });
        return orders.filter(order => {
          const orderDate = new Date(order.createdAt);
          return orderDate >= weekStart && orderDate <= weekEnd;
        });
      case "month":
        const monthStart = startOfMonth(now);
        const monthEnd = endOfMonth(now);
        return orders.filter(order => {
          const orderDate = new Date(order.createdAt);
          return orderDate >= monthStart && orderDate <= monthEnd;
        });
      case "all":
      default:
        return orders;
    }
  };

  // Calculate sales summary
  const calculateSalesSummary = (): SalesSummary => {
    const filteredOrders = getFilteredOrders();
    const totalSales = filteredOrders.reduce((sum, order) => sum + order.total, 0);
    const totalOrders = filteredOrders.length;
    const averageOrderValue = totalOrders > 0 ? totalSales / totalOrders : 0;
    
    // Calculate previous period for comparison
    const now = new Date();
    let previousPeriodStart, previousPeriodEnd, currentPeriodStart;
    
    switch (timeRange) {
      case "today":
        previousPeriodStart = subDays(now, 1);
        previousPeriodEnd = previousPeriodStart;
        currentPeriodStart = now;
        break;
      case "week":
        currentPeriodStart = startOfWeek(now, { weekStartsOn: 1 });
        previousPeriodStart = subDays(currentPeriodStart, 7);
        previousPeriodEnd = subDays(currentPeriodStart, 1);
        break;
      case "month":
        currentPeriodStart = startOfMonth(now);
        previousPeriodStart = startOfMonth(subDays(currentPeriodStart, 1));
        previousPeriodEnd = subDays(currentPeriodStart, 1);
        break;
      case "all":
      default:
        // For "all", we'll just use a fixed percentage change
        return {
          totalSales,
          totalOrders,
          averageOrderValue,
          percentChange: 0,
          topProducts: calculateTopProducts(filteredOrders)
        };
    }
    
    const previousPeriodOrders = orders.filter(order => {
      const orderDate = new Date(order.createdAt);
      return orderDate >= previousPeriodStart && orderDate <= previousPeriodEnd;
    });
    
    const previousSales = previousPeriodOrders.reduce((sum, order) => sum + order.total, 0);
    
    // Calculate percent change
    let percentChange = 0;
    if (previousSales > 0) {
      percentChange = ((totalSales - previousSales) / previousSales) * 100;
    } else if (totalSales > 0) {
      percentChange = 100; // If previous was 0 and current is positive, that's a 100% increase
    }
    
    return {
      totalSales,
      totalOrders,
      averageOrderValue,
      percentChange,
      topProducts: calculateTopProducts(filteredOrders)
    };
  };

  // Calculate top products
  const calculateTopProducts = (filteredOrders: Order[]) => {
    const productSales: Record<string, { quantity: number, revenue: number }> = {};
    
    // Aggregate sales by product
    filteredOrders.forEach(order => {
      order.items.forEach(item => {
        if (!productSales[item.productId]) {
          productSales[item.productId] = { quantity: 0, revenue: 0 };
        }
        productSales[item.productId].quantity += item.quantity;
        productSales[item.productId].revenue += item.price * item.quantity;
      });
    });
    
    // Convert to array and sort by revenue
    const topProducts = Object.entries(productSales).map(([id, data]) => {
      const product = products.find(p => p.id === id);
      return {
        id,
        name: product ? product.name : `Produto #${id}`,
        quantity: data.quantity,
        revenue: data.revenue
      };
    }).sort((a, b) => b.revenue - a.revenue).slice(0, 5);
    
    return topProducts;
  };

  const summary = calculateSalesSummary();

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const getTimeRangeLabel = () => {
    const now = new Date();
    
    switch (timeRange) {
      case "today":
        return format(now, "dd 'de' MMMM", { locale: ptBR });
      case "week":
        const weekStart = startOfWeek(now, { weekStartsOn: 1 });
        const weekEnd = endOfWeek(now, { weekStartsOn: 1 });
        return `${format(weekStart, "dd/MM", { locale: ptBR })} - ${format(weekEnd, "dd/MM", { locale: ptBR })}`;
      case "month":
        return format(now, "MMMM 'de' yyyy", { locale: ptBR });
      case "all":
      default:
        return "Todo o período";
    }
  };

  // Mock data for the chart
  const dailySales = [
    { day: "Seg", amount: 450 },
    { day: "Ter", amount: 380 },
    { day: "Qua", amount: 520 },
    { day: "Qui", amount: 650 },
    { day: "Sex", amount: 780 },
    { day: "Sáb", amount: 850 },
    { day: "Dom", amount: 400 },
  ];

  // Calculate order status distribution
  const orderStatusCounts = {
    pending: orders.filter(order => order.status === "pending").length,
    in_progress: orders.filter(order => order.status === "in_progress").length,
    delivered: orders.filter(order => order.status === "delivered").length,
    cancelled: orders.filter(order => order.status === "cancelled").length,
  };

  const isLoading = isLoadingOrders || isLoadingProducts;

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h2 className="text-xl font-semibold">Análise de Vendas</h2>
        <div className="flex gap-2 w-full sm:w-auto">
          <Select
            value={timeRange}
            onValueChange={(value: "today" | "week" | "month" | "all") => setTimeRange(value)}
          >
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="Período" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="today">Hoje</SelectItem>
              <SelectItem value="week">Esta semana</SelectItem>
              <SelectItem value="month">Este mês</SelectItem>
              <SelectItem value="all">Todo o período</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <div className="h-6 w-6 animate-spin rounded-full border-2 border-eco border-t-transparent" />
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Vendas Totais</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(summary.totalSales)}</div>
                <div className="flex items-center pt-1 text-xs text-muted-foreground">
                  {summary.percentChange > 0 ? (
                    <>
                      <ArrowUp className="h-3 w-3 text-green-500 mr-1" />
                      <span className="text-green-500">{summary.percentChange.toFixed(1)}%</span>
                    </>
                  ) : summary.percentChange < 0 ? (
                    <>
                      <ArrowDown className="h-3 w-3 text-red-500 mr-1" />
                      <span className="text-red-500">{Math.abs(summary.percentChange).toFixed(1)}%</span>
                    </>
                  ) : (
                    <span>Sem alteração</span>
                  )}
                  <span className="ml-1">vs. período anterior</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Pedidos</CardTitle>
                <ShoppingBag className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{summary.totalOrders}</div>
                <div className="pt-1 text-xs text-muted-foreground">
                  {getTimeRangeLabel()}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Ticket Médio</CardTitle>
                <BarChart className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(summary.averageOrderValue)}</div>
                <div className="pt-1 text-xs text-muted-foreground">
                  Por pedido
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-sm font-medium">Produtos Vendidos</CardTitle>
                <Package className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {summary.topProducts.reduce((sum, product) => sum + product.quantity, 0)}
                </div>
                <div className="pt-1 text-xs text-muted-foreground">
                  Unidades vendidas
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Vendas Diárias</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-[200px]">
                  <div className="flex h-full items-end gap-2">
                    {dailySales.map((day, i) => (
                      <div key={i} className="relative flex h-full w-full flex-col items-center">
                        <div 
                          className="absolute bottom-0 w-full rounded-md bg-eco" 
                          style={{ height: `${(day.amount / 850) * 100}%` }}
                        />
                        <div className="mt-auto pt-3 text-xs">{day.day}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Status dos Pedidos</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <div className="w-full">
                      <div className="flex justify-between mb-1">
                        <span className="text-sm">Pendentes</span>
                        <span className="text-sm font-medium">{orderStatusCounts.pending}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div className="bg-yellow-500 h-2.5 rounded-full" style={{ width: `${(orderStatusCounts.pending / orders.length) * 100}%` }}></div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="w-full">
                      <div className="flex justify-between mb-1">
                        <span className="text-sm">Em Preparo</span>
                        <span className="text-sm font-medium">{orderStatusCounts.in_progress}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div className="bg-blue-500 h-2.5 rounded-full" style={{ width: `${(orderStatusCounts.in_progress / orders.length) * 100}%` }}></div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="w-full">
                      <div className="flex justify-between mb-1">
                        <span className="text-sm">Entregues</span>
                        <span className="text-sm font-medium">{orderStatusCounts.delivered}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div className="bg-green-500 h-2.5 rounded-full" style={{ width: `${(orderStatusCounts.delivered / orders.length) * 100}%` }}></div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="w-full">
                      <div className="flex justify-between mb-1">
                        <span className="text-sm">Cancelados</span>
                        <span className="text-sm font-medium">{orderStatusCounts.cancelled}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2.5">
                        <div className="bg-red-500 h-2.5 rounded-full" style={{ width: `${(orderStatusCounts.cancelled / orders.length) * 100}%` }}></div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Produtos Mais Vendidos</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Produto</TableHead>
                    <TableHead className="text-right">Quantidade</TableHead>
                    <TableHead className="text-right">Receita</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {summary.topProducts.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={3} className="text-center py-4 text-muted-foreground">
                        Nenhum produto vendido neste período
                      </TableCell>
                    </TableRow>
                  ) : (
                    summary.topProducts.map((product) => (
                      <TableRow key={product.id}>
                        <TableCell className="font-medium">{product.name}</TableCell>
                        <TableCell className="text-right">{product.quantity}</TableCell>
                        <TableCell className="text-right">{formatCurrency(product.revenue)}</TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}
