import React, { useRef, useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface ContainerQueryProps {
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  breakpoints?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    '2xl'?: number;
  };
  as?: React.ElementType;
}

/**
 * Componente para aplicar estilos baseados no tamanho do container
 * Usa ResizeObserver para detectar mudanças no tamanho do container
 */
export function ContainerQuery({
  children,
  className,
  style,
  breakpoints = {
    xs: 0,
    sm: 384,
    md: 512,
    lg: 640,
    xl: 768,
    '2xl': 1024,
  },
  as: Component = 'div',
}: ContainerQueryProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [containerWidth, setContainerWidth] = useState(0);

  // Calcular classes baseadas no tamanho do container
  const containerClasses = [
    containerWidth >= (breakpoints.xs || 0) ? 'cq-xs' : '',
    containerWidth >= (breakpoints.sm || 384) ? 'cq-sm' : '',
    containerWidth >= (breakpoints.md || 512) ? 'cq-md' : '',
    containerWidth >= (breakpoints.lg || 640) ? 'cq-lg' : '',
    containerWidth >= (breakpoints.xl || 768) ? 'cq-xl' : '',
    containerWidth >= (breakpoints['2xl'] || 1024) ? 'cq-2xl' : '',
  ].filter(Boolean).join(' ');

  // Observar mudanças no tamanho do container
  useEffect(() => {
    if (!containerRef.current) return;

    const container = containerRef.current;
    
    // Definir largura inicial
    setContainerWidth(container.offsetWidth);

    // Criar ResizeObserver
    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        if (entry.target === container) {
          setContainerWidth(entry.contentRect.width);
        }
      }
    });

    // Observar container
    resizeObserver.observe(container);

    // Limpar observer
    return () => {
      resizeObserver.unobserve(container);
      resizeObserver.disconnect();
    };
  }, []);

  return (
    <Component
      ref={containerRef}
      className={cn(containerClasses, className)}
      style={{
        ...style,
        // Adicionar variáveis CSS para uso em estilos
        '--container-width': `${containerWidth}px`,
      }}
      data-container-width={containerWidth}
    >
      {children}
    </Component>
  );
}

/**
 * Componente para aplicar estilos baseados no tamanho do container com grid responsivo
 */
export function ResponsiveGrid({
  children,
  className,
  style,
  minItemWidth = 300,
  gap = 16,
  ...props
}: Omit<ContainerQueryProps, 'as'> & {
  minItemWidth?: number;
  gap?: number;
}) {
  return (
    <ContainerQuery
      className={cn('grid', className)}
      style={{
        ...style,
        display: 'grid',
        gridTemplateColumns: `repeat(auto-fill, minmax(${minItemWidth}px, 1fr))`,
        gap: gap,
      }}
      {...props}
    >
      {children}
    </ContainerQuery>
  );
}

/**
 * Componente para aplicar estilos baseados no tamanho do container com layout flexível
 */
export function ResponsiveFlex({
  children,
  className,
  style,
  direction = 'row',
  wrap = true,
  gap = 16,
  ...props
}: Omit<ContainerQueryProps, 'as'> & {
  direction?: 'row' | 'column' | 'row-reverse' | 'column-reverse';
  wrap?: boolean;
  gap?: number;
}) {
  return (
    <ContainerQuery
      className={cn('flex', className)}
      style={{
        ...style,
        display: 'flex',
        flexDirection: direction,
        flexWrap: wrap ? 'wrap' : 'nowrap',
        gap: gap,
      }}
      {...props}
    >
      {children}
    </ContainerQuery>
  );
}
