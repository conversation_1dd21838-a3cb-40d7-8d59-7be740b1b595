import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Loader2, MapPin, Navigation, AlertTriangle, Home } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { toast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { getLocationByAddressAsCoordinates as getLocationByAddress, calculateRouteWithCoordinates as calculateRoute, getLocationUpdates } from '@/services/map';
import { formatDistance, formatDuration } from '@/lib/validations';
import { logger } from '@/services/logging';

interface Coordinates {
  lat: number;
  lng: number;
}

interface DeliveryMapProps {
  orderAddress: string;
  delivererId?: string;
  shopAddress?: string;
  estimatedTime?: number;
  onLocationUpdate?: (location: Coordinates) => void;
  onEstimatedTimeUpdate?: (minutes: number) => void;
  className?: string;
  height?: string | number;
}

/**
 * Componente de mapa para rastreamento de entrega
 * Em uma aplicação real, isso seria integrado com Google Maps, Mapbox, etc.
 */
export function DeliveryMap({
  orderAddress,
  delivererId,
  shopAddress = 'Rua Augusta, 1500, São Paulo, SP',
  estimatedTime,
  onLocationUpdate,
  onEstimatedTimeUpdate,
  className = '',
  height = 300,
}: DeliveryMapProps) {
  // Refs
  const mapContainerRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<any>(null);

  // Estado
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [customerLocation, setCustomerLocation] = useState<Coordinates | null>(null);
  const [shopLocation, setShopLocation] = useState<Coordinates | null>(null);
  const [delivererLocation, setDelivererLocation] = useState<Coordinates | null>(null);
  const [routeInfo, setRouteInfo] = useState<{
    distance: number;
    duration: number;
    path: Coordinates[];
  } | null>(null);
  const [mapLoaded, setMapLoaded] = useState(false);

  // Carregar localizações
  useEffect(() => {
    const loadLocations = async () => {
      setLoading(true);
      setError(null);

      try {
        // Obter localização do cliente
        const customerCoords = await getLocationByAddress(orderAddress);
        if (!customerCoords) {
          throw new Error('Não foi possível encontrar o endereço de entrega');
        }
        setCustomerLocation(customerCoords);

        // Obter localização da loja
        const shopCoords = await getLocationByAddress(shopAddress);
        if (!shopCoords) {
          throw new Error('Não foi possível encontrar o endereço da loja');
        }
        setShopLocation(shopCoords);

        // Calcular rota
        if (customerCoords && shopCoords) {
          const route = await calculateRoute(shopCoords, customerCoords);
          setRouteInfo(route);

          // Atualizar tempo estimado se não fornecido
          if (!estimatedTime && onEstimatedTimeUpdate) {
            const estimatedMinutes = Math.ceil(route.duration / 60);
            onEstimatedTimeUpdate(estimatedMinutes);
          }
        }
      } catch (error: any) {
        setError(error.message || 'Erro ao carregar o mapa');
        logger.error('Error loading delivery map', error, { orderAddress, shopAddress });

        toast({
          title: 'Erro ao carregar mapa',
          description: error.message || 'Não foi possível carregar o mapa de entrega',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    loadLocations();
  }, [orderAddress, shopAddress, estimatedTime, onEstimatedTimeUpdate]);

  // Simular atualizações de localização do entregador
  useEffect(() => {
    if (!delivererId || !shopLocation || !customerLocation || !routeInfo) return;

    // Iniciar com a localização da loja
    setDelivererLocation(shopLocation);

    // Obter atualizações de localização
    const unsubscribe = getLocationUpdates(
      delivererId,
      shopLocation,
      customerLocation,
      routeInfo.path,
      (location) => {
        setDelivererLocation(location);

        // Notificar sobre atualização de localização
        if (onLocationUpdate) {
          onLocationUpdate(location);
        }
      }
    );

    return () => {
      unsubscribe();
    };
  }, [delivererId, shopLocation, customerLocation, routeInfo, onLocationUpdate]);

  // Simular carregamento do mapa
  useEffect(() => {
    if (mapLoaded || !customerLocation || !shopLocation) return;

    const timer = setTimeout(() => {
      setMapLoaded(true);
    }, 1000);

    return () => {
      clearTimeout(timer);
    };
  }, [customerLocation, shopLocation, mapLoaded]);

  // Calcular centro do mapa
  const mapCenter = useMemo(() => {
    if (customerLocation && shopLocation) {
      return {
        lat: (customerLocation.lat + shopLocation.lat) / 2,
        lng: (customerLocation.lng + shopLocation.lng) / 2,
      };
    }
    return null;
  }, [customerLocation, shopLocation]);

  // Renderizar estado de carregamento
  if (loading) {
    return (
      <div
        className={`bg-gray-100 rounded-lg flex items-center justify-center ${className}`}
        style={{ height }}
      >
        <div className="flex flex-col items-center">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground mb-2" />
          <p className="text-sm text-muted-foreground">Carregando mapa...</p>
        </div>
      </div>
    );
  }

  // Renderizar estado de erro
  if (error) {
    return (
      <div
        className={`bg-gray-100 rounded-lg flex items-center justify-center ${className}`}
        style={{ height }}
      >
        <Alert variant="destructive" className="max-w-md">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Erro ao carregar mapa</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  // Renderizar mapa
  return (
    <div
      ref={mapContainerRef}
      className={`relative bg-gray-100 rounded-lg overflow-hidden ${className}`}
      style={{ height }}
    >
      {/* Mapa simulado */}
      <div className="absolute inset-0 bg-blue-50">
        {/* Camada de mapa simulada */}
        <div className="absolute inset-0 opacity-20">
          <svg width="100%" height="100%">
            <pattern
              id="grid"
              width="20"
              height="20"
              patternUnits="userSpaceOnUse"
            >
              <path
                d="M 20 0 L 0 0 0 20"
                fill="none"
                stroke="currentColor"
                strokeWidth="0.5"
              />
            </pattern>
            <rect width="100%" height="100%" fill="url(#grid)" />
          </svg>
        </div>

        {/* Rota */}
        {routeInfo && mapLoaded && (
          <svg
            className="absolute inset-0 w-full h-full"
            viewBox="0 0 100 100"
            preserveAspectRatio="none"
          >
            <path
              d="M20,80 Q40,40 80,20"
              fill="none"
              stroke="#3b82f6"
              strokeWidth="2"
              strokeDasharray="4 2"
            />
          </svg>
        )}

        {/* Marcador da loja */}
        {shopLocation && mapLoaded && (
          <div className="absolute left-[20%] top-[80%] transform -translate-x-1/2 -translate-y-1/2">
            <div className="bg-white p-1 rounded-full shadow-md">
              <Home className="h-5 w-5 text-eco" />
            </div>
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 whitespace-nowrap">
              <span className="text-xs font-medium bg-white px-1 py-0.5 rounded shadow-sm">
                Loja
              </span>
            </div>
          </div>
        )}

        {/* Marcador do cliente */}
        {customerLocation && mapLoaded && (
          <div className="absolute right-[20%] top-[20%] transform -translate-x-1/2 -translate-y-1/2">
            <div className="bg-white p-1 rounded-full shadow-md">
              <MapPin className="h-5 w-5 text-red-500" />
            </div>
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 whitespace-nowrap">
              <span className="text-xs font-medium bg-white px-1 py-0.5 rounded shadow-sm">
                Destino
              </span>
            </div>
          </div>
        )}

        {/* Marcador do entregador */}
        <AnimatePresence>
          {delivererLocation && mapLoaded && (
            <motion.div
              className="absolute transform -translate-x-1/2 -translate-y-1/2"
              initial={{ left: '20%', top: '80%' }}
              animate={{
                left: delivererLocation === shopLocation ? '20%' : delivererLocation === customerLocation ? '80%' : '50%',
                top: delivererLocation === shopLocation ? '80%' : delivererLocation === customerLocation ? '20%' : '50%',
              }}
              transition={{ duration: 2, ease: 'linear' }}
            >
              <motion.div
                className="bg-eco p-1 rounded-full shadow-md"
                animate={{ scale: [1, 1.1, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              >
                <Navigation className="h-6 w-6 text-white" />
              </motion.div>
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 whitespace-nowrap">
                <span className="text-xs font-medium bg-white px-1 py-0.5 rounded shadow-sm">
                  Entregador
                </span>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Informações da entrega */}
      {routeInfo && mapLoaded && (
        <div className="absolute bottom-3 left-3 right-3 bg-white rounded-md shadow-md p-3">
          <div className="flex justify-between items-center">
            <div>
              <p className="text-sm font-medium">Distância</p>
              <p className="text-lg font-bold">{formatDistance(routeInfo.distance)}</p>
            </div>
            <div>
              <p className="text-sm font-medium">Tempo estimado</p>
              <p className="text-lg font-bold">
                {estimatedTime
                  ? `${estimatedTime} min`
                  : formatDuration(routeInfo.duration)}
              </p>
            </div>
            <Button variant="outline" size="sm" className="text-xs">
              Ver detalhes
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
