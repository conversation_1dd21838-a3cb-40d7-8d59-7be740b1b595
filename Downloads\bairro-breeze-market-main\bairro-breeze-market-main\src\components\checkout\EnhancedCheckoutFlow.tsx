import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  MapPin, 
  CreditCard, 
  CheckCircle2, 
  ChevronLeft, 
  ChevronRight, 
  Loader2,
  ShoppingBag,
  Clock,
  Truck
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useCart } from '@/hooks/useCart';
import { useAuth } from '@/hooks/useAuth';
import { toast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import { AddressForm } from '@/components/forms/AddressForm';
import { PaymentForm } from '@/components/forms/PaymentForm';
import { OrderSummary } from '@/components/forms/OrderSummary';

// Tipos de etapas do checkout
type CheckoutStep = 'address' | 'payment' | 'summary' | 'confirmation';

interface EnhancedCheckoutFlowProps {
  className?: string;
  onComplete?: (orderId: string) => void;
}

/**
 * Componente de fluxo de checkout aprimorado com animações fluidas
 */
export function EnhancedCheckoutFlow({ className, onComplete }: EnhancedCheckoutFlowProps) {
  // Estado para controlar a etapa atual do checkout
  const [step, setStep] = useState<CheckoutStep>('address');
  const [previousStep, setPreviousStep] = useState<CheckoutStep | null>(null);
  const [direction, setDirection] = useState<'forward' | 'backward'>('forward');
  
  // Estado para armazenar os dados do checkout
  const [deliveryAddress, setDeliveryAddress] = useState('');
  const [deliveryInstructions, setDeliveryInstructions] = useState('');
  const [paymentMethodId, setPaymentMethodId] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [orderId, setOrderId] = useState<string | null>(null);
  
  // Hooks
  const navigate = useNavigate();
  const { items, subtotal, clearCart } = useCart();
  const { user } = useAuth();
  
  // Verificar se o carrinho está vazio
  useEffect(() => {
    if (items.length === 0 && step !== 'confirmation') {
      toast({
        title: 'Carrinho vazio',
        description: 'Adicione produtos ao carrinho para finalizar a compra.',
        variant: 'destructive',
      });
      navigate('/');
    }
  }, [items, navigate, step]);
  
  // Manipuladores de eventos
  const handleAddressSelect = (address: string) => {
    setDeliveryAddress(address);
  };
  
  const handleInstructionsChange = (instructions: string) => {
    setDeliveryInstructions(instructions);
  };
  
  const handlePaymentMethodSelect = (methodId: string) => {
    setPaymentMethodId(methodId);
  };
  
  // Navegação entre etapas
  const goToNextStep = () => {
    if (step === 'address') {
      if (!deliveryAddress) {
        toast({
          title: 'Endereço obrigatório',
          description: 'Por favor, informe o endereço de entrega.',
          variant: 'destructive',
        });
        return;
      }
      setPreviousStep(step);
      setDirection('forward');
      setStep('payment');
    } else if (step === 'payment') {
      if (!paymentMethodId) {
        toast({
          title: 'Método de pagamento obrigatório',
          description: 'Por favor, selecione um método de pagamento.',
          variant: 'destructive',
        });
        return;
      }
      setPreviousStep(step);
      setDirection('forward');
      setStep('summary');
    }
  };
  
  const goToPreviousStep = () => {
    if (step === 'payment') {
      setPreviousStep(step);
      setDirection('backward');
      setStep('address');
    } else if (step === 'summary') {
      setPreviousStep(step);
      setDirection('backward');
      setStep('payment');
    }
  };
  
  // Finalizar pedido
  const handleOrderComplete = async () => {
    setIsProcessing(true);
    
    try {
      // Simulação de processamento de pedido
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Gerar ID de pedido aleatório
      const generatedOrderId = `ORD-${Math.floor(Math.random() * 1000000)}`;
      setOrderId(generatedOrderId);
      
      // Limpar carrinho
      clearCart();
      
      // Ir para confirmação
      setPreviousStep(step);
      setDirection('forward');
      setStep('confirmation');
      
      // Chamar callback de conclusão
      if (onComplete) {
        onComplete(generatedOrderId);
      }
    } catch (error) {
      console.error('Erro ao processar pedido:', error);
      toast({
        title: 'Erro ao processar pedido',
        description: 'Ocorreu um erro ao processar seu pedido. Tente novamente mais tarde.',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };
  
  // Variantes de animação
  const pageVariants = {
    initial: (direction: 'forward' | 'backward') => ({
      x: direction === 'forward' ? 300 : -300,
      opacity: 0,
    }),
    animate: {
      x: 0,
      opacity: 1,
      transition: {
        x: { type: 'spring', stiffness: 300, damping: 30 },
        opacity: { duration: 0.2 },
      },
    },
    exit: (direction: 'forward' | 'backward') => ({
      x: direction === 'forward' ? -300 : 300,
      opacity: 0,
      transition: {
        x: { type: 'spring', stiffness: 300, damping: 30 },
        opacity: { duration: 0.2 },
      },
    }),
  };
  
  // Calcular progresso
  const getProgress = () => {
    switch (step) {
      case 'address':
        return 0;
      case 'payment':
        return 33.33;
      case 'summary':
        return 66.66;
      case 'confirmation':
        return 100;
      default:
        return 0;
    }
  };
  
  return (
    <div className={cn('space-y-8', className)}>
      {/* Barra de progresso */}
      {step !== 'confirmation' && (
        <div className="relative h-2 bg-gray-200 rounded-full overflow-hidden">
          <motion.div
            className="absolute top-0 left-0 h-full bg-cta"
            initial={{ width: `${getProgress() - 33.33}%` }}
            animate={{ width: `${getProgress()}%` }}
            transition={{ duration: 0.5, ease: 'easeInOut' }}
          />
        </div>
      )}
      
      {/* Etapas do checkout */}
      {step !== 'confirmation' && (
        <div className="flex justify-between items-center">
          <motion.div
            className="flex flex-col items-center"
            animate={{
              opacity: step === 'address' ? 1 : 0.7,
              scale: step === 'address' ? 1.05 : 1,
            }}
          >
            <div
              className={cn(
                'w-10 h-10 rounded-full flex items-center justify-center mb-1',
                step === 'address' ? 'bg-cta text-white' : 'bg-gray-200 text-gray-500'
              )}
            >
              <MapPin className="h-5 w-5" />
            </div>
            <span className="text-xs font-medium">Endereço</span>
          </motion.div>
          
          <motion.div
            className="flex flex-col items-center"
            animate={{
              opacity: step === 'payment' ? 1 : 0.7,
              scale: step === 'payment' ? 1.05 : 1,
            }}
          >
            <div
              className={cn(
                'w-10 h-10 rounded-full flex items-center justify-center mb-1',
                step === 'payment' ? 'bg-cta text-white' : 'bg-gray-200 text-gray-500'
              )}
            >
              <CreditCard className="h-5 w-5" />
            </div>
            <span className="text-xs font-medium">Pagamento</span>
          </motion.div>
          
          <motion.div
            className="flex flex-col items-center"
            animate={{
              opacity: step === 'summary' ? 1 : 0.7,
              scale: step === 'summary' ? 1.05 : 1,
            }}
          >
            <div
              className={cn(
                'w-10 h-10 rounded-full flex items-center justify-center mb-1',
                step === 'summary' ? 'bg-cta text-white' : 'bg-gray-200 text-gray-500'
              )}
            >
              <CheckCircle2 className="h-5 w-5" />
            </div>
            <span className="text-xs font-medium">Resumo</span>
          </motion.div>
        </div>
      )}
      
      {/* Conteúdo das etapas */}
      <AnimatePresence mode="wait" custom={direction}>
        {step === 'address' && (
          <motion.div
            key="address"
            custom={direction}
            variants={pageVariants}
            initial="initial"
            animate="animate"
            exit="exit"
            className="bg-white p-6 rounded-lg border shadow-sm"
          >
            <div className="flex items-center mb-6">
              <div className="bg-cta/10 p-2 rounded-full mr-3">
                <MapPin className="h-5 w-5 text-cta" />
              </div>
              <h2 className="text-lg font-medium">Endereço de Entrega</h2>
            </div>
            
            <AddressForm
              onAddressSelect={handleAddressSelect}
              onComplete={goToNextStep}
            />
            
            <div className="mt-6 flex justify-end">
              <Button
                onClick={goToNextStep}
                className="bg-cta hover:bg-cta-dark"
                disabled={!deliveryAddress}
              >
                Continuar
                <ChevronRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </motion.div>
        )}
        
        {step === 'payment' && (
          <motion.div
            key="payment"
            custom={direction}
            variants={pageVariants}
            initial="initial"
            animate="animate"
            exit="exit"
            className="bg-white p-6 rounded-lg border shadow-sm"
          >
            <div className="flex items-center mb-6">
              <div className="bg-cta/10 p-2 rounded-full mr-3">
                <CreditCard className="h-5 w-5 text-cta" />
              </div>
              <h2 className="text-lg font-medium">Forma de Pagamento</h2>
            </div>
            
            <PaymentForm
              onPaymentMethodSelect={handlePaymentMethodSelect}
              onComplete={goToNextStep}
            />
            
            <div className="mt-6 flex justify-between">
              <Button
                variant="outline"
                onClick={goToPreviousStep}
              >
                <ChevronLeft className="mr-2 h-4 w-4" />
                Voltar
              </Button>
              
              <Button
                onClick={goToNextStep}
                className="bg-cta hover:bg-cta-dark"
                disabled={!paymentMethodId}
              >
                Continuar
                <ChevronRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </motion.div>
        )}
        
        {step === 'summary' && (
          <motion.div
            key="summary"
            custom={direction}
            variants={pageVariants}
            initial="initial"
            animate="animate"
            exit="exit"
            className="bg-white p-6 rounded-lg border shadow-sm"
          >
            <div className="flex items-center mb-6">
              <div className="bg-cta/10 p-2 rounded-full mr-3">
                <CheckCircle2 className="h-5 w-5 text-cta" />
              </div>
              <h2 className="text-lg font-medium">Resumo do Pedido</h2>
            </div>
            
            <OrderSummary
              deliveryAddress={deliveryAddress}
              deliveryInstructions={deliveryInstructions}
              paymentMethodId={paymentMethodId}
              onComplete={handleOrderComplete}
            />
            
            <div className="mt-6 flex justify-between">
              <Button
                variant="outline"
                onClick={goToPreviousStep}
                disabled={isProcessing}
              >
                <ChevronLeft className="mr-2 h-4 w-4" />
                Voltar
              </Button>
              
              <Button
                onClick={handleOrderComplete}
                className="bg-cta hover:bg-cta-dark"
                disabled={isProcessing}
              >
                {isProcessing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Processando...
                  </>
                ) : (
                  <>
                    Finalizar Compra
                    <ChevronRight className="ml-2 h-4 w-4" />
                  </>
                )}
              </Button>
            </div>
          </motion.div>
        )}
        
        {step === 'confirmation' && (
          <motion.div
            key="confirmation"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, type: 'spring', stiffness: 300, damping: 30 }}
            className="bg-white p-6 rounded-lg border shadow-sm text-center"
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: 'spring', stiffness: 300, damping: 20 }}
              className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6"
            >
              <CheckCircle2 className="h-10 w-10 text-green-600" />
            </motion.div>
            
            <h2 className="text-2xl font-bold mb-2">Pedido Confirmado!</h2>
            <p className="text-muted-foreground mb-6">
              Seu pedido #{orderId} foi recebido e está sendo processado.
            </p>
            
            <div className="space-y-4 mb-8">
              <div className="flex items-center justify-center space-x-8">
                <div className="flex flex-col items-center">
                  <div className="w-12 h-12 bg-cta/10 rounded-full flex items-center justify-center mb-2">
                    <ShoppingBag className="h-6 w-6 text-cta" />
                  </div>
                  <span className="text-sm">Pedido recebido</span>
                </div>
                
                <motion.div
                  className="w-16 h-0.5 bg-gray-200"
                  initial={{ scaleX: 0 }}
                  animate={{ scaleX: 1 }}
                  transition={{ delay: 0.5, duration: 0.5 }}
                />
                
                <div className="flex flex-col items-center">
                  <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-2">
                    <Clock className="h-6 w-6 text-gray-500" />
                  </div>
                  <span className="text-sm">Em preparo</span>
                </div>
                
                <motion.div
                  className="w-16 h-0.5 bg-gray-200"
                  initial={{ scaleX: 0 }}
                  animate={{ scaleX: 1 }}
                  transition={{ delay: 0.7, duration: 0.5 }}
                />
                
                <div className="flex flex-col items-center">
                  <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-2">
                    <Truck className="h-6 w-6 text-gray-500" />
                  </div>
                  <span className="text-sm">Em entrega</span>
                </div>
              </div>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                variant="outline"
                onClick={() => navigate('/')}
                className="flex-1 sm:flex-initial"
              >
                Continuar Comprando
              </Button>
              
              <Button
                onClick={() => navigate(`/orders/${orderId}`)}
                className="bg-cta hover:bg-cta-dark flex-1 sm:flex-initial"
              >
                Acompanhar Pedido
              </Button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
