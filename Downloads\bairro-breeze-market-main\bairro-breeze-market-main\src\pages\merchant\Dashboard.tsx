
import { useState, useEffect } from "react";
import { Navbar } from "@/components/ui/navbar";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  BarChart,
  Package,
  ShoppingBag,
  Tag,
  TrendingUp,
  User,
  Users,
} from "lucide-react";
import { ProductManagement } from "@/components/merchant/ProductManagement";
import { OrderManagement } from "@/components/merchant/OrderManagement";
import { PromotionManagement } from "@/components/merchant/PromotionManagement";
import { SalesAnalytics } from "@/components/merchant/SalesAnalytics";

interface MerchantDashboardProps {
  activeTab?: "orders" | "products" | "promotions" | "analytics";
}

const MerchantDashboard = ({ activeTab = "orders" }: MerchantDashboardProps) => {
  const [currentTab, setCurrentTab] = useState(activeTab);
  return (
    <div className="flex min-h-screen flex-col bg-gray-50">
      <header className="bg-eco border-b">
        <div className="container flex h-16 items-center justify-between px-4">
          <div className="flex items-center">
            <span className="text-xl font-bold text-white">Já Comprei</span>
            <span className="ml-2 rounded-md bg-white px-2 py-1 text-xs font-medium text-eco">
              Área do Comerciante
            </span>
          </div>
          <div className="flex items-center space-x-4">
            <Button variant="ghost" className="text-white">
              <User className="mr-2 h-4 w-4" />
              Minha Loja
            </Button>
          </div>
        </div>
      </header>

      <main className="flex-1 container px-4 py-8">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Bem-vindo, Mercado do Bairro</h1>
            <p className="text-muted-foreground">
              Gerencie seus produtos, pedidos e acompanhe suas vendas
            </p>
          </div>
          <div className="mt-4 md:mt-0 space-x-2">
            <Button variant="outline">
              <TrendingUp className="mr-2 h-4 w-4" />
              Relatórios
            </Button>
            <Button className="bg-cta hover:bg-cta-dark">
              <Tag className="mr-2 h-4 w-4" />
              Nova Promoção
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4 mb-8">
          <div className="rounded-lg border bg-card p-4">
            <div className="flex items-center space-x-2">
              <ShoppingBag className="h-4 w-4 text-cta" />
              <span className="text-sm font-medium">Pedidos Hoje</span>
            </div>
            <div className="mt-2">
              <span className="text-2xl font-bold">24</span>
              <span className="ml-2 text-xs text-green-500">+12% </span>
            </div>
          </div>
          <div className="rounded-lg border bg-card p-4">
            <div className="flex items-center space-x-2">
              <BarChart className="h-4 w-4 text-cta" />
              <span className="text-sm font-medium">Faturamento</span>
            </div>
            <div className="mt-2">
              <span className="text-2xl font-bold">R$ 1.250,90</span>
              <span className="ml-2 text-xs text-green-500">+8% </span>
            </div>
          </div>
          <div className="rounded-lg border bg-card p-4">
            <div className="flex items-center space-x-2">
              <Package className="h-4 w-4 text-cta" />
              <span className="text-sm font-medium">Produtos Ativos</span>
            </div>
            <div className="mt-2">
              <span className="text-2xl font-bold">78</span>
              <span className="ml-2 text-xs text-red-500">-2% </span>
            </div>
          </div>
          <div className="rounded-lg border bg-card p-4">
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-cta" />
              <span className="text-sm font-medium">Clientes</span>
            </div>
            <div className="mt-2">
              <span className="text-2xl font-bold">432</span>
              <span className="ml-2 text-xs text-green-500">+15% </span>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <Tabs value={currentTab} onValueChange={(value) => setCurrentTab(value as any)}>
          <TabsList className="mb-4">
            <TabsTrigger value="orders">Pedidos</TabsTrigger>
            <TabsTrigger value="products">Produtos</TabsTrigger>
            <TabsTrigger value="promotions">Promoções</TabsTrigger>
            <TabsTrigger value="analytics">Análises</TabsTrigger>
          </TabsList>
          <TabsContent value="orders" className="p-4 border rounded-lg">
            <h2 className="text-lg font-medium mb-4">Gerenciamento de Pedidos</h2>
            <OrderManagement />
          </TabsContent>
          <TabsContent value="products" className="p-4 border rounded-lg">
            <h2 className="text-lg font-medium mb-4">Gerenciamento de Produtos</h2>
            <ProductManagement />
          </TabsContent>
          <TabsContent value="promotions" className="p-4 border rounded-lg">
            <h2 className="text-lg font-medium mb-4">Gerenciamento de Promoções</h2>
            <PromotionManagement />
          </TabsContent>
          <TabsContent value="analytics" className="p-4 border rounded-lg">
            <h2 className="text-lg font-medium mb-4">Análise de Vendas</h2>
            <SalesAnalytics />
          </TabsContent>
        </Tabs>
      </main>
    </div>
  );
};

export default MerchantDashboard;
