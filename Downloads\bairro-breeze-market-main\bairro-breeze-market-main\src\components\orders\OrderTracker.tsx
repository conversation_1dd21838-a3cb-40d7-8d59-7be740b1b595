import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle
} from "@/components/ui/dialog";
import {
  CheckCircle,
  Clock,
  Package,
  ShoppingBag,
  Truck,
  MapPin,
  ChevronRight,
  Navigation,
  Phone,
  MessageCircle
} from "lucide-react";
import { DeliveryMap } from "@/components/map/DeliveryMap";
import { Order } from "@/types/order";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { motion } from "framer-motion";

interface OrderTrackerProps {
  order: Order;
}

export function OrderTracker({ order }: OrderTrackerProps) {
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [isMapOpen, setIsMapOpen] = useState(false);

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return format(date, "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });
  };

  const getStatusInfo = (status: Order["status"]) => {
    switch (status) {
      case "pending":
        return {
          label: "Pedido recebido",
          description: "Aguardando confirmação do estabelecimento",
          icon: <Clock className="h-5 w-5" />,
          color: "text-yellow-500",
          bgColor: "bg-yellow-100",
          progress: 25
        };
      case "in_progress":
        return {
          label: "Em preparo",
          description: "Seu pedido está sendo preparado e logo sairá para entrega",
          icon: <Package className="h-5 w-5" />,
          color: "text-blue-500",
          bgColor: "bg-blue-100",
          progress: 50
        };
      case "delivered":
        return {
          label: "Entregue",
          description: "Seu pedido foi entregue com sucesso",
          icon: <CheckCircle className="h-5 w-5" />,
          color: "text-green-500",
          bgColor: "bg-green-100",
          progress: 100
        };
      case "cancelled":
        return {
          label: "Cancelado",
          description: "Seu pedido foi cancelado",
          icon: <ShoppingBag className="h-5 w-5" />,
          color: "text-red-500",
          bgColor: "bg-red-100",
          progress: 0
        };
      default:
        return {
          label: "Status desconhecido",
          description: "Não foi possível determinar o status do pedido",
          icon: <ShoppingBag className="h-5 w-5" />,
          color: "text-gray-500",
          bgColor: "bg-gray-100",
          progress: 0
        };
    }
  };

  const statusInfo = getStatusInfo(order.status);

  return (
    <>
      <Card className="overflow-hidden">
        <CardContent className="p-0">
          <div className="p-4">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="font-medium">Pedido #{order.id}</h3>
                <p className="text-sm text-muted-foreground">{formatDate(order.createdAt)}</p>
              </div>
              <Badge
                variant="outline"
                className={`${statusInfo.bgColor} ${statusInfo.color} border-none`}
              >
                {statusInfo.icon}
                <span className="ml-1">{statusInfo.label}</span>
              </Badge>
            </div>

            <div className="mb-4">
              <div className="flex items-center mb-2">
                <MapPin className="h-4 w-4 mr-2 text-cta" />
                <span className="font-medium">{order.shopName}</span>
              </div>
              <div className="flex items-start">
                <MapPin className="h-4 w-4 mr-2 text-muted-foreground flex-shrink-0 mt-0.5" />
                <span className="text-sm text-muted-foreground">{order.deliveryAddress}</span>
              </div>
            </div>

            <div className="mb-4">
              <div className="relative h-2 bg-gray-100 rounded-full overflow-hidden">
                <motion.div
                  className="absolute top-0 left-0 h-full bg-cta"
                  initial={{ width: 0 }}
                  animate={{ width: `${statusInfo.progress}%` }}
                  transition={{ duration: 0.5, ease: "easeInOut" }}
                />
              </div>
              <div className="flex justify-between mt-2 text-xs text-muted-foreground">
                <span>Pedido recebido</span>
                <span>Em preparo</span>
                <span>Entregue</span>
              </div>
            </div>

            <p className="text-sm mb-4">
              {statusInfo.description}
            </p>

            <div className="flex justify-between items-center">
              <div>
                <p className="text-xs text-muted-foreground">Total</p>
                <p className="font-bold">{formatCurrency(order.total)}</p>
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsDetailsOpen(true)}
                >
                  Detalhes
                  <ChevronRight className="ml-1 h-4 w-4" />
                </Button>
                {order.status === "in_progress" && (
                  <Button
                    size="sm"
                    className="bg-cta hover:bg-cta-dark"
                    onClick={() => setIsMapOpen(true)}
                  >
                    <Truck className="mr-1 h-4 w-4" />
                    Rastrear
                  </Button>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Order Details Dialog */}
      <Dialog open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Detalhes do Pedido #{order.id}</DialogTitle>
            <DialogDescription>
              Informações completas sobre seu pedido.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm text-muted-foreground">Data do Pedido</p>
                <p className="font-medium">{formatDate(order.createdAt)}</p>
              </div>
              <Badge
                variant="outline"
                className={`${statusInfo.bgColor} ${statusInfo.color} border-none`}
              >
                {statusInfo.icon}
                <span className="ml-1">{statusInfo.label}</span>
              </Badge>
            </div>

            <div>
              <p className="text-sm text-muted-foreground mb-1">Estabelecimento</p>
              <div className="flex items-center">
                <MapPin className="h-4 w-4 mr-2 text-cta" />
                <p className="font-medium">{order.shopName}</p>
              </div>
            </div>

            <div>
              <p className="text-sm text-muted-foreground mb-1">Endereço de Entrega</p>
              <div className="flex items-start">
                <MapPin className="h-4 w-4 mr-2 text-muted-foreground flex-shrink-0 mt-0.5" />
                <p className="font-medium">{order.deliveryAddress}</p>
              </div>
            </div>

            <div>
              <p className="text-sm text-muted-foreground mb-2">Itens do Pedido</p>
              <div className="space-y-2">
                {order.items.map((item, index) => (
                  <div key={index} className="flex justify-between items-center py-2 border-b">
                    <div className="flex items-center">
                      <div className="mr-3 font-medium">{item.quantity}x</div>
                      <div>{item.name}</div>
                    </div>
                    <div className="font-medium">{formatCurrency(item.price * item.quantity)}</div>
                  </div>
                ))}
                <div className="flex justify-between items-center pt-2">
                  <div className="font-medium">Total</div>
                  <div className="font-bold">{formatCurrency(order.total)}</div>
                </div>
              </div>
            </div>

            {order.status === "in_progress" && (
              <Button
                className="w-full bg-cta hover:bg-cta-dark"
                onClick={() => {
                  setIsDetailsOpen(false);
                  setIsMapOpen(true);
                }}
              >
                <Truck className="mr-2 h-4 w-4" />
                Rastrear Entrega
              </Button>
            )}
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDetailsOpen(false)}>
              Fechar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Tracking Map Dialog */}
      <Dialog open={isMapOpen} onOpenChange={setIsMapOpen}>
        <DialogContent className="sm:max-w-[800px] sm:h-[600px]">
          <DialogHeader>
            <DialogTitle>Rastreamento de Entrega</DialogTitle>
            <DialogDescription>
              Acompanhe em tempo real a entrega do seu pedido #{order.id}.
            </DialogDescription>
          </DialogHeader>

          <DeliveryMap order={order} delivererName="Carlos Silva" estimatedTime={15} />

          <div className="mt-4 space-y-4">
            <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center">
                <div className="h-10 w-10 rounded-full bg-trust-light flex items-center justify-center mr-3">
                  <Truck className="h-5 w-5 text-trust" />
                </div>
                <div>
                  <p className="font-medium">Carlos Silva</p>
                  <p className="text-sm text-muted-foreground">Entregador</p>
                </div>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Tempo estimado</p>
                <p className="font-bold text-lg">15 min</p>
              </div>
            </div>

            <div className="flex gap-2">
              <Button className="flex-1 bg-trust hover:bg-trust-dark">
                <Phone className="mr-2 h-4 w-4" />
                Ligar
              </Button>
              <Button variant="outline" className="flex-1">
                <MessageCircle className="mr-2 h-4 w-4" />
                Mensagem
              </Button>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsMapOpen(false)}>
              Fechar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
