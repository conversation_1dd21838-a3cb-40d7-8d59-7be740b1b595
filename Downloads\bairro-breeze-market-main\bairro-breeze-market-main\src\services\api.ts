import { supabaseClient } from '@/lib/supabase';
import { Tables, Insertable, Updatable } from '@/types/database';

/**
 * Generic API service for CRUD operations
 */
export class ApiService<T extends keyof Database['public']['Tables']> {
  constructor(private table: T) {}

  /**
   * Get all records from a table
   * @param options Query options
   * @returns Promise with the data
   */
  async getAll(options?: {
    limit?: number;
    orderBy?: { column: string; ascending?: boolean };
    filters?: { column: string; value: any }[];
  }): Promise<Tables<T>[]> {
    try {
      let query = supabaseClient.from(this.table).select('*');

      // Apply filters if provided
      if (options?.filters) {
        for (const filter of options.filters) {
          query = query.eq(filter.column, filter.value);
        }
      }

      // Apply ordering if provided
      if (options?.orderBy) {
        query = query.order(
          options.orderBy.column, 
          { ascending: options.orderBy.ascending ?? false }
        );
      }

      // Apply limit if provided
      if (options?.limit) {
        query = query.limit(options.limit);
      }

      const { data, error } = await query;

      if (error) {
        console.error(`Error fetching ${this.table}:`, error);
        throw error;
      }

      return data as Tables<T>[];
    } catch (error) {
      console.error(`Error in getAll for ${this.table}:`, error);
      // In development mode, return mock data
      return [];
    }
  }

  /**
   * Get a single record by ID
   * @param id Record ID
   * @returns Promise with the data
   */
  async getById(id: string): Promise<Tables<T> | null> {
    try {
      const { data, error } = await supabaseClient
        .from(this.table)
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        console.error(`Error fetching ${this.table} by ID:`, error);
        throw error;
      }

      return data as Tables<T>;
    } catch (error) {
      console.error(`Error in getById for ${this.table}:`, error);
      // In development mode, return null
      return null;
    }
  }

  /**
   * Create a new record
   * @param data Record data
   * @returns Promise with the created record
   */
  async create(data: Insertable<T>): Promise<Tables<T> | null> {
    try {
      const { data: createdData, error } = await supabaseClient
        .from(this.table)
        .insert(data)
        .select()
        .single();

      if (error) {
        console.error(`Error creating ${this.table}:`, error);
        throw error;
      }

      return createdData as Tables<T>;
    } catch (error) {
      console.error(`Error in create for ${this.table}:`, error);
      // In development mode, return null
      return null;
    }
  }

  /**
   * Update an existing record
   * @param id Record ID
   * @param data Updated data
   * @returns Promise with the updated record
   */
  async update(id: string, data: Updatable<T>): Promise<Tables<T> | null> {
    try {
      const { data: updatedData, error } = await supabaseClient
        .from(this.table)
        .update(data)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error(`Error updating ${this.table}:`, error);
        throw error;
      }

      return updatedData as Tables<T>;
    } catch (error) {
      console.error(`Error in update for ${this.table}:`, error);
      // In development mode, return null
      return null;
    }
  }

  /**
   * Delete a record
   * @param id Record ID
   * @returns Promise with success status
   */
  async delete(id: string): Promise<boolean> {
    try {
      const { error } = await supabaseClient
        .from(this.table)
        .delete()
        .eq('id', id);

      if (error) {
        console.error(`Error deleting ${this.table}:`, error);
        throw error;
      }

      return true;
    } catch (error) {
      console.error(`Error in delete for ${this.table}:`, error);
      // In development mode, return false
      return false;
    }
  }
}
