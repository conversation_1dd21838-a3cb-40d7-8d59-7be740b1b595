import React, { ReactNode, useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, Home, Menu, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { ScrollProgress } from '@/components/animations/ScrollAnimations';
import { useMediaQuery } from '@/hooks/use-breakpoint';

interface PageTransitionLayoutProps {
  children: ReactNode;
  title?: string;
  showBackButton?: boolean;
  showHomeButton?: boolean;
  showHeader?: boolean;
  showFooter?: boolean;
  className?: string;
  contentClassName?: string;
  headerClassName?: string;
  footerClassName?: string;
  transitionType?: 'fade' | 'slide' | 'zoom' | 'none';
  onBack?: () => void;
  headerContent?: ReactNode;
  footerContent?: ReactNode;
  showScrollProgress?: boolean;
}

/**
 * Componente de layout com transições de página fluidas
 */
export function PageTransitionLayout({
  children,
  title,
  showBackButton = false,
  showHomeButton = false,
  showHeader = true,
  showFooter = true,
  className,
  contentClassName,
  headerClassName,
  footerClassName,
  transitionType = 'slide',
  onBack,
  headerContent,
  footerContent,
  showScrollProgress = true,
}: PageTransitionLayoutProps) {
  const location = useLocation();
  const navigate = useNavigate();
  const [previousLocation, setPreviousLocation] = useState(location);
  const [direction, setDirection] = useState<'forward' | 'backward'>('forward');
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const isMobile = !useMediaQuery('md');
  
  // Detectar direção da navegação
  useEffect(() => {
    if (location !== previousLocation) {
      const isPreviousLocationInHistory = window.history.state?.idx < window.history.state?.oldIdx;
      setDirection(isPreviousLocationInHistory ? 'backward' : 'forward');
      setPreviousLocation(location);
    }
  }, [location, previousLocation]);
  
  // Fechar menu ao mudar de página
  useEffect(() => {
    setIsMenuOpen(false);
  }, [location.pathname]);
  
  // Variantes de animação baseadas no tipo de transição
  const getVariants = () => {
    switch (transitionType) {
      case 'fade':
        return {
          initial: { opacity: 0 },
          animate: { opacity: 1 },
          exit: { opacity: 0 },
        };
      case 'slide':
        return {
          initial: (direction: 'forward' | 'backward') => ({
            x: direction === 'forward' ? '100%' : '-100%',
            opacity: 0,
          }),
          animate: {
            x: 0,
            opacity: 1,
          },
          exit: (direction: 'forward' | 'backward') => ({
            x: direction === 'forward' ? '-100%' : '100%',
            opacity: 0,
          }),
        };
      case 'zoom':
        return {
          initial: { opacity: 0, scale: 0.95 },
          animate: { opacity: 1, scale: 1 },
          exit: { opacity: 0, scale: 1.05 },
        };
      case 'none':
      default:
        return {
          initial: { opacity: 1 },
          animate: { opacity: 1 },
          exit: { opacity: 1 },
        };
    }
  };
  
  // Configurações de transição
  const getTransition = () => {
    switch (transitionType) {
      case 'slide':
        return {
          x: { type: 'spring', stiffness: 300, damping: 30 },
          opacity: { duration: 0.2 },
        };
      case 'zoom':
        return {
          type: 'spring',
          stiffness: 300,
          damping: 30,
        };
      case 'fade':
      default:
        return {
          duration: 0.2,
        };
    }
  };
  
  // Variantes de animação para o menu mobile
  const menuVariants = {
    closed: {
      x: '100%',
      opacity: 0,
      transition: {
        type: 'spring',
        stiffness: 400,
        damping: 40,
      },
    },
    open: {
      x: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 400,
        damping: 40,
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };
  
  // Variantes de animação para itens do menu
  const menuItemVariants = {
    closed: { x: 20, opacity: 0 },
    open: { x: 0, opacity: 1 },
  };
  
  // Manipulador de botão voltar
  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      navigate(-1);
    }
  };
  
  // Manipulador de botão home
  const handleHome = () => {
    navigate('/');
  };
  
  return (
    <div className={cn('min-h-screen flex flex-col', className)}>
      {/* Barra de progresso de rolagem */}
      {showScrollProgress && <ScrollProgress />}
      
      {/* Cabeçalho */}
      {showHeader && (
        <header className={cn(
          'sticky top-0 z-40 bg-white border-b shadow-sm',
          headerClassName
        )}>
          <div className="container px-4 h-16 flex items-center justify-between">
            <div className="flex items-center">
              {showBackButton && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleBack}
                  className="mr-2"
                >
                  <ChevronLeft className="h-5 w-5" />
                </Button>
              )}
              
              {showHomeButton && (
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleHome}
                  className="mr-2"
                >
                  <Home className="h-5 w-5" />
                </Button>
              )}
              
              {title && (
                <h1 className="text-xl font-medium">{title}</h1>
              )}
            </div>
            
            {headerContent || (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsMenuOpen(!isMenuOpen)}
                className="md:hidden"
              >
                <AnimatePresence mode="wait" initial={false}>
                  <motion.div
                    key={isMenuOpen ? 'close' : 'menu'}
                    initial={{ opacity: 0, rotate: isMenuOpen ? -90 : 90 }}
                    animate={{ opacity: 1, rotate: 0 }}
                    exit={{ opacity: 0, rotate: isMenuOpen ? 90 : -90 }}
                    transition={{ duration: 0.2 }}
                  >
                    {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
                  </motion.div>
                </AnimatePresence>
              </Button>
            )}
          </div>
        </header>
      )}
      
      {/* Menu mobile */}
      <AnimatePresence>
        {isMenuOpen && isMobile && (
          <motion.div
            className="fixed inset-0 z-30 bg-black/50"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setIsMenuOpen(false)}
          >
            <motion.div
              className="absolute top-16 right-0 bottom-0 w-3/4 max-w-xs bg-white shadow-xl"
              variants={menuVariants}
              initial="closed"
              animate="open"
              exit="closed"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="p-4 space-y-4">
                {/* Aqui você pode adicionar itens de menu */}
                <motion.div variants={menuItemVariants}>
                  <Button
                    variant="ghost"
                    className="w-full justify-start"
                    onClick={() => navigate('/')}
                  >
                    <Home className="mr-2 h-5 w-5" />
                    Início
                  </Button>
                </motion.div>
                
                {/* Adicione mais itens de menu conforme necessário */}
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Conteúdo principal com animação */}
      <main className={cn('flex-1', contentClassName)}>
        <AnimatePresence mode="wait" custom={direction}>
          <motion.div
            key={location.pathname}
            custom={direction}
            variants={getVariants()}
            initial="initial"
            animate="animate"
            exit="exit"
            transition={getTransition()}
            className="min-h-full"
          >
            {children}
          </motion.div>
        </AnimatePresence>
      </main>
      
      {/* Rodapé */}
      {showFooter && (
        <footer className={cn(
          'bg-white border-t py-4',
          footerClassName
        )}>
          <div className="container px-4">
            {footerContent || (
              <div className="text-center text-sm text-muted-foreground">
                &copy; {new Date().getFullYear()} Já Comprei. Todos os direitos reservados.
              </div>
            )}
          </div>
        </footer>
      )}
    </div>
  );
}
