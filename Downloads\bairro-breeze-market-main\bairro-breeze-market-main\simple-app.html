<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Já Comprei - Marketplace Local</title>
  <style>
    /* Reset básico */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: #333;
      background-color: #f5f5f5;
    }

    .app-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }

    /* Header */
    header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 0;
      border-bottom: 1px solid #eee;
    }

    header h1 {
      color: #4CAF50;
      font-size: 24px;
    }

    .cart-icon {
      font-size: 24px;
      position: relative;
      cursor: pointer;
    }

    .cart-count {
      position: absolute;
      top: -10px;
      right: -10px;
      background-color: #FF5722;
      color: white;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
    }

    /* Hero section */
    .hero {
      text-align: center;
      padding: 60px 0;
      background-color: #e8f5e9;
      border-radius: 8px;
      margin: 20px 0;
    }

    .hero h2 {
      font-size: 28px;
      margin-bottom: 16px;
      color: #2E7D32;
    }

    .hero p {
      font-size: 18px;
      color: #555;
      margin-bottom: 24px;
    }

    .cta-button {
      background-color: #4CAF50;
      color: white;
      border: none;
      padding: 12px 24px;
      font-size: 16px;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.3s;
    }

    .cta-button:hover {
      background-color: #388E3C;
    }

    /* Products section */
    .products {
      padding: 40px 0;
    }

    .products h2 {
      text-align: center;
      margin-bottom: 30px;
      color: #333;
    }

    .product-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
      gap: 30px;
    }

    .product-card {
      background-color: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s;
    }

    .product-card:hover {
      transform: translateY(-5px);
    }

    .product-card img {
      width: 100%;
      height: 200px;
      object-fit: cover;
    }

    .product-card h3 {
      padding: 16px 16px 8px;
      font-size: 18px;
    }

    .product-card .price {
      padding: 0 16px 16px;
      font-weight: bold;
      color: #4CAF50;
    }

    .product-card button {
      width: 100%;
      padding: 12px;
      background-color: #4CAF50;
      color: white;
      border: none;
      cursor: pointer;
      transition: background-color 0.3s;
    }

    .product-card button:hover {
      background-color: #388E3C;
    }

    /* Cart section */
    .cart {
      margin: 40px 0;
      background-color: white;
      border-radius: 8px;
      padding: 20px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      display: none;
    }

    .cart.active {
      display: block;
    }

    .cart h2 {
      margin-bottom: 20px;
      color: #333;
    }

    .cart-items {
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    .cart-item {
      display: flex;
      align-items: center;
      padding: 10px;
      border-bottom: 1px solid #eee;
    }

    .cart-item img {
      width: 60px;
      height: 60px;
      object-fit: cover;
      border-radius: 4px;
      margin-right: 15px;
    }

    .item-details {
      flex: 1;
    }

    .item-details h3 {
      font-size: 16px;
      margin-bottom: 5px;
    }

    .cart-item button {
      background-color: #f44336;
      color: white;
      border: none;
      padding: 8px 12px;
      border-radius: 4px;
      cursor: pointer;
    }

    .cart-total {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 20px;
      padding-top: 20px;
      border-top: 1px solid #eee;
    }

    .cart-total p {
      font-size: 18px;
      font-weight: bold;
    }

    .checkout-button {
      background-color: #FF5722;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
    }

    /* Footer */
    footer {
      text-align: center;
      padding: 20px 0;
      margin-top: 40px;
      border-top: 1px solid #eee;
      color: #777;
    }

    /* Responsive */
    @media (max-width: 768px) {
      .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      }
      
      .hero {
        padding: 40px 20px;
      }
      
      .hero h2 {
        font-size: 24px;
      }
      
      .hero p {
        font-size: 16px;
      }
    }
  </style>
</head>
<body>
  <div class="app-container">
    <header>
      <h1>Já Comprei</h1>
      <div class="cart-icon" id="cart-icon">
        🛒 <span class="cart-count" id="cart-count">0</span>
      </div>
    </header>

    <main>
      <section class="hero">
        <h2>Marketplace local que conecta clientes, comerciantes e entregadores</h2>
        <p>Encontre produtos de lojas locais e receba em minutos</p>
        <button class="cta-button">Explorar produtos</button>
      </section>

      <section class="products">
        <h2>Produtos em destaque</h2>
        <div class="product-grid" id="product-grid">
          <!-- Produtos serão adicionados aqui via JavaScript -->
        </div>
      </section>

      <section class="cart" id="cart-section">
        <h2>Seu carrinho</h2>
        <div class="cart-items" id="cart-items">
          <!-- Itens do carrinho serão adicionados aqui via JavaScript -->
        </div>
        <div class="cart-total">
          <p>Total: <span id="cart-total">R$ 0,00</span></p>
          <button class="checkout-button">Finalizar compra</button>
        </div>
      </section>
    </main>

    <footer>
      <p>&copy; 2024 Já Comprei. Todos os direitos reservados.</p>
    </footer>
  </div>

  <script>
    // Dados dos produtos
    const products = [
      {
        id: 1,
        name: 'Pão Francês',
        price: 0.75,
        image: 'https://images.unsplash.com/photo-1608198093002-ad4e005484ec?auto=format&fit=crop&w=300&h=300'
      },
      {
        id: 2,
        name: 'Filé Mignon Premium',
        price: 69.90,
        image: 'https://images.unsplash.com/photo-1588168333986-5078d3ae3976?auto=format&fit=crop&w=300&h=300'
      },
      {
        id: 3,
        name: 'Café Especial 250g',
        price: 24.90,
        image: 'https://images.unsplash.com/photo-1559056199-641a0ac8b55e?auto=format&fit=crop&w=300&h=300'
      }
    ];

    // Carrinho de compras
    let cart = [];

    // Formatar preço
    function formatCurrency(value) {
      return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
      }).format(value);
    }

    // Renderizar produtos
    function renderProducts() {
      const productGrid = document.getElementById('product-grid');
      productGrid.innerHTML = '';

      products.forEach(product => {
        const productCard = document.createElement('div');
        productCard.className = 'product-card';
        productCard.innerHTML = `
          <img src="${product.image}" alt="${product.name}">
          <h3>${product.name}</h3>
          <p class="price">${formatCurrency(product.price)}</p>
          <button onclick="addToCart(${product.id})">Adicionar ao carrinho</button>
        `;
        productGrid.appendChild(productCard);
      });
    }

    // Adicionar ao carrinho
    function addToCart(productId) {
      const product = products.find(p => p.id === productId);
      if (product) {
        cart.push(product);
        updateCart();
      }
    }

    // Remover do carrinho
    function removeFromCart(index) {
      cart.splice(index, 1);
      updateCart();
    }

    // Atualizar carrinho
    function updateCart() {
      const cartCount = document.getElementById('cart-count');
      const cartItems = document.getElementById('cart-items');
      const cartTotal = document.getElementById('cart-total');
      const cartSection = document.getElementById('cart-section');

      // Atualizar contador
      cartCount.textContent = cart.length;

      // Mostrar ou esconder seção do carrinho
      if (cart.length > 0) {
        cartSection.classList.add('active');
      } else {
        cartSection.classList.remove('active');
      }

      // Atualizar itens
      cartItems.innerHTML = '';
      cart.forEach((item, index) => {
        const cartItem = document.createElement('div');
        cartItem.className = 'cart-item';
        cartItem.innerHTML = `
          <img src="${item.image}" alt="${item.name}">
          <div class="item-details">
            <h3>${item.name}</h3>
            <p>${formatCurrency(item.price)}</p>
          </div>
          <button onclick="removeFromCart(${index})">Remover</button>
        `;
        cartItems.appendChild(cartItem);
      });

      // Atualizar total
      const total = cart.reduce((sum, item) => sum + item.price, 0);
      cartTotal.textContent = formatCurrency(total);
    }

    // Inicializar
    document.addEventListener('DOMContentLoaded', () => {
      renderProducts();
      
      // Mostrar/esconder carrinho ao clicar no ícone
      document.getElementById('cart-icon').addEventListener('click', () => {
        const cartSection = document.getElementById('cart-section');
        cartSection.classList.toggle('active');
      });
    });
  </script>
</body>
</html>
