import React, { useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';

interface SkipToContentProps {
  contentId: string;
  className?: string;
  children?: React.ReactNode;
}

/**
 * Componente para pular para o conteúdo principal (acessibilidade)
 */
export function SkipToContent({
  contentId,
  className,
  children = 'Pular para o conteúdo principal',
}: SkipToContentProps) {
  return (
    <a
      href={`#${contentId}`}
      className={cn(
        'sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:p-4 focus:bg-white focus:shadow-md focus:rounded-md focus:outline-none focus:ring-2 focus:ring-primary',
        className
      )}
    >
      {children}
    </a>
  );
}

interface FocusTrapProps {
  children: React.ReactNode;
  active?: boolean;
  className?: string;
}

/**
 * Componente para prender o foco dentro de um elemento (modais, diálogos, etc.)
 */
export function FocusTrap({
  children,
  active = true,
  className,
}: FocusTrapProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const startSentinelRef = useRef<HTMLDivElement>(null);
  const endSentinelRef = useRef<HTMLDivElement>(null);

  // Gerenciar foco quando o componente é montado
  useEffect(() => {
    if (!active) return;

    const container = containerRef.current;
    if (!container) return;

    // Encontrar todos os elementos focáveis dentro do container
    const focusableElements = container.querySelectorAll<HTMLElement>(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );

    if (focusableElements.length === 0) return;

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    // Focar o primeiro elemento quando o componente é montado
    firstElement.focus();

    // Função para lidar com a tecla Tab
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      // Shift + Tab no primeiro elemento focável
      if (e.shiftKey && document.activeElement === firstElement) {
        e.preventDefault();
        lastElement.focus();
      }
      // Tab no último elemento focável
      else if (!e.shiftKey && document.activeElement === lastElement) {
        e.preventDefault();
        firstElement.focus();
      }
    };

    // Adicionar event listener para keydown
    document.addEventListener('keydown', handleKeyDown);

    // Limpar event listener
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [active]);

  return (
    <div ref={containerRef} className={className}>
      {/* Sentinela de início para prender o foco */}
      <div
        ref={startSentinelRef}
        tabIndex={active ? 0 : -1}
        aria-hidden="true"
        style={{ position: 'absolute', width: 1, height: 1, padding: 0, overflow: 'hidden', clip: 'rect(0, 0, 0, 0)', whiteSpace: 'nowrap', border: 0 }}
      />
      
      {children}
      
      {/* Sentinela de fim para prender o foco */}
      <div
        ref={endSentinelRef}
        tabIndex={active ? 0 : -1}
        aria-hidden="true"
        style={{ position: 'absolute', width: 1, height: 1, padding: 0, overflow: 'hidden', clip: 'rect(0, 0, 0, 0)', whiteSpace: 'nowrap', border: 0 }}
      />
    </div>
  );
}

interface VisuallyHiddenProps {
  children: React.ReactNode;
  as?: React.ElementType;
}

/**
 * Componente para esconder visualmente conteúdo, mas mantê-lo acessível para leitores de tela
 */
export function VisuallyHidden({
  children,
  as: Component = 'span',
}: VisuallyHiddenProps) {
  return (
    <Component
      className="sr-only"
      aria-hidden="false"
    >
      {children}
    </Component>
  );
}

interface LiveRegionProps {
  children: React.ReactNode;
  'aria-live'?: 'polite' | 'assertive' | 'off';
  'aria-atomic'?: boolean;
  'aria-relevant'?: 'additions' | 'removals' | 'text' | 'all';
  className?: string;
}

/**
 * Componente para anunciar mudanças para leitores de tela
 */
export function LiveRegion({
  children,
  'aria-live': ariaLive = 'polite',
  'aria-atomic': ariaAtomic = true,
  'aria-relevant': ariaRelevant = 'additions text',
  className,
}: LiveRegionProps) {
  return (
    <div
      aria-live={ariaLive}
      aria-atomic={ariaAtomic}
      aria-relevant={ariaRelevant}
      className={cn('sr-only', className)}
    >
      {children}
    </div>
  );
}
