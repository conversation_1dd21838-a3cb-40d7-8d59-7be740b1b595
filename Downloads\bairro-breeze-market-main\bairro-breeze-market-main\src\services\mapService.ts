/**
 * Serviços básicos de mapa para desenvolvimento
 */

// Interfaces
export interface Location {
  latitude: number;
  longitude: number;
  address?: string;
}

export interface Coordinates {
  lat: number;
  lng: number;
}

export interface Route {
  origin: Location;
  destination: Location;
  waypoints?: Location[];
  distance: number;
  duration: number;
  polyline: string;
}

// Mock locations
const mockLocations: Record<string, Location> = {
  'shop1': { latitude: -23.5505, longitude: -46.6333, address: 'Rua das Flores, 123 - Centro' },
  'shop2': { latitude: -23.5605, longitude: -46.6433, address: 'Av. Principal, 456 - Vila Nova' },
  'customer1': { latitude: -23.5555, longitude: -46.6383, address: 'Rua Augusta, 567 - Consolação' },
  'deliverer1': { latitude: -23.5605, longitude: -46.6383 }
};

/**
 * Get current location
 */
export const getCurrentLocation = async (): Promise<Location> => {
  try {
    if (navigator.geolocation) {
      return new Promise((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(
          position => {
            resolve({
              latitude: position.coords.latitude,
              longitude: position.coords.longitude
            });
          },
          error => {
            console.error('Error getting current location:', error);
            resolve(mockLocations.deliverer1);
          },
          { enableHighAccuracy: true, timeout: 5000, maximumAge: 0 }
        );
      });
    } else {
      return mockLocations.deliverer1;
    }
  } catch (error) {
    console.error('Error in getCurrentLocation:', error);
    return mockLocations.deliverer1;
  }
};

/**
 * Get location by address
 */
export const getLocationByAddress = async (address: string): Promise<Location | null> => {
  try {
    // Find a mock location with a matching address
    for (const key in mockLocations) {
      if (mockLocations[key].address?.includes(address)) {
        return mockLocations[key];
      }
    }

    // If no match, return a random location
    const keys = Object.keys(mockLocations);
    const randomKey = keys[Math.floor(Math.random() * keys.length)];
    return {
      ...mockLocations[randomKey],
      address
    };
  } catch (error) {
    console.error(`Error geocoding address "${address}":`, error);
    return null;
  }
};

/**
 * Calculate route between two locations
 */
export const calculateRoute = async (
  origin: Location,
  destination: Location,
  waypoints?: Location[]
): Promise<Route | null> => {
  try {
    // Calculate mock distance and duration
    const distance = Math.sqrt(
      Math.pow(destination.latitude - origin.latitude, 2) +
      Math.pow(destination.longitude - origin.longitude, 2)
    ) * 111000; // Convert degrees to meters (roughly)

    const duration = distance / 5; // Assume 5 m/s (18 km/h)

    return {
      origin,
      destination,
      waypoints,
      distance,
      duration,
      polyline: 'mock_polyline'
    };
  } catch (error) {
    console.error('Error calculating route:', error);
    return null;
  }
};

/**
 * Track location updates
 */
export const trackLocation = (
  callback: (location: Location) => void
): () => void => {
  try {
    // For development, simulate movement
    let currentLocation = { ...mockLocations.deliverer1 };

    const intervalId = setInterval(() => {
      // Simulate random movement
      currentLocation = {
        latitude: currentLocation.latitude + (Math.random() - 0.5) * 0.001,
        longitude: currentLocation.longitude + (Math.random() - 0.5) * 0.001
      };

      callback(currentLocation);
    }, 5000);

    return () => clearInterval(intervalId);
  } catch (error) {
    console.error('Error in trackLocation:', error);
    
    // Return a no-op function
    return () => {};
  }
};
