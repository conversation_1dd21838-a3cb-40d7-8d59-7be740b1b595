import { ApiService } from './api';
import { Tables, Insertable, Updatable } from '@/types/database';
import { supabaseClient } from '@/lib/supabase';

// Create a typed service for products
class ProductService extends ApiService<'products'> {
  constructor() {
    super('products');
  }

  /**
   * Get products by shop ID
   * @param shopId Shop ID
   * @returns Promise with products
   */
  async getByShopId(shopId: string): Promise<Tables<'products'>[]> {
    try {
      const { data, error } = await supabaseClient
        .from('products')
        .select('*')
        .eq('shop_id', shopId)
        .eq('active', true)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching products by shop ID:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in getByShopId:', error);
      return [];
    }
  }

  /**
   * Get products by category
   * @param category Category name
   * @returns Promise with products
   */
  async getByCategory(category: string): Promise<Tables<'products'>[]> {
    try {
      const { data, error } = await supabaseClient
        .from('products')
        .select('*')
        .eq('category', category)
        .eq('active', true)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching products by category:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in getByCategory:', error);
      return [];
    }
  }

  /**
   * Get promotional products
   * @param limit Number of products to return
   * @returns Promise with promotional products
   */
  async getPromotions(limit = 10): Promise<Tables<'products'>[]> {
    try {
      const { data, error } = await supabaseClient
        .from('products')
        .select('*')
        .eq('is_promo', true)
        .eq('active', true)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching promotional products:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in getPromotions:', error);
      return [];
    }
  }

  /**
   * Search products by name or description
   * @param query Search query
   * @returns Promise with matching products
   */
  async search(query: string): Promise<Tables<'products'>[]> {
    try {
      const { data, error } = await supabaseClient
        .from('products')
        .select('*')
        .or(`name.ilike.%${query}%,description.ilike.%${query}%`)
        .eq('active', true)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error searching products:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in search:', error);
      return [];
    }
  }
}

export const productService = new ProductService();
