
import { <PERSON><PERSON>in, ChevronDown } from "lucide-react";
import { But<PERSON> } from "./button";
import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "./dialog";
import { Input } from "./input";
import { Label } from "./label";
import { 
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "./form";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useToast } from "@/hooks/use-toast";

const addressSchema = z.object({
  street: z.string().min(3, "Endereço deve ter pelo menos 3 caracteres"),
  neighborhood: z.string().min(2, "Bairro deve ter pelo menos 2 caracteres"),
  city: z.string().min(2, "Cidade deve ter pelo menos 2 caracteres"),
  state: z.string().length(2, "Estado deve ter 2 caracteres (ex: SP)")
});

type AddressFormValues = z.infer<typeof addressSchema>;

interface Address {
  street: string;
  neighborhood: string;
  city: string;
  state: string;
}

export function AddressBar() {
  const [address, setAddress] = useState<Address>({
    street: "Rua das Flores, 123",
    neighborhood: "Jardim Primavera",
    city: "São Paulo",
    state: "SP"
  });

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const { toast } = useToast();

  const form = useForm<AddressFormValues>({
    resolver: zodResolver(addressSchema),
    defaultValues: address
  });

  function onSubmit(values: AddressFormValues) {
    const updatedAddress: Address = {
      street: values.street,
      neighborhood: values.neighborhood,
      city: values.city,
      state: values.state
    };
    
    setAddress(updatedAddress);
    setIsDialogOpen(false);
    toast({
      title: "Endereço atualizado",
      description: "Seu endereço de entrega foi atualizado com sucesso.",
    });
  }

  const fullAddress = `${address.street} - ${address.neighborhood}`;

  return (
    <div className="bg-eco-light px-4 py-2 flex items-center justify-between">
      <div className="flex items-center">
        <MapPin className="w-4 h-4 text-eco mr-2" />
        <div>
          <p className="text-xs text-muted-foreground">Entregar em</p>
          <div className="flex items-center">
            <p className="text-sm font-medium">{fullAddress}</p>
            <ChevronDown className="h-3 w-3 ml-1 text-muted-foreground" />
          </div>
        </div>
      </div>
      
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger asChild>
          <Button variant="ghost" size="sm" className="text-xs text-eco">
            Alterar
          </Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Atualizar endereço de entrega</DialogTitle>
          </DialogHeader>
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="street"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Rua e número</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Ex: Av. Brasil, 123" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="neighborhood"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Bairro</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="Ex: Centro" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="grid grid-cols-2 gap-3">
                <FormField
                  control={form.control}
                  name="city"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Cidade</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Ex: São Paulo" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="state"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Estado</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Ex: SP" maxLength={2} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <div className="flex justify-end space-x-2 pt-4">
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setIsDialogOpen(false)}
                >
                  Cancelar
                </Button>
                <Button type="submit" className="bg-eco text-white hover:bg-eco/90">
                  Salvar endereço
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
