
import { cn } from "@/lib/utils";
import { LucideIcon } from "lucide-react";

interface CategoryCardProps {
  name: string;
  icon: LucideIcon;
  className?: string;
}

export function CategoryCard({ name, icon: Icon, className }: CategoryCardProps) {
  return (
    <div 
      className={cn(
        "flex flex-col items-center justify-center p-3 transition-all rounded-lg cursor-pointer hover:bg-eco-light",
        className
      )}
    >
      <div className="flex items-center justify-center w-12 h-12 mb-2 rounded-full bg-eco-light">
        <Icon className="w-6 h-6 text-eco" />
      </div>
      <span className="text-sm font-medium text-center">{name}</span>
    </div>
  );
}
