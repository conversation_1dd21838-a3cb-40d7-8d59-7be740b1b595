import React from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { cn } from '@/lib/utils';

interface SkeletonCardProps {
  className?: string;
  imageHeight?: number | string;
  lines?: number;
  hasFooter?: boolean;
  hasHeader?: boolean;
  rounded?: boolean;
}

/**
 * Componente de skeleton para cards
 */
export function SkeletonCard({
  className,
  imageHeight = 200,
  lines = 3,
  hasFooter = true,
  hasHeader = true,
  rounded = true,
}: SkeletonCardProps) {
  return (
    <div className={cn(
      'border bg-card overflow-hidden',
      rounded ? 'rounded-lg' : '',
      className
    )}>
      {hasHeader && (
        <div className="p-4 border-b">
          <div className="flex items-center space-x-4">
            <Skeleton className="h-10 w-10 rounded-full" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-[150px]" />
              <Skeleton className="h-3 w-[100px]" />
            </div>
          </div>
        </div>
      )}
      
      <Skeleton className="w-full" style={{ height: imageHeight }} />
      
      <div className="p-4 space-y-3">
        <Skeleton className="h-5 w-[80%]" />
        
        {Array.from({ length: lines }).map((_, i) => (
          <Skeleton 
            key={i} 
            className="h-4" 
            style={{ width: `${Math.max(50, 100 - (i * 10))}%` }} 
          />
        ))}
      </div>
      
      {hasFooter && (
        <div className="p-4 border-t flex justify-between items-center">
          <Skeleton className="h-9 w-[100px]" />
          <Skeleton className="h-9 w-9 rounded-full" />
        </div>
      )}
    </div>
  );
}

interface SkeletonListProps {
  className?: string;
  count?: number;
  itemClassName?: string;
  itemHeight?: number | string;
  gap?: number | string;
}

/**
 * Componente de skeleton para listas
 */
export function SkeletonList({
  className,
  count = 5,
  itemClassName,
  itemHeight = 60,
  gap = 4,
}: SkeletonListProps) {
  return (
    <div 
      className={cn('space-y-4', className)}
      style={{ gap }}
    >
      {Array.from({ length: count }).map((_, i) => (
        <div 
          key={i} 
          className={cn(
            'flex items-center border rounded-md p-3 bg-card',
            itemClassName
          )}
          style={{ height: itemHeight }}
        >
          <Skeleton className="h-10 w-10 rounded-full mr-3" />
          <div className="space-y-2 flex-1">
            <Skeleton className="h-4 w-[60%]" />
            <Skeleton className="h-3 w-[40%]" />
          </div>
          <Skeleton className="h-8 w-[80px] ml-2" />
        </div>
      ))}
    </div>
  );
}

interface SkeletonGridProps {
  className?: string;
  columns?: number;
  count?: number;
  itemClassName?: string;
  itemHeight?: number | string;
  gap?: number | string;
}

/**
 * Componente de skeleton para grids
 */
export function SkeletonGrid({
  className,
  columns = 3,
  count = 6,
  itemClassName,
  itemHeight = 200,
  gap = 16,
}: SkeletonGridProps) {
  return (
    <div 
      className={cn('grid', className)}
      style={{ 
        gridTemplateColumns: `repeat(${columns}, minmax(0, 1fr))`,
        gap,
      }}
    >
      {Array.from({ length: count }).map((_, i) => (
        <SkeletonCard
          key={i}
          className={itemClassName}
          imageHeight={itemHeight}
          lines={2}
          hasFooter={false}
        />
      ))}
    </div>
  );
}

interface SkeletonTextProps {
  className?: string;
  lines?: number;
  width?: number | string;
  height?: number | string;
  lastLineWidth?: number | string;
}

/**
 * Componente de skeleton para texto
 */
export function SkeletonText({
  className,
  lines = 3,
  width = '100%',
  height = 16,
  lastLineWidth = '70%',
}: SkeletonTextProps) {
  return (
    <div className={cn('space-y-2', className)}>
      {Array.from({ length: lines }).map((_, i) => (
        <Skeleton
          key={i}
          className="rounded"
          style={{
            height,
            width: i === lines - 1 && lastLineWidth ? lastLineWidth : width,
          }}
        />
      ))}
    </div>
  );
}

interface SkeletonProfileProps {
  className?: string;
  hasAvatar?: boolean;
  hasHeader?: boolean;
  hasStats?: boolean;
}

/**
 * Componente de skeleton para perfil
 */
export function SkeletonProfile({
  className,
  hasAvatar = true,
  hasHeader = true,
  hasStats = true,
}: SkeletonProfileProps) {
  return (
    <div className={cn('space-y-6', className)}>
      {hasHeader && (
        <div className="flex items-center space-x-4">
          {hasAvatar && <Skeleton className="h-16 w-16 rounded-full" />}
          <div className="space-y-2">
            <Skeleton className="h-6 w-[200px]" />
            <Skeleton className="h-4 w-[150px]" />
          </div>
        </div>
      )}
      
      {hasStats && (
        <div className="grid grid-cols-3 gap-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="border rounded-md p-3 bg-card">
              <Skeleton className="h-8 w-[60px] mb-2" />
              <Skeleton className="h-4 w-[100px]" />
            </div>
          ))}
        </div>
      )}
      
      <div className="space-y-4">
        <Skeleton className="h-5 w-[150px]" />
        <SkeletonText lines={4} height={12} />
      </div>
    </div>
  );
}
