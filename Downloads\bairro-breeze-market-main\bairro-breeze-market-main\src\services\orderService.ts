import { ApiService } from './api';
import { Tables, Insertable, Updatable } from '@/types/database';
import { supabaseClient } from '@/lib/supabase';
import { CartItem } from '@/hooks/useCart';

// Create a typed service for orders
class OrderService extends ApiService<'orders'> {
  constructor() {
    super('orders');
  }

  /**
   * Get orders by user ID (customer)
   * @param userId User ID
   * @returns Promise with orders
   */
  async getByUserId(userId: string): Promise<Tables<'orders'>[]> {
    try {
      const { data, error } = await supabaseClient
        .from('orders')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching orders by user ID:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in getByUserId:', error);
      return [];
    }
  }

  /**
   * Get orders by shop ID (merchant)
   * @param shopId Shop ID
   * @returns Promise with orders
   */
  async getByShopId(shopId: string): Promise<Tables<'orders'>[]> {
    try {
      const { data, error } = await supabaseClient
        .from('orders')
        .select('*')
        .eq('shop_id', shopId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching orders by shop ID:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in getByShopId:', error);
      return [];
    }
  }

  /**
   * Get orders by deliverer ID
   * @param delivererId Deliverer ID
   * @returns Promise with orders
   */
  async getByDelivererId(delivererId: string): Promise<Tables<'orders'>[]> {
    try {
      const { data, error } = await supabaseClient
        .from('orders')
        .select('*')
        .eq('deliverer_id', delivererId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching orders by deliverer ID:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in getByDelivererId:', error);
      return [];
    }
  }

  /**
   * Get orders by status
   * @param status Order status
   * @returns Promise with orders
   */
  async getByStatus(status: Tables<'orders'>['status']): Promise<Tables<'orders'>[]> {
    try {
      const { data, error } = await supabaseClient
        .from('orders')
        .select('*')
        .eq('status', status)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching orders by status:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in getByStatus:', error);
      return [];
    }
  }

  /**
   * Get pending orders (available for deliverers)
   * @returns Promise with pending orders
   */
  async getPendingOrders(): Promise<Tables<'orders'>[]> {
    try {
      const { data, error } = await supabaseClient
        .from('orders')
        .select('*')
        .eq('status', 'pending')
        .is('deliverer_id', null)
        .order('created_at', { ascending: true });

      if (error) {
        console.error('Error fetching pending orders:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in getPendingOrders:', error);
      return [];
    }
  }

  /**
   * Create a new order with items
   * @param orderData Order data
   * @param items Cart items
   * @returns Promise with created order
   */
  async createWithItems(
    orderData: Insertable<'orders'>,
    items: CartItem[]
  ): Promise<Tables<'orders'> | null> {
    try {
      // Start a transaction
      const { data: order, error: orderError } = await supabaseClient
        .from('orders')
        .insert(orderData)
        .select()
        .single();

      if (orderError) {
        console.error('Error creating order:', orderError);
        throw orderError;
      }

      // Insert order items
      const orderItems = items.map(item => ({
        order_id: order.id,
        product_id: item.id,
        quantity: item.quantity,
        price: item.price,
        name: item.name
      }));

      const { error: itemsError } = await supabaseClient
        .from('order_items')
        .insert(orderItems);

      if (itemsError) {
        console.error('Error creating order items:', itemsError);
        throw itemsError;
      }

      return order;
    } catch (error) {
      console.error('Error in createWithItems:', error);
      return null;
    }
  }

  /**
   * Update order status
   * @param orderId Order ID
   * @param status New status
   * @returns Promise with updated order
   */
  async updateStatus(
    orderId: string,
    status: Tables<'orders'>['status']
  ): Promise<Tables<'orders'> | null> {
    try {
      const { data, error } = await supabaseClient
        .from('orders')
        .update({ status })
        .eq('id', orderId)
        .select()
        .single();

      if (error) {
        console.error('Error updating order status:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in updateStatus:', error);
      return null;
    }
  }

  /**
   * Assign deliverer to order
   * @param orderId Order ID
   * @param delivererId Deliverer ID
   * @returns Promise with updated order
   */
  async assignDeliverer(
    orderId: string,
    delivererId: string
  ): Promise<Tables<'orders'> | null> {
    try {
      const { data, error } = await supabaseClient
        .from('orders')
        .update({
          deliverer_id: delivererId,
          status: 'in_progress'
        })
        .eq('id', orderId)
        .select()
        .single();

      if (error) {
        console.error('Error assigning deliverer:', error);
        throw error;
      }

      // Create a delivery record
      const { error: deliveryError } = await supabaseClient
        .from('deliveries')
        .insert({
          order_id: orderId,
          deliverer_id: delivererId,
          status: 'in_progress',
          earnings: data.total * 0.1 // 10% of order total
        });

      if (deliveryError) {
        console.error('Error creating delivery record:', deliveryError);
        throw deliveryError;
      }

      return data;
    } catch (error) {
      console.error('Error in assignDeliverer:', error);
      return null;
    }
  }

  /**
   * Get order items
   * @param orderId Order ID
   * @returns Promise with order items
   */
  async getOrderItems(orderId: string): Promise<Tables<'order_items'>[]> {
    try {
      const { data, error } = await supabaseClient
        .from('order_items')
        .select('*')
        .eq('order_id', orderId);

      if (error) {
        console.error('Error fetching order items:', error);
        throw error;
      }

      return data;
    } catch (error) {
      console.error('Error in getOrderItems:', error);
      return [];
    }
  }
}

export const orderService = new OrderService();
