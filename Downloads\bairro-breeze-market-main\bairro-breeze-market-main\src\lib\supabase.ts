import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/database';

// Dados simulados para desenvolvimento
const mockData: any = {
  products: [],
  shops: [],
  categories: [],
  orders: [],
  profiles: []
};

// Tenta importar dados simulados de um arquivo externo se disponível
import('./mockData.ts')
  .then(module => {
    Object.assign(mockData, module.mockData);
  })
  .catch(() => {
    console.warn('mockData.ts não encontrado, usando dados vazios');
  });

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Determine if we're in development mode without Supabase credentials
export const isDevelopmentMode = !supabaseUrl || !supabaseAnonKey;

// Create a properly typed Supabase client
let supabase: any = null;

try {
  if (isDevelopmentMode) {
    console.warn('Modo de desenvolvimento ativo: Supabase não configurado - usando dados simulados');
  } else {
    supabase = createClient<Database>(supabaseUrl, supabaseAnonKey, {
      auth: {
        autoRefreshToken: true,
        persistSession: true,
      },
      realtime: {
        params: {
          eventsPerSecond: 10,
        },
      },
    });
    console.info('Supabase client initialized successfully');
  }
} catch (error) {
  console.error('Erro ao inicializar Supabase:', error);
}

// Create a mock client for development when Supabase is not configured
const createMockClient = () => {
  if (supabase) return supabase;

  console.info('Using mock Supabase client with simulated data');

  // Return a mock client with the same interface but using localStorage and mockData
  return {
    auth: {
      getSession: () => {
        const storedUser = localStorage.getItem('mockUser');
        if (storedUser) {
          const user = JSON.parse(storedUser);
          return Promise.resolve({
            data: {
              session: {
                user: user
              }
            }
          });
        }
        return Promise.resolve({ data: { session: null } });
      },
      onAuthStateChange: (callback) => {
        // Return a mock subscription
        return { data: { subscription: { unsubscribe: () => {} } } };
      },
      signInWithPassword: ({ email, password }) => {
        // Simulate successful login
        const mockUser = {
          id: 'user1',
          email,
          user_metadata: {
            name: 'João Silva',
            role: 'customer'
          }
        };
        localStorage.setItem('mockUser', JSON.stringify(mockUser));
        return Promise.resolve({ data: { user: mockUser, session: { user: mockUser } }, error: null });
      },
      signUp: ({ email, password, options }) => {
        // Simulate successful registration
        const mockUser = {
          id: 'mock-user-id',
          email,
          user_metadata: options?.data || {}
        };
        localStorage.setItem('mockUser', JSON.stringify(mockUser));
        return Promise.resolve({ data: { user: mockUser }, error: null });
      },
      signOut: () => {
        localStorage.removeItem('mockUser');
        return Promise.resolve({ error: null });
      }
    },
    from: (table) => ({
      select: (columns = '*') => {
        // Função para filtrar dados com base em condições
        let filteredData = [...mockData[table] || []];

        return {
          eq: (column, value) => ({
            single: () => {
              const item = filteredData.find(item => item[column] === value);
              return Promise.resolve({ data: item || null, error: null });
            },
            order: (orderColumn, { ascending = false } = {}) => ({
              limit: (limit) => {
                let result = filteredData
                  .filter(item => item[column] === value)
                  .sort((a, b) => {
                    if (ascending) {
                      return a[orderColumn] > b[orderColumn] ? 1 : -1;
                    } else {
                      return a[orderColumn] < b[orderColumn] ? 1 : -1;
                    }
                  });

                if (limit) {
                  result = result.slice(0, limit);
                }

                return Promise.resolve({ data: result, error: null });
              }
            }),
            eq: (column2, value2) => ({
              single: () => {
                const item = filteredData.find(item =>
                  item[column] === value && item[column2] === value2
                );
                return Promise.resolve({ data: item || null, error: null });
              },
              order: (orderColumn, { ascending = false } = {}) => ({
                limit: (limit) => {
                  let result = filteredData
                    .filter(item => item[column] === value && item[column2] === value2)
                    .sort((a, b) => {
                      if (ascending) {
                        return a[orderColumn] > b[orderColumn] ? 1 : -1;
                      } else {
                        return a[orderColumn] < b[orderColumn] ? 1 : -1;
                      }
                    });

                  if (limit) {
                    result = result.slice(0, limit);
                  }

                  return Promise.resolve({ data: result, error: null });
                }
              })
            })
          }),
          order: (orderColumn, { ascending = false } = {}) => ({
            limit: (limit) => {
              let result = [...filteredData].sort((a, b) => {
                if (ascending) {
                  return a[orderColumn] > b[orderColumn] ? 1 : -1;
                } else {
                  return a[orderColumn] < b[orderColumn] ? 1 : -1;
                }
              });

              if (limit) {
                result = result.slice(0, limit);
              }

              return Promise.resolve({ data: result, error: null });
            }
          })
        };
      },
      insert: (data) => {
        const newItem = {
          ...data,
          id: `mock-${Date.now()}`,
          created_at: new Date().toISOString()
        };

        mockData[table] = [...(mockData[table] || []), newItem];

        return {
          select: () => ({
            single: () => Promise.resolve({ data: newItem, error: null })
          })
        };
      },
      update: (data) => ({
        eq: (column, value) => {
          const index = mockData[table]?.findIndex(item => item[column] === value);

          if (index !== -1 && index !== undefined) {
            mockData[table][index] = {
              ...mockData[table][index],
              ...data,
              updated_at: new Date().toISOString()
            };

            return Promise.resolve({ data: mockData[table][index], error: null });
          }

          return Promise.resolve({ data: null, error: { message: 'Item not found' } });
        }
      }),
      delete: () => ({
        eq: (column, value) => {
          const index = mockData[table]?.findIndex(item => item[column] === value);

          if (index !== -1 && index !== undefined) {
            const deletedItem = mockData[table][index];
            mockData[table] = mockData[table].filter(item => item[column] !== value);

            return Promise.resolve({ data: deletedItem, error: null });
          }

          return Promise.resolve({ data: null, error: { message: 'Item not found' } });
        }
      })
    })
  };
};

// Export the real client or a mock client if not initialized
export const supabaseClient = supabase || createMockClient();
