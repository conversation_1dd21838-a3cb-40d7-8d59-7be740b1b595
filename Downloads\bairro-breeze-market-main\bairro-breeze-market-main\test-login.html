<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Login - Bairro Breeze Market</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #2563eb;
            border-bottom: 2px solid #e5e7eb;
            padding-bottom: 10px;
        }
        h2 {
            color: #4b5563;
            margin-top: 30px;
        }
        .card {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        .button {
            display: inline-block;
            background-color: #2563eb;
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            text-decoration: none;
            margin-right: 10px;
            margin-bottom: 10px;
            font-weight: bold;
        }
        .button.customer {
            background-color: #3b82f6;
        }
        .button.merchant {
            background-color: #10b981;
        }
        .button.deliverer {
            background-color: #f59e0b;
        }
        code {
            background-color: #f3f4f6;
            padding: 2px 4px;
            border-radius: 4px;
            font-family: monospace;
        }
        .note {
            background-color: #fffbeb;
            border-left: 4px solid #f59e0b;
            padding: 10px 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>Teste de Login - Bairro Breeze Market</h1>
    
    <div class="note">
        <p><strong>Nota:</strong> Esta página é apenas para testes. Ela permite acessar diretamente os diferentes perfis de usuário no aplicativo.</p>
    </div>
    
    <h2>Escolha um perfil para testar:</h2>
    
    <div class="card">
        <h3>Cliente</h3>
        <p>Acesse a interface do cliente para navegar por produtos, fazer pedidos e acompanhar entregas.</p>
        <a href="http://localhost:8080/login?role=customer" class="button customer">Entrar como Cliente</a>
    </div>
    
    <div class="card">
        <h3>Lojista</h3>
        <p>Acesse o painel do lojista para gerenciar produtos, pedidos e análises de vendas.</p>
        <a href="http://localhost:8080/login?role=merchant" class="button merchant">Entrar como Lojista</a>
    </div>
    
    <div class="card">
        <h3>Entregador</h3>
        <p>Acesse o painel do entregador para gerenciar entregas, visualizar rotas e acompanhar ganhos.</p>
        <a href="http://localhost:8080/login?role=deliverer" class="button deliverer">Entrar como Entregador</a>
    </div>
    
    <h2>Instruções de Teste</h2>
    
    <ol>
        <li>Clique em um dos botões acima para acessar o perfil desejado.</li>
        <li>Na tela de login, use qualquer email e senha (no modo de desenvolvimento).</li>
        <li>Após o login, você será redirecionado para a interface correspondente ao perfil escolhido.</li>
        <li>Para testar outro perfil, volte a esta página e clique em outro botão.</li>
    </ol>
    
    <div class="note">
        <p><strong>Dica:</strong> Para testar o redirecionamento automático baseado no perfil, acesse <a href="http://localhost:8080/redirect">http://localhost:8080/redirect</a> após fazer login.</p>
    </div>
</body>
</html>
