/**
 * Representa uma categoria de produto
 */
export interface Category {
  id: string;
  name: string;
  icon: string;
  description?: string;
}

/**
 * Representa um produto completo
 */
export interface Product {
  id: string;
  name: string;
  description?: string;
  image: string;
  images?: string[];
  price: number;
  originalPrice?: number;
  discount?: number; // Percentual de desconto
  shopId: string;
  shopName: string;
  category: string;
  categoryId?: string;
  isPromo?: boolean;
  isFeatured?: boolean;
  isAvailable?: boolean;
  stock?: number;
  rating?: number;
  numReviews?: number;
  createdAt?: string;
  updatedAt?: string;
  tags?: string[];
  nutritionalInfo?: {
    calories?: number;
    protein?: number;
    carbs?: number;
    fat?: number;
    ingredients?: string[];
    allergens?: string[];
  };
}

/**
 * Dados necessários para criar um novo produto
 */
export interface CreateProductRequest {
  name: string;
  description?: string;
  image: string;
  price: number;
  originalPrice?: number;
  shopId: string;
  category: string;
  isPromo?: boolean;
  stock?: number;
}

/**
 * Filtros para busca de produtos
 */
export interface ProductFilters {
  query?: string;
  category?: string;
  shopId?: string;
  minPrice?: number;
  maxPrice?: number;
  isPromo?: boolean;
  isFeatured?: boolean;
  sortBy?: 'price_asc' | 'price_desc' | 'rating' | 'newest';
  limit?: number;
  offset?: number;
}
