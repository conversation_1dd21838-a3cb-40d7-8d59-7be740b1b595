import { useState, useEffect } from 'react';

// Definição de breakpoints padrão do Tailwind
export const breakpoints = {
  xs: 0,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
};

export type Breakpoint = keyof typeof breakpoints;

/**
 * Hook para detectar o breakpoint atual baseado na largura da tela
 * @returns O breakpoint atual (xs, sm, md, lg, xl, 2xl)
 */
export function useBreakpoint() {
  // Inicializar com undefined para evitar incompatibilidade SSR
  const [breakpoint, setBreakpoint] = useState<Breakpoint | undefined>(undefined);
  const [width, setWidth] = useState<number | undefined>(undefined);

  useEffect(() => {
    // Função para atualizar o breakpoint baseado na largura da janela
    const handleResize = () => {
      const windowWidth = window.innerWidth;
      setWidth(windowWidth);
      
      if (windowWidth < breakpoints.sm) {
        setBreakpoint('xs');
      } else if (windowWidth < breakpoints.md) {
        setBreakpoint('sm');
      } else if (windowWidth < breakpoints.lg) {
        setBreakpoint('md');
      } else if (windowWidth < breakpoints.xl) {
        setBreakpoint('lg');
      } else if (windowWidth < breakpoints['2xl']) {
        setBreakpoint('xl');
      } else {
        setBreakpoint('2xl');
      }
    };

    // Adicionar event listener para resize
    window.addEventListener('resize', handleResize);
    
    // Definir breakpoint inicial
    handleResize();
    
    // Limpar event listener
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return { breakpoint, width };
}

/**
 * Hook para verificar se a largura da tela é maior ou igual a um breakpoint específico
 * @param breakpoint O breakpoint a ser verificado (sm, md, lg, xl, 2xl)
 * @returns Boolean indicando se a tela é maior ou igual ao breakpoint
 */
export function useMediaQuery(breakpoint: Breakpoint) {
  const [matches, setMatches] = useState(false);

  useEffect(() => {
    // Verificar se estamos no navegador
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia(`(min-width: ${breakpoints[breakpoint]}px)`);
    
    // Definir estado inicial
    setMatches(mediaQuery.matches);

    // Função para atualizar o estado quando o mediaQuery mudar
    const handleChange = (event: MediaQueryListEvent) => {
      setMatches(event.matches);
    };

    // Adicionar listener para mudanças no mediaQuery
    mediaQuery.addEventListener('change', handleChange);
    
    // Limpar listener
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [breakpoint]);

  return matches;
}

/**
 * Hook para verificar se a tela está em modo retrato ou paisagem
 * @returns Boolean indicando se a tela está em modo retrato
 */
export function useIsPortrait() {
  const [isPortrait, setIsPortrait] = useState(false);

  useEffect(() => {
    // Verificar se estamos no navegador
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia('(orientation: portrait)');
    
    // Definir estado inicial
    setIsPortrait(mediaQuery.matches);

    // Função para atualizar o estado quando a orientação mudar
    const handleChange = (event: MediaQueryListEvent) => {
      setIsPortrait(event.matches);
    };

    // Adicionar listener para mudanças na orientação
    mediaQuery.addEventListener('change', handleChange);
    
    // Limpar listener
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return isPortrait;
}
