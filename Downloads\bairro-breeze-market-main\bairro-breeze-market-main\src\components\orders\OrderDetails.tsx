import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { 
  ShoppingBag, 
  MapPin, 
  CreditCard, 
  Clock, 
  CheckCircle, 
  Truck, 
  AlertCircle,
  ChevronDown,
  ChevronUp,
  Star
} from "lucide-react";
import { Order } from "@/types/order";
import { motion, AnimatePresence } from "framer-motion";
import { getPaymentByOrderId } from "@/services/payment";
import { useAuth } from "@/hooks/useAuth";
import { createReview, getUserReviewForItem } from "@/services/reviews";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "@/hooks/use-toast";

interface OrderDetailsProps {
  order: Order;
}

export function OrderDetails({ order }: OrderDetailsProps) {
  const [isItemsExpanded, setIsItemsExpanded] = useState(false);
  const [isReviewDialogOpen, setIsReviewDialogOpen] = useState(false);
  const [rating, setRating] = useState(5);
  const [comment, setComment] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [hasReviewed, setHasReviewed] = useState(false);
  
  const { user } = useAuth();

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  // Get status badge
  const getStatusBadge = (status: Order['status']) => {
    switch (status) {
      case 'pending':
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            <Clock className="mr-1 h-3 w-3" />
            Pendente
          </Badge>
        );
      case 'in_progress':
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            <Truck className="mr-1 h-3 w-3" />
            Em andamento
          </Badge>
        );
      case 'delivered':
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <CheckCircle className="mr-1 h-3 w-3" />
            Entregue
          </Badge>
        );
      case 'cancelled':
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            <AlertCircle className="mr-1 h-3 w-3" />
            Cancelado
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            {status}
          </Badge>
        );
    }
  };

  // Check if user has already reviewed this order
  const checkReviewStatus = async () => {
    if (!user) return;
    
    try {
      const review = await getUserReviewForItem(user.id, order.id, 'shop');
      setHasReviewed(!!review);
    } catch (error) {
      console.error("Error checking review status:", error);
    }
  };

  // Submit review
  const submitReview = async () => {
    if (!user) return;
    
    setIsSubmitting(true);
    
    try {
      const review = await createReview({
        userId: user.id,
        userName: user.user_metadata.name || user.email,
        userAvatar: user.profile?.avatar_url,
        type: 'shop',
        itemId: order.shopName, // Using shop name as ID for now
        rating,
        comment: comment.trim() || undefined
      });
      
      if (review) {
        toast({
          title: "Avaliação enviada",
          description: "Obrigado por compartilhar sua experiência!",
        });
        
        setHasReviewed(true);
        setIsReviewDialogOpen(false);
      }
    } catch (error) {
      console.error("Error submitting review:", error);
      toast({
        title: "Erro ao enviar avaliação",
        description: "Ocorreu um erro ao enviar sua avaliação. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Order header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <div className="flex items-center gap-2 mb-1">
            <h3 className="text-lg font-medium">Pedido #{order.id}</h3>
            {getStatusBadge(order.status)}
          </div>
          <p className="text-sm text-muted-foreground">
            Realizado em {formatDate(order.createdAt)}
          </p>
        </div>
        
        {order.status === 'delivered' && !hasReviewed && (
          <Dialog open={isReviewDialogOpen} onOpenChange={setIsReviewDialogOpen}>
            <DialogTrigger asChild>
              <Button 
                variant="outline" 
                size="sm"
                className="flex items-center gap-1"
                onClick={checkReviewStatus}
              >
                <Star className="h-4 w-4" />
                Avaliar pedido
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Avaliar pedido</DialogTitle>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div>
                  <Label>Sua avaliação</Label>
                  <div className="flex items-center gap-1 mt-2">
                    {[1, 2, 3, 4, 5].map((value) => (
                      <button
                        key={value}
                        type="button"
                        onClick={() => setRating(value)}
                        className="focus:outline-none"
                      >
                        <Star
                          className={`h-8 w-8 ${
                            value <= rating ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'
                          }`}
                        />
                      </button>
                    ))}
                  </div>
                </div>
                <div>
                  <Label htmlFor="comment">Comentário (opcional)</Label>
                  <Textarea
                    id="comment"
                    value={comment}
                    onChange={(e) => setComment(e.target.value)}
                    placeholder="Conte-nos sobre sua experiência..."
                    className="mt-2"
                  />
                </div>
                <div className="flex justify-end gap-2 mt-4">
                  <Button
                    variant="outline"
                    onClick={() => setIsReviewDialogOpen(false)}
                  >
                    Cancelar
                  </Button>
                  <Button
                    onClick={submitReview}
                    disabled={isSubmitting}
                    className="bg-cta hover:bg-cta-dark"
                  >
                    {isSubmitting ? (
                      <>
                        <motion.div
                          className="mr-2 h-4 w-4 border-2 border-white border-t-transparent rounded-full"
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        />
                        Enviando...
                      </>
                    ) : (
                      "Enviar avaliação"
                    )}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>
      
      {/* Order details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Shop info */}
        <div className="bg-white p-4 rounded-lg border">
          <div className="flex items-center mb-3">
            <ShoppingBag className="h-5 w-5 text-muted-foreground mr-2" />
            <h4 className="font-medium">Loja</h4>
          </div>
          <p className="font-medium">{order.shopName}</p>
        </div>
        
        {/* Delivery address */}
        <div className="bg-white p-4 rounded-lg border">
          <div className="flex items-center mb-3">
            <MapPin className="h-5 w-5 text-muted-foreground mr-2" />
            <h4 className="font-medium">Endereço de entrega</h4>
          </div>
          <p className="text-sm">{order.deliveryAddress}</p>
        </div>
        
        {/* Payment info */}
        <div className="bg-white p-4 rounded-lg border">
          <div className="flex items-center mb-3">
            <CreditCard className="h-5 w-5 text-muted-foreground mr-2" />
            <h4 className="font-medium">Pagamento</h4>
          </div>
          <p className="text-sm">
            {order.paymentMethod === 'credit-card' ? 'Cartão de crédito' :
             order.paymentMethod === 'debit-card' ? 'Cartão de débito' :
             order.paymentMethod === 'pix' ? 'PIX' :
             order.paymentMethod === 'cash' ? 'Dinheiro' :
             order.paymentMethod}
          </p>
          <p className="font-medium mt-1">{formatCurrency(order.total)}</p>
        </div>
        
        {/* Order status */}
        <div className="bg-white p-4 rounded-lg border">
          <div className="flex items-center mb-3">
            <Clock className="h-5 w-5 text-muted-foreground mr-2" />
            <h4 className="font-medium">Status do pedido</h4>
          </div>
          <div className="flex items-center">
            {getStatusBadge(order.status)}
            <span className="ml-2 text-sm">
              {order.status === 'pending' ? 'Aguardando confirmação' :
               order.status === 'in_progress' ? 'Em preparação/entrega' :
               order.status === 'delivered' ? 'Entregue com sucesso' :
               order.status === 'cancelled' ? 'Pedido cancelado' :
               order.status}
            </span>
          </div>
        </div>
      </div>
      
      {/* Order items */}
      <div className="bg-white p-4 rounded-lg border">
        <button
          className="flex items-center justify-between w-full"
          onClick={() => setIsItemsExpanded(!isItemsExpanded)}
        >
          <div className="flex items-center">
            <ShoppingBag className="h-5 w-5 text-muted-foreground mr-2" />
            <h4 className="font-medium">Itens do pedido</h4>
          </div>
          {isItemsExpanded ? (
            <ChevronUp className="h-5 w-5 text-muted-foreground" />
          ) : (
            <ChevronDown className="h-5 w-5 text-muted-foreground" />
          )}
        </button>
        
        <AnimatePresence>
          {isItemsExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="overflow-hidden"
            >
              <Separator className="my-4" />
              
              <div className="space-y-4">
                {order.items.map((item) => (
                  <div key={item.productId} className="flex items-center">
                    <div className="flex-shrink-0 w-12 h-12 bg-gray-100 rounded-md mr-3" />
                    <div className="flex-1">
                      <p className="font-medium">{item.name}</p>
                      <p className="text-sm text-muted-foreground">
                        Quantidade: {item.quantity}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{formatCurrency(item.price)}</p>
                      <p className="text-sm text-muted-foreground">
                        {formatCurrency(item.price * item.quantity)}
                      </p>
                    </div>
                  </div>
                ))}
                
                <Separator className="my-4" />
                
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Subtotal</span>
                    <span>{formatCurrency(order.total - 5.0)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Taxa de entrega</span>
                    <span>{formatCurrency(5.0)}</span>
                  </div>
                  <div className="flex justify-between font-bold">
                    <span>Total</span>
                    <span>{formatCurrency(order.total)}</span>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}
