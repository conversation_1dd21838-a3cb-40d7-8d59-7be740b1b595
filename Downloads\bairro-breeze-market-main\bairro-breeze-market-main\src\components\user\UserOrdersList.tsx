import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  Package, 
  Clock, 
  ChevronRight, 
  Loader2, 
  ShoppingBag,
  CheckCircle,
  Truck,
  AlertCircle
} from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { useAuth } from "@/hooks/useAuth";
import { Order } from "@/types/order";
import { fetchOrdersByUserId } from "@/services/orders";
import { Badge } from "@/components/ui/badge";

export function UserOrdersList() {
  const { user } = useAuth();
  const navigate = useNavigate();
  
  const [isLoading, setIsLoading] = useState(true);
  const [orders, setOrders] = useState<Order[]>([]);
  
  // Load user orders
  useEffect(() => {
    const loadOrders = async () => {
      if (!user) return;
      
      setIsLoading(true);
      
      try {
        const userOrders = await fetchOrdersByUserId(user.id);
        setOrders(userOrders);
      } catch (error) {
        console.error("Error loading orders:", error);
        toast({
          title: "Erro ao carregar pedidos",
          description: "Não foi possível carregar seus pedidos. Tente novamente mais tarde.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    loadOrders();
  }, [user]);
  
  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };
  
  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };
  
  // Get status badge
  const getStatusBadge = (status: Order["status"]) => {
    switch (status) {
      case "pending":
        return (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
            Pendente
          </Badge>
        );
      case "in_progress":
        return (
          <Badge variant="outline" className="bg-blue-100 text-blue-800 hover:bg-blue-100">
            Em preparo
          </Badge>
        );
      case "delivered":
        return (
          <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-100">
            Entregue
          </Badge>
        );
      case "cancelled":
        return (
          <Badge variant="outline" className="bg-red-100 text-red-800 hover:bg-red-100">
            Cancelado
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-gray-100 text-gray-800 hover:bg-gray-100">
            Desconhecido
          </Badge>
        );
    }
  };
  
  // Get status icon
  const getStatusIcon = (status: Order["status"]) => {
    switch (status) {
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case "in_progress":
        return <Truck className="h-4 w-4 text-blue-500" />;
      case "delivered":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "cancelled":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Package className="h-4 w-4 text-gray-500" />;
    }
  };
  
  // Handle view order details
  const handleViewOrderDetails = (orderId: string) => {
    navigate(`/orders/${orderId}`);
  };
  
  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Meus Pedidos</CardTitle>
        <CardDescription>
          Visualize e acompanhe seus pedidos
        </CardDescription>
      </CardHeader>
      <CardContent>
        {orders.length === 0 ? (
          <div className="text-center py-8">
            <ShoppingBag className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-1">Nenhum pedido encontrado</h3>
            <p className="text-muted-foreground mb-4">
              Você ainda não realizou nenhum pedido
            </p>
            <Button 
              className="bg-cta hover:bg-cta-dark"
              onClick={() => navigate("/")}
            >
              Explorar Produtos
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {orders.map((order) => (
              <div 
                key={order.id} 
                className="border rounded-lg p-4 hover:border-cta transition-colors cursor-pointer"
                onClick={() => handleViewOrderDetails(order.id)}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <div className="flex items-center">
                      <p className="font-medium">Pedido #{order.id.substring(0, 8)}</p>
                      <div className="ml-2">
                        {getStatusBadge(order.status)}
                      </div>
                    </div>
                    <p className="text-sm text-muted-foreground flex items-center mt-1">
                      <Clock className="mr-1 h-3 w-3" />
                      {formatDate(order.createdAt)}
                    </p>
                  </div>
                  <ChevronRight className="h-5 w-5 text-muted-foreground" />
                </div>
                
                <div className="mt-3">
                  <p className="text-sm font-medium">{order.shopName}</p>
                  <p className="text-sm text-muted-foreground">
                    {order.items.length} {order.items.length === 1 ? "item" : "itens"} • {formatCurrency(order.total)}
                  </p>
                </div>
                
                <div className="mt-3 flex items-center text-sm">
                  {getStatusIcon(order.status)}
                  <span className="ml-1">
                    {order.status === "pending" && "Aguardando confirmação"}
                    {order.status === "in_progress" && "Em preparo"}
                    {order.status === "delivered" && "Entregue"}
                    {order.status === "cancelled" && "Cancelado"}
                  </span>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
      {orders.length > 0 && (
        <CardFooter>
          <Button 
            variant="outline" 
            className="w-full"
            onClick={() => navigate("/orders")}
          >
            Ver Todos os Pedidos
          </Button>
        </CardFooter>
      )}
    </Card>
  );
}
