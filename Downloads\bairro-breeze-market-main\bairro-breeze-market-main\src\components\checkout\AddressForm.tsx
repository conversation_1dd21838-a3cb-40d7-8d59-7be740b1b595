import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { MapPin, Loader2, Check } from "lucide-react";
import { getLocationByAddress, getAddressByLocation } from "@/services/map";
import { getAddresses, createAddress, setDefaultAddress } from "@/services/addresses";
import { useAuth } from "@/hooks/useAuth";
import { Address } from "@/services/addresses";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { motion } from "framer-motion";
import { toast } from "@/hooks/use-toast";

interface AddressFormProps {
  onAddressSelect: (address: string) => void;
  onComplete: () => void;
}

export function AddressForm({ onAddressSelect, onComplete }: AddressFormProps) {
  const [street, setStreet] = useState("");
  const [number, setNumber] = useState("");
  const [complement, setComplement] = useState("");
  const [neighborhood, setNeighborhood] = useState("");
  const [city, setCity] = useState("");
  const [state, setState] = useState("");
  const [zipCode, setZipCode] = useState("");
  const [instructions, setInstructions] = useState("");
  const [isValidating, setIsValidating] = useState(false);
  const [isAddressValid, setIsAddressValid] = useState(false);
  const [savedAddresses, setSavedAddresses] = useState<Address[]>([]);
  const [selectedAddressId, setSelectedAddressId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingAddresses, setIsLoadingAddresses] = useState(false);
  const [isAddingNewAddress, setIsAddingNewAddress] = useState(false);
  
  const { user } = useAuth();

  // Load saved addresses
  useEffect(() => {
    const loadAddresses = async () => {
      if (!user) return;
      
      setIsLoadingAddresses(true);
      try {
        const addresses = await getAddresses(user.id);
        setSavedAddresses(addresses);
        
        // Select default address if available
        const defaultAddress = addresses.find(addr => addr.isDefault);
        if (defaultAddress) {
          setSelectedAddressId(defaultAddress.id);
          const fullAddress = formatFullAddress(defaultAddress);
          onAddressSelect(fullAddress);
        } else if (addresses.length > 0) {
          setSelectedAddressId(addresses[0].id);
          const fullAddress = formatFullAddress(addresses[0]);
          onAddressSelect(fullAddress);
        } else {
          // No saved addresses, show the form to add a new one
          setIsAddingNewAddress(true);
        }
      } catch (error) {
        console.error("Error loading addresses:", error);
        toast({
          title: "Erro ao carregar endereços",
          description: "Não foi possível carregar seus endereços salvos.",
          variant: "destructive"
        });
      } finally {
        setIsLoadingAddresses(false);
      }
    };
    
    loadAddresses();
  }, [user, onAddressSelect]);

  // Format full address from address object
  const formatFullAddress = (address: Address): string => {
    return `${address.street}, ${address.number}${address.complement ? `, ${address.complement}` : ''} - ${address.neighborhood}, ${address.city}, ${address.state} - ${address.zipCode}`;
  };

  // Validate address with map service
  const validateAddress = async () => {
    if (!street || !number || !neighborhood || !city || !state || !zipCode) {
      toast({
        title: "Endereço incompleto",
        description: "Por favor, preencha todos os campos obrigatórios.",
        variant: "destructive"
      });
      return;
    }
    
    setIsValidating(true);
    
    try {
      const fullAddress = `${street}, ${number} - ${neighborhood}, ${city}, ${state} - ${zipCode}`;
      const location = await getLocationByAddress(fullAddress);
      
      if (location) {
        setIsAddressValid(true);
        onAddressSelect(fullAddress + (instructions ? ` (${instructions})` : ''));
        
        // Save address if user is logged in
        if (user) {
          await saveAddress();
        }
        
        // Move to next step
        onComplete();
      } else {
        setIsAddressValid(false);
        toast({
          title: "Endereço não encontrado",
          description: "Não foi possível validar o endereço. Verifique os dados e tente novamente.",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error("Error validating address:", error);
      toast({
        title: "Erro ao validar endereço",
        description: "Ocorreu um erro ao validar o endereço. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsValidating(false);
    }
  };

  // Save address to user profile
  const saveAddress = async () => {
    if (!user) return;
    
    setIsLoading(true);
    
    try {
      const newAddress = await createAddress({
        userId: user.id,
        name: "Casa", // Default name, could be customizable
        street,
        number,
        complement,
        neighborhood,
        city,
        state,
        zipCode,
        isDefault: savedAddresses.length === 0 // Make default if first address
      });
      
      if (newAddress) {
        toast({
          title: "Endereço salvo",
          description: "Seu endereço foi salvo com sucesso.",
        });
        
        // Update saved addresses
        setSavedAddresses(prev => [...prev, newAddress]);
        setSelectedAddressId(newAddress.id);
      }
    } catch (error) {
      console.error("Error saving address:", error);
      toast({
        title: "Erro ao salvar endereço",
        description: "Ocorreu um erro ao salvar o endereço. Tente novamente.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle address selection
  const handleAddressSelect = (addressId: string) => {
    setSelectedAddressId(addressId);
    
    const selectedAddress = savedAddresses.find(addr => addr.id === addressId);
    if (selectedAddress) {
      const fullAddress = formatFullAddress(selectedAddress);
      onAddressSelect(fullAddress);
      
      // Set as default if not already
      if (!selectedAddress.isDefault) {
        setDefaultAddress(addressId).catch(error => {
          console.error("Error setting default address:", error);
        });
      }
    }
  };

  // Continue with selected address
  const handleContinue = () => {
    if (selectedAddressId) {
      onComplete();
    } else {
      toast({
        title: "Selecione um endereço",
        description: "Por favor, selecione um endereço para continuar.",
        variant: "destructive"
      });
    }
  };

  if (isLoadingAddresses) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-8 w-8 animate-spin text-cta" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {savedAddresses.length > 0 && !isAddingNewAddress && (
        <>
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Endereços salvos</h3>
            
            <RadioGroup value={selectedAddressId || ""} onValueChange={handleAddressSelect}>
              <div className="space-y-3">
                {savedAddresses.map((address) => (
                  <motion.div
                    key={address.id}
                    className="flex items-start space-x-3 border rounded-md p-3"
                    whileHover={{ backgroundColor: "#f9fafb" }}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                  >
                    <RadioGroupItem value={address.id} id={`address-${address.id}`} className="mt-1" />
                    <div className="flex-1">
                      <div className="flex justify-between">
                        <Label htmlFor={`address-${address.id}`} className="font-medium cursor-pointer">
                          {address.name}
                          {address.isDefault && (
                            <span className="ml-2 text-xs bg-eco-light text-eco px-2 py-0.5 rounded">
                              Padrão
                            </span>
                          )}
                        </Label>
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">
                        {formatFullAddress(address)}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </RadioGroup>
            
            <div className="flex justify-between mt-4">
              <Button 
                variant="outline" 
                type="button"
                onClick={() => setIsAddingNewAddress(true)}
              >
                <MapPin className="mr-2 h-4 w-4" />
                Novo endereço
              </Button>
              
              <Button 
                type="button"
                className="bg-cta hover:bg-cta-dark"
                onClick={handleContinue}
              >
                Continuar
              </Button>
            </div>
          </div>
        </>
      )}
      
      {(isAddingNewAddress || savedAddresses.length === 0) && (
        <>
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Novo endereço</h3>
            
            <div className="grid grid-cols-2 gap-3">
              <div className="col-span-2 sm:col-span-1">
                <Label htmlFor="street">Rua/Avenida*</Label>
                <Input
                  id="street"
                  value={street}
                  onChange={(e) => setStreet(e.target.value)}
                  required
                />
              </div>
              <div className="col-span-2 sm:col-span-1">
                <Label htmlFor="number">Número*</Label>
                <Input
                  id="number"
                  value={number}
                  onChange={(e) => setNumber(e.target.value)}
                  required
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="complement">Complemento</Label>
              <Input
                id="complement"
                value={complement}
                onChange={(e) => setComplement(e.target.value)}
                placeholder="Apt, Bloco, etc."
              />
            </div>
            
            <div className="grid grid-cols-2 gap-3">
              <div className="col-span-2 sm:col-span-1">
                <Label htmlFor="neighborhood">Bairro*</Label>
                <Input
                  id="neighborhood"
                  value={neighborhood}
                  onChange={(e) => setNeighborhood(e.target.value)}
                  required
                />
              </div>
              <div className="col-span-2 sm:col-span-1">
                <Label htmlFor="zipCode">CEP*</Label>
                <Input
                  id="zipCode"
                  value={zipCode}
                  onChange={(e) => setZipCode(e.target.value)}
                  required
                />
              </div>
            </div>
            
            <div className="grid grid-cols-3 gap-3">
              <div className="col-span-2">
                <Label htmlFor="city">Cidade*</Label>
                <Input
                  id="city"
                  value={city}
                  onChange={(e) => setCity(e.target.value)}
                  required
                />
              </div>
              <div>
                <Label htmlFor="state">Estado*</Label>
                <Input
                  id="state"
                  value={state}
                  onChange={(e) => setState(e.target.value)}
                  maxLength={2}
                  required
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="instructions">Instruções para entrega</Label>
              <Textarea
                id="instructions"
                value={instructions}
                onChange={(e) => setInstructions(e.target.value)}
                placeholder="Ex: Interfone 123, deixar na portaria, etc."
                className="resize-none"
              />
            </div>
            
            <div className="flex justify-between mt-4">
              {savedAddresses.length > 0 && (
                <Button 
                  variant="outline" 
                  type="button"
                  onClick={() => setIsAddingNewAddress(false)}
                >
                  Voltar
                </Button>
              )}
              
              <Button 
                type="button"
                className="bg-cta hover:bg-cta-dark ml-auto"
                onClick={validateAddress}
                disabled={isValidating || isLoading}
              >
                {isValidating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Validando...
                  </>
                ) : isAddressValid ? (
                  <>
                    <Check className="mr-2 h-4 w-4" />
                    Endereço validado
                  </>
                ) : (
                  "Validar e continuar"
                )}
              </Button>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
