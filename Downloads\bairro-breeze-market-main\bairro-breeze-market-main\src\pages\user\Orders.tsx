import { useQuery } from "@tanstack/react-query";
import { fetchOrders } from "@/services/orders";
import { OrderTracker } from "@/components/orders/OrderTracker";
import { OrderDetails } from "@/components/orders/OrderDetails";
import { Loader2, Package, ShoppingBag, Filter, CheckCircle, AlertCircle, X } from "lucide-react";
import { Navbar } from "@/components/ui/navbar";
import { BottomNav } from "@/components/ui/bottom-nav";
import { motion } from "framer-motion";
import { AnimatedPage, AnimatedList, FadeIn } from "@/components/animations";
import { Button } from "@/components/ui/button";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/hooks/useAuth";
import { Order } from "@/types/order";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";

export default function Orders() {
  const [filter, setFilter] = useState<string | null>(null);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const navigate = useNavigate();
  const { user, loading } = useAuth();

  const { data: orders, isLoading } = useQuery({
    queryKey: ["orders"],
    queryFn: fetchOrders,
  });

  // Redirect to login if not authenticated
  if (!loading && !user) {
    navigate('/login');
    return null;
  }

  // Filter orders based on status
  const filteredOrders = filter
    ? orders?.filter(order => order.status === filter)
    : orders;

  return (
    <AnimatedPage className="flex flex-col min-h-screen bg-gray-50">
      <Navbar />

      <main className="flex-1 container px-4 py-6 pb-20 md:pb-6">
        <FadeIn delay={0.1}>
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold">Meus Pedidos</h1>
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="outline" size="sm" className="flex items-center gap-1">
                  <Filter className="h-4 w-4" />
                  Filtrar
                </Button>
              </SheetTrigger>
              <SheetContent>
                <SheetHeader>
                  <SheetTitle>Filtrar pedidos</SheetTitle>
                </SheetHeader>
                <div className="py-6 space-y-6">
                  <div className="space-y-3">
                    <Button
                      variant={filter === null ? "default" : "outline"}
                      className="w-full justify-start"
                      onClick={() => {
                        setFilter(null);
                      }}
                    >
                      <ShoppingBag className="mr-2 h-4 w-4" />
                      Todos os pedidos
                    </Button>
                    <Button
                      variant={filter === "pending" ? "default" : "outline"}
                      className="w-full justify-start"
                      onClick={() => {
                        setFilter("pending");
                      }}
                    >
                      <Loader2 className="mr-2 h-4 w-4" />
                      Pedidos pendentes
                    </Button>
                    <Button
                      variant={filter === "in_progress" ? "default" : "outline"}
                      className="w-full justify-start"
                      onClick={() => {
                        setFilter("in_progress");
                      }}
                    >
                      <Package className="mr-2 h-4 w-4" />
                      Pedidos em andamento
                    </Button>
                    <Button
                      variant={filter === "delivered" ? "default" : "outline"}
                      className="w-full justify-start"
                      onClick={() => {
                        setFilter("delivered");
                      }}
                    >
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Pedidos entregues
                    </Button>
                    <Button
                      variant={filter === "cancelled" ? "default" : "outline"}
                      className="w-full justify-start"
                      onClick={() => {
                        setFilter("cancelled");
                      }}
                    >
                      <AlertCircle className="mr-2 h-4 w-4" />
                      Pedidos cancelados
                    </Button>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
          </div>

          {/* Status filter tabs */}
          <div className="flex gap-2 overflow-x-auto pb-4 -mx-4 px-4 mb-4">
            <motion.button
              className={`px-3 py-1.5 rounded-full text-sm whitespace-nowrap flex items-center gap-1 ${filter === null ? 'bg-cta text-white' : 'bg-white border text-muted-foreground'}`}
              onClick={() => setFilter(null)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <ShoppingBag className="h-3.5 w-3.5" />
              Todos
            </motion.button>
            <motion.button
              className={`px-3 py-1.5 rounded-full text-sm whitespace-nowrap flex items-center gap-1 ${filter === 'pending' ? 'bg-yellow-500 text-white' : 'bg-white border text-muted-foreground'}`}
              onClick={() => setFilter('pending')}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Loader2 className="h-3.5 w-3.5" />
              Pendentes
            </motion.button>
            <motion.button
              className={`px-3 py-1.5 rounded-full text-sm whitespace-nowrap flex items-center gap-1 ${filter === 'in_progress' ? 'bg-blue-500 text-white' : 'bg-white border text-muted-foreground'}`}
              onClick={() => setFilter('in_progress')}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Package className="h-3.5 w-3.5" />
              Em andamento
            </motion.button>
            <motion.button
              className={`px-3 py-1.5 rounded-full text-sm whitespace-nowrap flex items-center gap-1 ${filter === 'delivered' ? 'bg-green-500 text-white' : 'bg-white border text-muted-foreground'}`}
              onClick={() => setFilter('delivered')}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <CheckCircle className="h-3.5 w-3.5" />
              Entregues
            </motion.button>
          </div>
        </FadeIn>

        {isLoading ? (
          <div className="flex justify-center items-center py-12">
            <motion.div
              className="h-12 w-12 rounded-full border-4 border-cta border-t-transparent"
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            />
          </div>
        ) : filteredOrders?.length === 0 ? (
          <FadeIn delay={0.2}>
            <div className="text-center py-12 bg-white rounded-lg border p-8">
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 0.5 }}
                className="mb-4"
              >
                <ShoppingBag className="h-16 w-16 text-muted-foreground mx-auto" />
              </motion.div>
              <h3 className="text-lg font-medium mb-2">
                {filter ? "Nenhum pedido com este status" : "Você ainda não tem pedidos"}
              </h3>
              <p className="text-muted-foreground mb-6">
                {filter ? "Tente outro filtro ou verifique mais tarde." : "Que tal explorar nossos produtos e fazer seu primeiro pedido?"}
              </p>
              {!filter && (
                <Button
                  className="bg-cta hover:bg-cta-dark text-white"
                  onClick={() => navigate('/')}
                >
                  Explorar produtos
                </Button>
              )}
            </div>
          </FadeIn>
        ) : (
          <AnimatedList className="grid gap-4 md:grid-cols-2 lg:grid-cols-2" delay={0.2} staggerDelay={0.1}>
            {filteredOrders?.map((order) => (
              <motion.div
                key={order.id}
                whileHover={{ y: -5, boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1)" }}
                className="cursor-pointer"
                onClick={() => {
                  setSelectedOrder(order);
                  setIsDetailsOpen(true);
                }}
              >
                <OrderTracker order={order} />
              </motion.div>
            ))}
          </AnimatedList>
        )}
      </main>

      <BottomNav />

      {/* Order details sheet */}
      <Sheet open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
        <SheetContent className="w-full sm:max-w-md md:max-w-lg" side="right">
          <SheetHeader className="flex flex-row items-center justify-between">
            <SheetTitle>Detalhes do Pedido</SheetTitle>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsDetailsOpen(false)}
            >
              <X className="h-4 w-4" />
            </Button>
          </SheetHeader>
          <div className="mt-6">
            {selectedOrder && <OrderDetails order={selectedOrder} />}
          </div>
        </SheetContent>
      </Sheet>
    </AnimatedPage>
  );
}
