import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from "@/components/ui/dialog";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu";
import { 
  Search, 
  MoreVertical, 
  Eye, 
  CheckCircle, 
  XCircle,
  Clock,
  Truck,
  MapPin,
  ArrowUpDown,
  Navigation
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Order } from "@/types/order";
import { toast } from "@/hooks/use-toast";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { fetchOrders } from "@/services/orders";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

export function DeliveryManagement() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string | null>("pending");
  const [sortBy, setSortBy] = useState<"date" | "distance">("date");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [isRouteDialogOpen, setIsRouteDialogOpen] = useState(false);
  
  const queryClient = useQueryClient();
  
  // Fetch orders
  const { data: orders = [], isLoading } = useQuery({
    queryKey: ["orders"],
    queryFn: fetchOrders,
  });

  // Filter orders by search term and status
  const filteredOrders = orders.filter(order => {
    const matchesSearch = 
      order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.shopName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.deliveryAddress.toLowerCase().includes(searchTerm.toLowerCase());
    
    // For deliverers, we're only interested in pending or in_progress orders
    const matchesStatus = statusFilter 
      ? order.status === statusFilter 
      : (order.status === "pending" || order.status === "in_progress");
    
    return matchesSearch && matchesStatus;
  });

  // Sort orders
  const sortedOrders = [...filteredOrders].sort((a, b) => {
    if (sortBy === "date") {
      const dateA = new Date(a.createdAt).getTime();
      const dateB = new Date(b.createdAt).getTime();
      return sortOrder === "asc" ? dateA - dateB : dateB - dateA;
    }
    // In a real app, we would sort by actual distance
    return 0;
  });

  const handleUpdateStatus = (orderId: string, newStatus: Order["status"]) => {
    // In a real app, this would call an API
    const updatedOrders = orders.map(order => 
      order.id === orderId 
        ? { ...order, status: newStatus } 
        : order
    );
    
    // Update local cache
    queryClient.setQueryData(["orders"], updatedOrders);
    
    toast({
      title: "Status atualizado",
      description: `Pedido #${orderId} foi atualizado para ${getStatusLabel(newStatus)}.`,
    });
    
    // If the order details dialog is open and this is the selected order, update it
    if (selectedOrder && selectedOrder.id === orderId) {
      setSelectedOrder({ ...selectedOrder, status: newStatus });
    }
  };

  const toggleSort = (column: "date" | "distance") => {
    if (sortBy === column) {
      setSortOrder(sortOrder === "asc" ? "desc" : "asc");
    } else {
      setSortBy(column);
      setSortOrder("desc");
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return format(date, "dd/MM/yyyy 'às' HH:mm", { locale: ptBR });
  };

  const getStatusLabel = (status: Order["status"]) => {
    switch (status) {
      case "pending":
        return "Pendente";
      case "in_progress":
        return "Em Preparo";
      case "delivered":
        return "Entregue";
      case "cancelled":
        return "Cancelado";
      default:
        return status;
    }
  };

  const getStatusBadge = (status: Order["status"]) => {
    switch (status) {
      case "pending":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            <Clock className="w-3 h-3 mr-1" />
            Pendente
          </Badge>
        );
      case "in_progress":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            <Truck className="w-3 h-3 mr-1" />
            Em Rota
          </Badge>
        );
      case "delivered":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <CheckCircle className="w-3 h-3 mr-1" />
            Entregue
          </Badge>
        );
      case "cancelled":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            <XCircle className="w-3 h-3 mr-1" />
            Cancelado
          </Badge>
        );
      default:
        return <Badge>{status}</Badge>;
    }
  };

  const acceptDelivery = (order: Order) => {
    handleUpdateStatus(order.id, "in_progress");
    toast({
      title: "Entrega aceita",
      description: `Você aceitou a entrega do pedido #${order.id}.`,
    });
  };

  const completeDelivery = (order: Order) => {
    handleUpdateStatus(order.id, "delivered");
    toast({
      title: "Entrega concluída",
      description: `Você concluiu a entrega do pedido #${order.id}.`,
    });
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="relative w-full sm:w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Buscar entregas..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="flex gap-2 w-full sm:w-auto">
          <Select
            value={statusFilter || ""}
            onValueChange={(value) => setStatusFilter(value || null)}
          >
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="Filtrar por status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Todos</SelectItem>
              <SelectItem value="pending">Pendentes</SelectItem>
              <SelectItem value="in_progress">Em Rota</SelectItem>
              <SelectItem value="delivered">Entregues</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {isLoading ? (
        <div className="flex justify-center items-center py-12">
          <div className="h-6 w-6 animate-spin rounded-full border-2 border-trust border-t-transparent" />
        </div>
      ) : (
        <div className="space-y-4">
          {sortedOrders.length === 0 ? (
            <div className="text-center py-12 bg-gray-50 rounded-lg border">
              <Truck className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-1">Nenhuma entrega encontrada</h3>
              <p className="text-muted-foreground">
                Não há entregas disponíveis com os filtros selecionados.
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {sortedOrders.map((order) => (
                <Card key={order.id} className="overflow-hidden">
                  <CardContent className="p-0">
                    <div className="p-4 border-b">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h3 className="font-medium">Pedido #{order.id}</h3>
                          <p className="text-sm text-muted-foreground">{formatDate(order.createdAt)}</p>
                        </div>
                        {getStatusBadge(order.status)}
                      </div>
                      <div className="flex items-center text-sm mb-2">
                        <MapPin className="h-4 w-4 mr-1 text-trust" />
                        <span className="font-medium">{order.shopName}</span>
                      </div>
                      <div className="flex items-start">
                        <MapPin className="h-4 w-4 mr-1 text-cta flex-shrink-0 mt-0.5" />
                        <span className="text-sm">{order.deliveryAddress}</span>
                      </div>
                    </div>
                    <div className="p-4 bg-gray-50 flex justify-between items-center">
                      <div>
                        <p className="text-sm text-muted-foreground">Total</p>
                        <p className="font-bold">{formatCurrency(order.total)}</p>
                      </div>
                      <div className="flex gap-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => {
                            setSelectedOrder(order);
                            setIsDetailsOpen(true);
                          }}
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          Detalhes
                        </Button>
                        {order.status === "pending" && (
                          <Button 
                            size="sm"
                            className="bg-trust hover:bg-trust-dark"
                            onClick={() => acceptDelivery(order)}
                          >
                            <Truck className="h-4 w-4 mr-1" />
                            Aceitar
                          </Button>
                        )}
                        {order.status === "in_progress" && (
                          <Button 
                            size="sm"
                            className="bg-cta hover:bg-cta-dark"
                            onClick={() => completeDelivery(order)}
                          >
                            <CheckCircle className="h-4 w-4 mr-1" />
                            Concluir
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Order Details Dialog */}
      <Dialog open={isDetailsOpen} onOpenChange={setIsDetailsOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Detalhes da Entrega #{selectedOrder?.id}</DialogTitle>
            <DialogDescription>
              Informações completas sobre a entrega.
            </DialogDescription>
          </DialogHeader>
          {selectedOrder && (
            <div className="space-y-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm text-muted-foreground">Data do Pedido</p>
                  <p className="font-medium">{formatDate(selectedOrder.createdAt)}</p>
                </div>
                <div>{getStatusBadge(selectedOrder.status)}</div>
              </div>
              
              <div className="space-y-2">
                <div>
                  <p className="text-sm text-muted-foreground mb-1">Estabelecimento</p>
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 mr-2 text-trust" />
                    <p className="font-medium">{selectedOrder.shopName}</p>
                  </div>
                </div>
                
                <div>
                  <p className="text-sm text-muted-foreground mb-1">Endereço de Entrega</p>
                  <div className="flex items-start">
                    <MapPin className="h-4 w-4 mr-2 text-cta flex-shrink-0 mt-0.5" />
                    <p className="font-medium">{selectedOrder.deliveryAddress}</p>
                  </div>
                </div>
              </div>
              
              <div>
                <p className="text-sm text-muted-foreground mb-2">Itens do Pedido</p>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Produto</TableHead>
                        <TableHead className="text-right">Qtd</TableHead>
                        <TableHead className="text-right">Preço</TableHead>
                        <TableHead className="text-right">Subtotal</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {selectedOrder.items.map((item, index) => (
                        <TableRow key={index}>
                          <TableCell>{item.name}</TableCell>
                          <TableCell className="text-right">{item.quantity}</TableCell>
                          <TableCell className="text-right">{formatCurrency(item.price)}</TableCell>
                          <TableCell className="text-right">{formatCurrency(item.price * item.quantity)}</TableCell>
                        </TableRow>
                      ))}
                      <TableRow>
                        <TableCell colSpan={3} className="text-right font-medium">Total</TableCell>
                        <TableCell className="text-right font-bold">{formatCurrency(selectedOrder.total)}</TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </div>
              </div>
              
              <div className="flex justify-between gap-2">
                <Button 
                  variant="outline" 
                  onClick={() => {
                    setIsRouteDialogOpen(true);
                    setIsDetailsOpen(false);
                  }}
                >
                  <Navigation className="mr-2 h-4 w-4" />
                  Ver Rota
                </Button>
                
                <div className="flex gap-2">
                  {selectedOrder.status === "pending" && (
                    <Button 
                      className="bg-trust hover:bg-trust-dark"
                      onClick={() => {
                        acceptDelivery(selectedOrder);
                        setIsDetailsOpen(false);
                      }}
                    >
                      <Truck className="mr-2 h-4 w-4" />
                      Aceitar Entrega
                    </Button>
                  )}
                  {selectedOrder.status === "in_progress" && (
                    <Button 
                      className="bg-cta hover:bg-cta-dark"
                      onClick={() => {
                        completeDelivery(selectedOrder);
                        setIsDetailsOpen(false);
                      }}
                    >
                      <CheckCircle className="mr-2 h-4 w-4" />
                      Concluir Entrega
                    </Button>
                  )}
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Route Dialog */}
      <Dialog open={isRouteDialogOpen} onOpenChange={setIsRouteDialogOpen}>
        <DialogContent className="sm:max-w-[800px] sm:h-[600px]">
          <DialogHeader>
            <DialogTitle>Rota de Entrega</DialogTitle>
            <DialogDescription>
              Visualize a rota para entrega do pedido #{selectedOrder?.id}.
            </DialogDescription>
          </DialogHeader>
          
          <div className="flex-1 bg-gray-100 rounded-md h-[400px] flex items-center justify-center">
            <div className="text-center">
              <Navigation className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-1">Mapa de Rota</h3>
              <p className="text-muted-foreground max-w-md mx-auto">
                Em uma implementação real, aqui seria exibido um mapa interativo com a rota de entrega, 
                utilizando serviços como Google Maps ou Mapbox.
              </p>
            </div>
          </div>
          
          {selectedOrder && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <div>
                <p className="text-sm text-muted-foreground mb-1">Origem</p>
                <div className="flex items-center">
                  <MapPin className="h-4 w-4 mr-2 text-trust" />
                  <p className="font-medium">{selectedOrder.shopName}</p>
                </div>
              </div>
              <div>
                <p className="text-sm text-muted-foreground mb-1">Destino</p>
                <div className="flex items-start">
                  <MapPin className="h-4 w-4 mr-2 text-cta flex-shrink-0 mt-0.5" />
                  <p className="font-medium">{selectedOrder.deliveryAddress}</p>
                </div>
              </div>
            </div>
          )}
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsRouteDialogOpen(false)}>
              Fechar
            </Button>
            {selectedOrder?.status === "in_progress" && (
              <Button 
                className="bg-cta hover:bg-cta-dark"
                onClick={() => {
                  completeDelivery(selectedOrder);
                  setIsRouteDialogOpen(false);
                }}
              >
                <CheckCircle className="mr-2 h-4 w-4" />
                Concluir Entrega
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
